import{d as T,l as A,I as D,r,f as E,cu as O,o as i,s as _,w as s,i as u,a as e,j as f,H as Q,c as R,F as j,k as q,V as B,G,t as H,c5 as I,y as K,dt as L,Z as P,K as Z,aQ as z,aR as J,N as W,M as X,Q as Y,__tla as $}from"./index-B58vSXOX.js";import{_ as ee,__tla as ae}from"./Dialog.vue_vue_type_style_index_0_lang-DX8U556d.js";import{C as le}from"./constants-D0aoFN3l.js";let v,se=Promise.all([(()=>{try{return $}catch{}})(),(()=>{try{return ae}catch{}})()]).then(async()=>{v=T({name:"SystemDictDataForm",__name:"MessageConfigForm",emits:["success"],setup(te,{expose:b,emit:y}){const{t:V}=A(),g=D(),o=r(!1),k=r("\u4FEE\u6539"),d=r(!1);r("");const t=r({id:void 0,cmd:void 0,name:void 0,status:le.ENABLE,url:void 0}),h=E({remark:[{required:!0,message:"\u901A\u77E5\u58F0\u97F3\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),n=r();O([{value:"default",label:"\u9ED8\u8BA4"},{value:"primary",label:"\u4E3B\u8981"},{value:"success",label:"\u6210\u529F"},{value:"info",label:"\u4FE1\u606F"},{value:"warning",label:"\u8B66\u544A"},{value:"danger",label:"\u5371\u9669"}]),b({open:m=>{t.value=m,o.value=!0}});const w=y,C=async()=>{if(n&&await n.value.validate()){d.value=!0;try{const m=t.value;await L(m),g.success(V("common.updateSuccess")),o.value=!1,w("success")}finally{d.value=!1}}};return(m,l)=>{const U=P,c=Z,F=z,N=J,S=W,p=X,x=ee,M=Y;return i(),_(x,{modelValue:e(o),"onUpdate:modelValue":l[4]||(l[4]=a=>K(o)?o.value=a:null),title:e(k)},{footer:s(()=>[u(p,{disabled:e(d),type:"primary",onClick:C},{default:s(()=>[f("\u786E \u5B9A")]),_:1},8,["disabled"]),u(p,{onClick:l[3]||(l[3]=a=>o.value=!1)},{default:s(()=>[f("\u53D6 \u6D88")]),_:1})]),default:s(()=>[Q((i(),_(S,{ref_key:"formRef",ref:n,model:e(t),rules:e(h),"label-width":"80px"},{default:s(()=>[u(c,{label:"\u6D88\u606F\u7C7B\u578B",prop:"label"},{default:s(()=>[u(U,{modelValue:e(t).name,"onUpdate:modelValue":l[0]||(l[0]=a=>e(t).name=a),placeholder:"\u8BF7\u8F93\u5165\u6570\u636E\u6807\u7B7E",disabled:""},null,8,["modelValue"])]),_:1}),u(c,{label:"\u72B6\u6001",prop:"status"},{default:s(()=>[u(N,{modelValue:e(t).status,"onUpdate:modelValue":l[1]||(l[1]=a=>e(t).status=a)},{default:s(()=>[(i(!0),R(j,null,q(e(B)(e(G).COMMON_STATUS),a=>(i(),_(F,{key:a.value,label:a.value},{default:s(()=>[f(H(a.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),u(c,{label:"\u901A\u77E5\u58F0\u97F3",prop:"url"},{default:s(()=>[u(e(I),{fileType:["mp3"],limit:1,modelValue:e(t).url,"onUpdate:modelValue":l[2]||(l[2]=a=>e(t).url=a)},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[M,e(d)]])]),_:1},8,["modelValue","title"])}}})});export{v as _,se as __tla};
