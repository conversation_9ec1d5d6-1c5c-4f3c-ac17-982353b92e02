import{d as v,a0 as P,r as j,o as u,s as w,a as o,y as x,a1 as L,a5 as y,bY as z,B as O,cT as d,cU as A,cV as C,cW as M,cX as N,p as R,cY as S,b as k,w as g,i as D,c as U,k as Y,F as $,_ as B,j as F,t as W,__tla as X}from"./index-B58vSXOX.js";import{u as T,__tla as q}from"./useIcon-boHg64K-.js";import{b as G,a as H,E as J,__tla as K}from"./el-dropdown-item-Bl0vtQKL.js";let V,E,Q=Promise.all([(()=>{try{return X}catch{}})(),(()=>{try{return q}catch{}})(),(()=>{try{return K}catch{}})()]).then(async()=>{let i,b;i="var(--el-color-black)",V=O(v({name:"ThemeSwitch",__name:"ThemeSwitch",setup(l){const{getPrefixCls:s}=y(),c=s("theme-switch"),a=T({icon:"emojione-monotone:sun",color:"#fde047"}),t=T({icon:"emojione-monotone:crescent-moon",color:"#fde047"}),_=P(),n=j(_.getIsDark),e=r=>{_.setIsDark(r)};return(r,m)=>{const p=z;return u(),w(p,{modelValue:o(n),"onUpdate:modelValue":m[0]||(m[0]=h=>x(n)?n.value=h:null),"active-color":i,"active-icon":o(a),"border-color":i,class:L(o(c)),"inactive-color":i,"inactive-icon":o(t),"inline-prompt":"",onChange:e},null,8,["modelValue","active-icon","class","inactive-icon"])}}}),[["__scopeId","data-v-3b3542c2"]]),b=()=>({changeLocale:async l=>{const s=d.global,c=await A(Object.assign({"../../locales/en.ts":()=>C(()=>import("./en-2_rr8rGj.js"),__vite__mapDeps([])),"../../locales/zh-CN.ts":()=>C(()=>import("./zh-CN-D73L9FSo.js"),__vite__mapDeps([]))}),`../../locales/${l}.ts`);s.setLocaleMessage(l,c.default),(a=>{const t=M();d.mode==="legacy"?d.global.locale=a:d.global.locale.value=a,t.setCurrentLocale({lang:a}),N(a)})(l)}}),E=v({name:"LocaleDropdown",__name:"LocaleDropdown",props:{color:R.string.def("")},setup(l){const{getPrefixCls:s}=y(),c=s("locale-dropdown"),a=S(),t=k(()=>a.getLocaleMap),_=k(()=>a.getCurrentLocale),n=e=>{if(e===o(_).lang)return;window.location.reload(),a.setCurrentLocale({lang:e});const{changeLocale:r}=b();r(e)};return(e,r)=>{const m=B,p=J,h=H,I=G;return u(),w(I,{class:L(o(c)),trigger:"click",onCommand:n},{dropdown:g(()=>[D(h,null,{default:g(()=>[(u(!0),U($,null,Y(o(t),f=>(u(),w(p,{key:f.lang,command:f.lang},{default:g(()=>[F(W(f.name),1)]),_:2},1032,["command"]))),128))]),_:1})]),default:g(()=>[D(m,{class:L([e.$attrs.class,"cursor-pointer !p-0"]),color:l.color,size:18,icon:"ion:language-sharp"},null,8,["class","color"])]),_:1},8,["class"])}}})});export{V as T,E as _,Q as __tla};
function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = []
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
