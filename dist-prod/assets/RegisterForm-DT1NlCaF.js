import{_ as I,__tla as V}from"./Form-D1HGicRo.js";import{_ as x,__tla as F}from"./XButton-CHDcijwl.js";import{d as L,l as U,b as q,a,f as B,r as C,H as E,a9 as S,o as j,s as z,w as c,i as s,g as i,Z as G,__tla as H}from"./index-B58vSXOX.js";import{u as T,__tla as Z}from"./useForm-D49T6Htj.js";import{u as A,__tla as D}from"./useValidator-DilgrSrY.js";import{u as J,L as K,_ as M,__tla as N}from"./LoginFormTitle.vue_vue_type_script_setup_true_lang-BFl3ShCw.js";import{__tla as O}from"./Form.vue_vue_type_style_index_0_scoped_09f6ff61_lang-DKLtuoM2.js";import{__tla as Q}from"./el-virtual-list-Og_QQeVM.js";import{__tla as W}from"./el-tree-select-Dq6GzaOt.js";import{__tla as X}from"./InputPassword-CkWRm87N.js";let u,Y=Promise.all([(()=>{try{return V}catch{}})(),(()=>{try{return F}catch{}})(),(()=>{try{return H}catch{}})(),(()=>{try{return Z}catch{}})(),(()=>{try{return D}catch{}})(),(()=>{try{return N}catch{}})(),(()=>{try{return O}catch{}})(),(()=>{try{return Q}catch{}})(),(()=>{try{return W}catch{}})(),(()=>{try{return X}catch{}})()]).then(async()=>{let _,d,p;_={class:"w-[100%] flex"},d={class:"w-[100%]"},p={class:"mt-15px w-[100%]"},u=L({name:"RegisterForm",__name:"RegisterForm",setup($){const{t:e}=U(),{required:t}=A(),{register:h,elFormRef:g}=T(),{handleBackLogin:f,getLoginState:y}=J(),w=q(()=>a(y)===K.REGISTER),P=B([{field:"title",colProps:{span:24}},{field:"username",label:e("login.username"),value:"",component:"Input",colProps:{span:24},componentProps:{placeholder:e("login.usernamePlaceholder")}},{field:"password",label:e("login.password"),value:"",component:"InputPassword",colProps:{span:24},componentProps:{style:{width:"100%"},strength:!0,placeholder:e("login.passwordPlaceholder")}},{field:"check_password",label:e("login.checkPassword"),value:"",component:"InputPassword",colProps:{span:24},componentProps:{style:{width:"100%"},strength:!0,placeholder:e("login.passwordPlaceholder")}},{field:"code",label:e("login.code"),colProps:{span:24}},{field:"register",colProps:{span:24}}]),b={username:[t()],password:[t()],check_password:[t()],code:[t()]},n=C(!1);return(ee,l)=>{const v=G,m=x,k=I;return E((j(),z(k,{rules:b,schema:a(P),class:"dark:(border-1 border-[var(--el-border-color)] border-solid)","hide-required-asterisk":"","label-position":"top",size:"large",onRegister:a(h)},{title:c(()=>[s(M,{style:{width:"100%"}})]),code:c(o=>[i("div",_,[s(v,{modelValue:o.code,"onUpdate:modelValue":r=>o.code=r,placeholder:a(e)("login.codePlaceholder")},null,8,["modelValue","onUpdate:modelValue","placeholder"])])]),register:c(()=>[i("div",d,[s(m,{loading:a(n),title:a(e)("login.register"),class:"w-[100%]",type:"primary",onClick:l[0]||(l[0]=o=>(async()=>{const r=a(g);r==null||r.validate(async R=>{if(R)try{n.value=!0}finally{n.value=!1}})})())},null,8,["loading","title"])]),i("div",p,[s(m,{title:a(e)("login.hasUser"),class:"w-[100%]",onClick:l[1]||(l[1]=o=>a(f)())},null,8,["title"])])]),_:1},8,["schema","onRegister"])),[[S,a(w)]])}}})});export{Y as __tla,u as default};
