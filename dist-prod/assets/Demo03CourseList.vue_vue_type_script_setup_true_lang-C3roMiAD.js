import{d as D,l as F,I as L,r as _,f as M,an as O,T as Q,o as i,c as R,i as a,w as n,H as c,s as d,j as h,a as r,F as q,_ as A,M as B,O as E,P as G,Q as J,__tla as K}from"./index-B58vSXOX.js";import{_ as V,__tla as W}from"./ContentWrap.vue_vue_type_script_setup_true_lang-1EA99Qaa.js";import{_ as X,__tla as Y}from"./index.vue_vue_type_script_setup_true_lang-Bafh4YVk.js";import{d as Z,__tla as $}from"./formatTime-CjAREQvd.js";import{e as aa,f as ta,__tla as ea}from"./index-UuY36Rjm.js";import{_ as ra,__tla as la}from"./Demo03CourseForm.vue_vue_type_script_setup_true_lang-BubXvVR9.js";let I,sa=Promise.all([(()=>{try{return K}catch{}})(),(()=>{try{return W}catch{}})(),(()=>{try{return Y}catch{}})(),(()=>{try{return $}catch{}})(),(()=>{try{return ea}catch{}})(),(()=>{try{return la}catch{}})()]).then(async()=>{I=D({__name:"Demo03CourseList",props:{studentId:{}},setup(S){const{t:b}=F(),u=L(),m=S,f=_(!1),w=_([]),k=_(0),l=M({pageNo:1,pageSize:10,studentId:void 0});O(()=>m.studentId,t=>{t&&(l.studentId=t,x())},{immediate:!0,deep:!0});const p=async()=>{f.value=!0;try{const t=await aa(l);w.value=t.list,k.value=t.total}finally{f.value=!1}},x=()=>{l.pageNo=1,p()},v=_(),C=(t,e)=>{m.studentId?v.value.open(t,e,m.studentId):u.error("\u8BF7\u9009\u62E9\u4E00\u4E2A\u5B66\u751F")};return(t,e)=>{const z=A,y=B,o=E,N=G,P=X,H=V,g=Q("hasPermi"),T=J;return i(),R(q,null,[a(H,null,{default:n(()=>[c((i(),d(y,{plain:"",type:"primary",onClick:e[0]||(e[0]=s=>C("create"))},{default:n(()=>[a(z,{class:"mr-5px",icon:"ep:plus"}),h(" \u65B0\u589E ")]),_:1})),[[g,["infra:demo03-student:create"]]]),c((i(),d(N,{data:r(w),"show-overflow-tooltip":!0,stripe:!0},{default:n(()=>[a(o,{align:"center",label:"\u7F16\u53F7",prop:"id"}),a(o,{align:"center",label:"\u540D\u5B57",prop:"name"}),a(o,{align:"center",label:"\u5206\u6570",prop:"score"}),a(o,{formatter:r(Z),align:"center",label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime",width:"180px"},null,8,["formatter"]),a(o,{align:"center",label:"\u64CD\u4F5C"},{default:n(s=>[c((i(),d(y,{link:"",type:"primary",onClick:U=>C("update",s.row.id)},{default:n(()=>[h(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[g,["infra:demo03-student:update"]]]),c((i(),d(y,{link:"",type:"danger",onClick:U=>(async j=>{try{await u.delConfirm(),await ta(j),u.success(b("common.delSuccess")),await p()}catch{}})(s.row.id)},{default:n(()=>[h(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[g,["infra:demo03-student:delete"]]])]),_:1})]),_:1},8,["data"])),[[T,r(f)]]),a(P,{limit:r(l).pageSize,"onUpdate:limit":e[1]||(e[1]=s=>r(l).pageSize=s),page:r(l).pageNo,"onUpdate:page":e[2]||(e[2]=s=>r(l).pageNo=s),total:r(k),onPagination:p},null,8,["limit","page","total"])]),_:1}),a(ra,{ref_key:"formRef",ref:v,onSuccess:p},null,512)],64)}}})});export{I as _,sa as __tla};
