import{d as F,l as b,I as g,r as c,o as x,s as A,w as u,i as m,a as l,j as v,H as C,y as R,M as j,Q as E,__tla as H}from"./index-B58vSXOX.js";import{_ as I,__tla as P}from"./Dialog.vue_vue_type_style_index_0_lang-DX8U556d.js";import{_ as Q,__tla as U}from"./Form-D1HGicRo.js";import{g as q,c as z,a as B,__tla as D}from"./useCrudSchemas-CJS0JPfe.js";import{r as G,a as J,__tla as K}from"./account.data-DzR5bDHV.js";let p,L=Promise.all([(()=>{try{return H}catch{}})(),(()=>{try{return P}catch{}})(),(()=>{try{return U}catch{}})(),(()=>{try{return D}catch{}})(),(()=>{try{return K}catch{}})()]).then(async()=>{p=F({name:"SystemMailAccountForm",__name:"MailAccountForm",emits:["success"],setup(N,{expose:h,emit:w}){const{t:o}=b(),n=g(),e=c(!1),i=c(""),t=c(!1),f=c(""),r=c();h({open:async(s,a)=>{if(e.value=!0,i.value=o("action."+s),f.value=s,a){t.value=!0;try{const _=await q(a);r.value.setValues(_)}finally{t.value=!1}}}});const M=w,S=async()=>{if(r&&await r.value.getElFormRef().validate()){t.value=!0;try{const s=r.value.formModel;f.value==="create"?(await z(s),n.success(o("common.createSuccess"))):(await B(s),n.success(o("common.updateSuccess"))),e.value=!1,M("success")}finally{t.value=!1}}};return(s,a)=>{const _=Q,y=j,V=I,k=E;return x(),A(V,{modelValue:l(e),"onUpdate:modelValue":a[1]||(a[1]=d=>R(e)?e.value=d:null),title:l(i)},{footer:u(()=>[m(y,{disabled:l(t),type:"primary",onClick:S},{default:u(()=>[v("\u786E \u5B9A")]),_:1},8,["disabled"]),m(y,{onClick:a[0]||(a[0]=d=>e.value=!1)},{default:u(()=>[v("\u53D6 \u6D88")]),_:1})]),default:u(()=>[C(m(_,{ref_key:"formRef",ref:r,rules:l(G),schema:l(J).formSchema},null,8,["rules","schema"]),[[k,l(t)]])]),_:1},8,["modelValue","title"])}}})});export{p as _,L as __tla};
