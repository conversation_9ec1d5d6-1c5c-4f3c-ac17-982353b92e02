import{d as C,l as S,I as H,r as n,o as i,s as c,w as t,i as d,a as l,j as y,H as J,c as K,F as M,k as D,y as N,Z as P,K as Q,R as Z,J as q,N as z,M as B,Q as E,__tla as G}from"./index-B58vSXOX.js";import{_ as L,__tla as O}from"./Dialog.vue_vue_type_style_index_0_lang-DX8U556d.js";import{c as T,d as W,__tla as X}from"./index-B07jKjqk.js";import{b as Y,__tla as $}from"./index-DwdBPw3i.js";let V,ee=Promise.all([(()=>{try{return G}catch{}})(),(()=>{try{return O}catch{}})(),(()=>{try{return X}catch{}})(),(()=>{try{return $}catch{}})()]).then(async()=>{V=C({name:"SystemUserAssignRoleForm",__name:"UserAssignRoleForm",emits:["success"],setup(ae,{expose:b,emit:k}){const{t:I}=S(),h=H(),r=n(!1),o=n(!1),e=n({id:-1,nickname:"",username:"",roleIds:[]}),m=n(),f=n([]);b({open:async u=>{r.value=!0,R(),e.value.id=u.id,e.value.username=u.username,e.value.nickname=u.nickname,o.value=!0;try{e.value.roleIds=await T(u.id)}finally{o.value=!1}f.value=await Y()}});const w=k,U=async()=>{if(m&&await m.value.validate()){o.value=!0;try{await W({userId:e.value.id,roleIds:e.value.roleIds}),h.success(I("common.updateSuccess")),r.value=!1,w("success",!0)}finally{o.value=!1}}},R=()=>{var u;e.value={id:-1,nickname:"",username:"",roleIds:[]},(u=m.value)==null||u.resetFields()};return(u,s)=>{const v=P,_=Q,F=Z,g=q,x=z,p=B,j=L,A=E;return i(),c(j,{modelValue:l(r),"onUpdate:modelValue":s[4]||(s[4]=a=>N(r)?r.value=a:null),title:"\u5206\u914D\u89D2\u8272"},{footer:t(()=>[d(p,{disabled:l(o),type:"primary",onClick:U},{default:t(()=>[y("\u786E \u5B9A")]),_:1},8,["disabled"]),d(p,{onClick:s[3]||(s[3]=a=>r.value=!1)},{default:t(()=>[y("\u53D6 \u6D88")]),_:1})]),default:t(()=>[J((i(),c(x,{ref_key:"formRef",ref:m,model:l(e),"label-width":"80px"},{default:t(()=>[d(_,{label:"\u7528\u6237\u540D\u79F0"},{default:t(()=>[d(v,{modelValue:l(e).username,"onUpdate:modelValue":s[0]||(s[0]=a=>l(e).username=a),disabled:!0},null,8,["modelValue"])]),_:1}),d(_,{label:"\u7528\u6237\u6635\u79F0"},{default:t(()=>[d(v,{modelValue:l(e).nickname,"onUpdate:modelValue":s[1]||(s[1]=a=>l(e).nickname=a),disabled:!0},null,8,["modelValue"])]),_:1}),d(_,{label:"\u89D2\u8272"},{default:t(()=>[d(g,{modelValue:l(e).roleIds,"onUpdate:modelValue":s[2]||(s[2]=a=>l(e).roleIds=a),multiple:"",placeholder:"\u8BF7\u9009\u62E9\u89D2\u8272"},{default:t(()=>[(i(!0),K(M,null,D(l(f),a=>(i(),c(F,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])),[[A,l(o)]])]),_:1},8,["modelValue"])}}})});export{V as _,ee as __tla};
