import{f as M,G as H,d as V,l as Y,I as j,r as c,o as A,s as I,w as u,i as f,a as o,j as T,H as E,y as O,M as R,Q as U,__tla as G}from"./index-B58vSXOX.js";import{_ as N,__tla as Q}from"./Dialog.vue_vue_type_style_index_0_lang-DX8U556d.js";import{_ as q,__tla as z}from"./Form-D1HGicRo.js";import{g as B,c as J,u as K,__tla as L}from"./index-cO5MyIhE.js";import{d as W,__tla as X}from"./formatTime-CjAREQvd.js";import{e as Z,u as $,__tla as ee}from"./useCrudSchemas-CJS0JPfe.js";import{r as a,__tla as ae}from"./formRules-D5gHmcW6.js";let k,p,te=Promise.all([(()=>{try{return G}catch{}})(),(()=>{try{return Q}catch{}})(),(()=>{try{return z}catch{}})(),(()=>{try{return L}catch{}})(),(()=>{try{return X}catch{}})(),(()=>{try{return ee}catch{}})(),(()=>{try{return ae}catch{}})()]).then(async()=>{let m,h,y;m=await Z(),h=M({name:[a],code:[a],accountId:[a],label:[a],content:[a],params:[a],status:[a]}),y=M([{label:"\u6A21\u677F\u7F16\u7801",field:"code",isSearch:!0},{label:"\u6A21\u677F\u540D\u79F0",field:"name",isSearch:!0},{label:"\u6A21\u677F\u6807\u9898",field:"title"},{label:"\u6A21\u677F\u5185\u5BB9",field:"content",form:{component:"Editor",componentProps:{valueHtml:"",height:200}}},{label:"\u90AE\u7BB1\u8D26\u53F7",field:"accountId",width:"200px",formatter:(P,v,_)=>{var t;return(t=m.find(n=>n.id===_))==null?void 0:t.mail},search:{show:!0,component:"Select",api:()=>m,componentProps:{optionsAlias:{labelField:"mail",valueField:"id"}}},form:{component:"Select",api:()=>m,componentProps:{optionsAlias:{labelField:"mail",valueField:"id"}}}},{label:"\u53D1\u9001\u4EBA\u540D\u79F0",field:"nickname"},{label:"\u5F00\u542F\u72B6\u6001",field:"status",isSearch:!0,dictType:H.COMMON_STATUS,dictClass:"number"},{label:"\u5907\u6CE8",field:"remark",isTable:!1},{label:"\u521B\u5EFA\u65F6\u95F4",field:"createTime",isForm:!1,formatter:W,search:{show:!0,component:"DatePicker",componentProps:{valueFormat:"YYYY-MM-DD HH:mm:ss",type:"daterange",defaultTime:[new Date("1 00:00:00"),new Date("1 23:59:59")]}}},{label:"\u64CD\u4F5C",field:"action",isForm:!1}]),{allSchemas:p}=$(y),k=V({name:"SystemMailTemplateForm",__name:"MailTemplateForm",emits:["success"],setup(P,{expose:v,emit:_}){const{t}=Y(),n=j(),l=c(!1),b=c(""),s=c(!1),w=c(""),i=c();v({open:async(r,e)=>{if(l.value=!0,b.value=t("action."+r),w.value=r,e){s.value=!0;try{const d=await B(e);i.value.setValues(d)}finally{s.value=!1}}}});const g=_,x=async()=>{if(i&&await i.value.getElFormRef().validate()){s.value=!0;try{const r=i.value.formModel;w.value==="create"?(await J(r),n.success(t("common.createSuccess"))):(await K(r),n.success(t("common.updateSuccess"))),l.value=!1,g("success")}finally{s.value=!1}}};return(r,e)=>{const d=q,S=R,C=N,D=U;return A(),I(C,{modelValue:o(l),"onUpdate:modelValue":e[1]||(e[1]=F=>O(l)?l.value=F:null),"max-height":500,scroll:!0,title:o(b),width:800},{footer:u(()=>[f(S,{disabled:o(s),type:"primary",onClick:x},{default:u(()=>[T("\u786E \u5B9A")]),_:1},8,["disabled"]),f(S,{onClick:e[0]||(e[0]=F=>l.value=!1)},{default:u(()=>[T("\u53D6 \u6D88")]),_:1})]),default:u(()=>[E(f(d,{ref_key:"formRef",ref:i,rules:o(h),schema:o(p).formSchema},null,8,["rules","schema"]),[[D,o(s)]])]),_:1},8,["modelValue","title"])}}})});export{k as _,te as __tla,p as a};
