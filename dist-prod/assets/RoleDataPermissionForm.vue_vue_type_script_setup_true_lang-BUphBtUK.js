import{d as G,l as H,I as J,r as u,f as Q,o as v,s as h,w as o,i as s,a,j as n,H as q,t as C,c as z,F as B,k as L,V as W,G as X,y as S,aa as Z,aE as $,bK as ee,K as ae,R as le,J as te,N as de,bY as oe,b5 as se,M as ue,Q as ce,__tla as ne}from"./index-B58vSXOX.js";import{_ as re,__tla as ie}from"./Dialog.vue_vue_type_style_index_0_lang-DX8U556d.js";import{d as me,h as pe}from"./tree-Cts6wVCK.js";import{c as D}from"./constants-D0aoFN3l.js";import{a as _e,__tla as ve}from"./index-M2TjMRx4.js";import{b as fe,__tla as ye}from"./index-B07jKjqk.js";import{<PERSON> as he,__tla as Se}from"./index-DurBFu_Z.js";let w,Ve=Promise.all([(()=>{try{return ne}catch{}})(),(()=>{try{return ie}catch{}})(),(()=>{try{return ve}catch{}})(),(()=>{try{return ye}catch{}})(),(()=>{try{return Se}catch{}})()]).then(async()=>{w=G({name:"SystemRoleDataPermissionForm",__name:"RoleDataPermissionForm",emits:["success"],setup(be,{expose:E,emit:P}){const{t:U}=H(),I=J(),r=u(!1),f=u(!1),d=Q({id:void 0,name:"",code:"",dataScope:void 0,dataScopeDeptIds:[]}),k=u(),V=u([]),i=u(!0),m=u(),p=u(!1),_=u(!0);E({open:async l=>{var e;r.value=!0,M(),V.value=pe(await _e()),d.id=l.id,d.name=l.name,d.code=l.code,d.dataScope=l.dataScope,await $(),(e=l.dataScopeDeptIds)==null||e.forEach(c=>{m.value.setChecked(c,!0,!1)})}});const R=P,T=async()=>{f.value=!0;try{const l={roleId:d.id,dataScope:d.dataScope,dataScopeDeptIds:d.dataScope!==D.DEPT_CUSTOM?[]:m.value.getCheckedKeys(!1)};await fe(l),I.success(U("common.updateSuccess")),r.value=!1,R("success")}finally{f.value=!1}},M=()=>{var l,e;p.value=!1,i.value=!0,_.value=!0,d.value={id:void 0,name:"",code:"",dataScope:void 0,dataScopeDeptIds:[]},(l=m.value)==null||l.setCheckedNodes([]),(e=k.value)==null||e.resetFields()},g=()=>{var e;const l=(e=m.value)==null?void 0:e.store.nodesMap;for(let c in l)l[c].expanded!==i.value&&(l[c].expanded=i.value)};return(l,e)=>{const c=ee,y=ae,F=le,K=te,O=de,b=oe,A=se,N=he,x=ue,Y=re,j=ce;return v(),h(Y,{modelValue:a(r),"onUpdate:modelValue":e[6]||(e[6]=t=>S(r)?r.value=t:null),title:"\u83DC\u5355\u6743\u9650",width:"800"},{footer:o(()=>[s(x,{disabled:a(f),type:"primary",onClick:T},{default:o(()=>[n("\u786E \u5B9A")]),_:1},8,["disabled"]),s(x,{onClick:e[5]||(e[5]=t=>r.value=!1)},{default:o(()=>[n("\u53D6 \u6D88")]),_:1})]),default:o(()=>[q((v(),h(O,{ref_key:"formRef",ref:k,model:a(d),"label-width":"80px"},{default:o(()=>[s(y,{label:"\u89D2\u8272\u540D\u79F0"},{default:o(()=>[s(c,null,{default:o(()=>[n(C(a(d).name),1)]),_:1})]),_:1}),s(y,{label:"\u89D2\u8272\u6807\u8BC6"},{default:o(()=>[s(c,null,{default:o(()=>[n(C(a(d).code),1)]),_:1})]),_:1}),s(y,{label:"\u6743\u9650\u8303\u56F4"},{default:o(()=>[s(K,{modelValue:a(d).dataScope,"onUpdate:modelValue":e[0]||(e[0]=t=>a(d).dataScope=t)},{default:o(()=>[(v(!0),z(B,null,L(a(W)(a(X).SYSTEM_DATA_SCOPE),t=>(v(),h(F,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])),[[j,a(f)]]),a(d).dataScope===a(D).DEPT_CUSTOM?(v(),h(y,{key:0,label:"\u6743\u9650\u8303\u56F4",style:{display:"flex"}},{default:o(()=>[s(N,{class:"card",shadow:"never"},{header:o(()=>[n(" \u5168\u9009/\u5168\u4E0D\u9009: "),s(b,{modelValue:a(p),"onUpdate:modelValue":e[1]||(e[1]=t=>S(p)?p.value=t:null),"active-text":"\u662F","inactive-text":"\u5426","inline-prompt":"",onChange:e[2]||(e[2]=t=>{m.value.setCheckedNodes(p.value?V.value:[])})},null,8,["modelValue"]),n(" \u5168\u90E8\u5C55\u5F00/\u6298\u53E0: "),s(b,{modelValue:a(i),"onUpdate:modelValue":e[3]||(e[3]=t=>S(i)?i.value=t:null),"active-text":"\u5C55\u5F00","inactive-text":"\u6298\u53E0","inline-prompt":"",onChange:g},null,8,["modelValue"]),n(" \u7236\u5B50\u8054\u52A8(\u9009\u4E2D\u7236\u8282\u70B9\uFF0C\u81EA\u52A8\u9009\u62E9\u5B50\u8282\u70B9): "),s(b,{modelValue:a(_),"onUpdate:modelValue":e[4]||(e[4]=t=>S(_)?_.value=t:null),"active-text":"\u662F","inactive-text":"\u5426","inline-prompt":""},null,8,["modelValue"])]),default:o(()=>[s(A,{ref_key:"treeRef",ref:m,"check-strictly":!a(_),data:a(V),props:a(me),"default-expand-all":"","empty-text":"\u52A0\u8F7D\u4E2D\uFF0C\u8BF7\u7A0D\u540E","node-key":"id","show-checkbox":""},null,8,["check-strictly","data","props"])]),_:1})]),_:1})):Z("",!0)]),_:1},8,["modelValue"])}}})});export{w as _,Ve as __tla};
