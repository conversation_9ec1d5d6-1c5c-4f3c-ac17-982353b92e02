import{d as f,l as y,I as h,r as s,C as g,o as _,s as o,w as n,H as v,a as e,i as t,O as w,P as b,Q as I,__tla as x}from"./index-B58vSXOX.js";import{_ as C,__tla as P}from"./ContentWrap.vue_vue_type_script_setup_true_lang-1EA99Qaa.js";import{d as D,__tla as H}from"./formatTime-CjAREQvd.js";import{a as L,__tla as O}from"./index-C3hYB9KS.js";let p,Q=Promise.all([(()=>{try{return x}catch{}})(),(()=>{try{return P}catch{}})(),(()=>{try{return H}catch{}})(),(()=>{try{return O}catch{}})()]).then(async()=>{p=f({__name:"Demo03CourseList",props:{studentId:{}},setup(c){y(),h();const i=c,r=s(!1),l=s([]);return g(()=>{(async()=>{r.value=!0;try{l.value=await L(i.studentId)}finally{r.value=!1}})()}),(T,j)=>{const a=w,u=b,m=C,d=I;return _(),o(m,null,{default:n(()=>[v((_(),o(u,{data:e(l),stripe:!0,"show-overflow-tooltip":!0},{default:n(()=>[t(a,{label:"\u7F16\u53F7",align:"center",prop:"id"}),t(a,{label:"\u540D\u5B57",align:"center",prop:"name"}),t(a,{label:"\u5206\u6570",align:"center",prop:"score"}),t(a,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:e(D),width:"180px"},null,8,["formatter"])]),_:1},8,["data"])),[[d,e(r)]])]),_:1})}}})});export{p as _,Q as __tla};
