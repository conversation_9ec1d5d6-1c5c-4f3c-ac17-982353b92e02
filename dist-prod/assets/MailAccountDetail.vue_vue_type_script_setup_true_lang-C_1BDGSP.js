import{_ as i,__tla as d}from"./Dialog.vue_vue_type_style_index_0_lang-DX8U556d.js";import{_ as p,__tla as y}from"./Descriptions-BZui0vo4.js";import{g as h,__tla as f}from"./useCrudSchemas-CJS0JPfe.js";import{a as v,__tla as V}from"./account.data-DzR5bDHV.js";import{d as w,r as t,o as x,s as A,w as D,i as M,a as e,y as S,__tla as g}from"./index-B58vSXOX.js";let o,P=Promise.all([(()=>{try{return d}catch{}})(),(()=>{try{return y}catch{}})(),(()=>{try{return f}catch{}})(),(()=>{try{return V}catch{}})(),(()=>{try{return g}catch{}})()]).then(async()=>{o=w({name:"SystemMailAccountDetail",__name:"MailAccountDetail",setup(U,{expose:c}){const a=t(!1),l=t(!1),r=t();return c({open:async _=>{a.value=!0,l.value=!0;try{r.value=await h(_)}finally{l.value=!1}}}),(_,s)=>{const n=p,u=i;return x(),A(u,{modelValue:e(a),"onUpdate:modelValue":s[0]||(s[0]=m=>S(a)?a.value=m:null),title:"\u8BE6\u60C5"},{default:D(()=>[M(n,{data:e(r),schema:e(v).detailSchema},null,8,["data","schema"])]),_:1},8,["modelValue"])}}})});export{o as _,P as __tla};
