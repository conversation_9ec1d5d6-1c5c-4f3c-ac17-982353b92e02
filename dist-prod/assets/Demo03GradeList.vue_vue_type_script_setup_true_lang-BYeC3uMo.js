import{d as F,l as G,I as H,r as _,f as L,an as M,T as O,o as i,c as Q,i as a,w as n,H as c,s as d,j as h,a as r,F as R,_ as q,M as A,O as B,P as E,Q as J,__tla as K}from"./index-B58vSXOX.js";import{_ as V,__tla as W}from"./ContentWrap.vue_vue_type_script_setup_true_lang-1EA99Qaa.js";import{_ as X,__tla as Y}from"./index.vue_vue_type_script_setup_true_lang-Bafh4YVk.js";import{d as Z,__tla as $}from"./formatTime-CjAREQvd.js";import{k as aa,l as ta,__tla as ea}from"./index-UuY36Rjm.js";import{_ as ra,__tla as la}from"./Demo03GradeForm.vue_vue_type_script_setup_true_lang-tfpZnJXf.js";let C,sa=Promise.all([(()=>{try{return K}catch{}})(),(()=>{try{return W}catch{}})(),(()=>{try{return Y}catch{}})(),(()=>{try{return $}catch{}})(),(()=>{try{return ea}catch{}})(),(()=>{try{return la}catch{}})()]).then(async()=>{C=F({__name:"Demo03GradeList",props:{studentId:{}},setup(S){const{t:b}=G(),u=H(),m=S,f=_(!1),w=_([]),k=_(0),l=L({pageNo:1,pageSize:10,studentId:void 0});M(()=>m.studentId,t=>{t&&(l.studentId=t,z())},{immediate:!0,deep:!0});const p=async()=>{f.value=!0;try{const t=await aa(l);w.value=t.list,k.value=t.total}finally{f.value=!1}},z=()=>{l.pageNo=1,p()},v=_(),I=(t,e)=>{m.studentId?v.value.open(t,e,m.studentId):u.error("\u8BF7\u9009\u62E9\u4E00\u4E2A\u5B66\u751F")};return(t,e)=>{const N=q,y=A,o=B,P=E,x=X,U=V,g=O("hasPermi"),T=J;return i(),Q(R,null,[a(U,null,{default:n(()=>[c((i(),d(y,{plain:"",type:"primary",onClick:e[0]||(e[0]=s=>I("create"))},{default:n(()=>[a(N,{class:"mr-5px",icon:"ep:plus"}),h(" \u65B0\u589E ")]),_:1})),[[g,["infra:demo03-student:create"]]]),c((i(),d(P,{data:r(w),"show-overflow-tooltip":!0,stripe:!0},{default:n(()=>[a(o,{align:"center",label:"\u7F16\u53F7",prop:"id"}),a(o,{align:"center",label:"\u540D\u5B57",prop:"name"}),a(o,{align:"center",label:"\u73ED\u4E3B\u4EFB",prop:"teacher"}),a(o,{formatter:r(Z),align:"center",label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime",width:"180px"},null,8,["formatter"]),a(o,{align:"center",label:"\u64CD\u4F5C"},{default:n(s=>[c((i(),d(y,{link:"",type:"primary",onClick:j=>I("update",s.row.id)},{default:n(()=>[h(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[g,["infra:demo03-student:update"]]]),c((i(),d(y,{link:"",type:"danger",onClick:j=>(async D=>{try{await u.delConfirm(),await ta(D),u.success(b("common.delSuccess")),await p()}catch{}})(s.row.id)},{default:n(()=>[h(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[g,["infra:demo03-student:delete"]]])]),_:1})]),_:1},8,["data"])),[[T,r(f)]]),a(x,{limit:r(l).pageSize,"onUpdate:limit":e[1]||(e[1]=s=>r(l).pageSize=s),page:r(l).pageNo,"onUpdate:page":e[2]||(e[2]=s=>r(l).pageNo=s),total:r(k),onPagination:p},null,8,["limit","page","total"])]),_:1}),a(ra,{ref_key:"formRef",ref:v,onSuccess:p},null,512)],64)}}})});export{C as _,sa as __tla};
