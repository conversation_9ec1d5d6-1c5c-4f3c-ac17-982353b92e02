import{aN as o,d as R,l as j,I as A,r as v,f as G,o as c,s as N,w as n,i as u,a as e,j as b,H,c as P,F as J,k as K,V as Q,G as Z,y as z,Z as B,K as E,R as L,J as W,N as X,M as Y,Q as $,__tla as ee}from"./index-B58vSXOX.js";import{_ as ae,__tla as te}from"./Dialog.vue_vue_type_style_index_0_lang-DX8U556d.js";let i,V,le=Promise.all([(()=>{try{return ee}catch{}})(),(()=>{try{return te}catch{}})()]).then(async()=>{i={getTenantServerNamePage:async t=>await o.get({url:"/system/tenant-server-name/page",params:t}),getTenantServerName:async t=>await o.get({url:"/system/tenant-server-name/get?id="+t}),createTenantServerName:async t=>await o.post({url:"/system/tenant-server-name/create",data:t}),updateTenantServerName:async t=>await o.put({url:"/system/tenant-server-name/update",data:t}),deleteTenantServerName:async t=>await o.delete({url:"/system/tenant-server-name/delete?id="+t}),exportTenantServerName:async t=>await o.download({url:"/system/tenant-server-name/export-excel",params:t})},V=R({name:"TenantServerNameForm",__name:"TenantServerNameForm",emits:["success"],setup(t,{expose:I,emit:h}){const{t:p}=j(),f=A(),d=v(!1),g=v(""),m=v(!1),S=v(""),s=v({id:void 0,tenId:void 0,serverName:void 0,status:void 0}),x=G({tenId:[{required:!0,message:"\u79DF\u6237id\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],serverName:[{required:!0,message:"\u57DF\u540D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],status:[{required:!0,message:"\u5F00\u542F\u72B6\u6001\uFF080\u6B63\u5E38 1\u505C\u7528\uFF09\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),y=v();I({open:async(r,a)=>{if(d.value=!0,g.value=p("action."+r),S.value=r,F(),a){m.value=!0;try{s.value=await i.getTenantServerName(a)}finally{m.value=!1}}}});const k=h,U=async()=>{await y.value.validate(),m.value=!0;try{const r=s.value;S.value==="create"?(await i.createTenantServerName(r),f.success(p("common.createSuccess"))):(await i.updateTenantServerName(r),f.success(p("common.updateSuccess"))),d.value=!1,k("success")}finally{m.value=!1}},F=()=>{var r;s.value={id:void 0,tenId:void 0,serverName:void 0,status:void 0},(r=y.value)==null||r.resetFields()};return(r,a)=>{const w=B,_=E,q=L,C=W,M=X,T=Y,D=ae,O=$;return c(),N(D,{title:e(g),modelValue:e(d),"onUpdate:modelValue":a[4]||(a[4]=l=>z(d)?d.value=l:null)},{footer:n(()=>[u(T,{onClick:U,type:"primary",disabled:e(m)},{default:n(()=>[b("\u786E \u5B9A")]),_:1},8,["disabled"]),u(T,{onClick:a[3]||(a[3]=l=>d.value=!1)},{default:n(()=>[b("\u53D6 \u6D88")]),_:1})]),default:n(()=>[H((c(),N(M,{ref_key:"formRef",ref:y,model:e(s),rules:e(x),"label-width":"100px"},{default:n(()=>[u(_,{label:"\u79DF\u6237 ID",prop:"tenId"},{default:n(()=>[u(w,{modelValue:e(s).tenId,"onUpdate:modelValue":a[0]||(a[0]=l=>e(s).tenId=l),placeholder:"\u8BF7\u8F93\u5165\u79DF\u6237 ID"},null,8,["modelValue"])]),_:1}),u(_,{label:"\u57DF\u540D",prop:"serverName"},{default:n(()=>[u(w,{modelValue:e(s).serverName,"onUpdate:modelValue":a[1]||(a[1]=l=>e(s).serverName=l),placeholder:"\u8BF7\u8F93\u5165\u57DF\u540D"},null,8,["modelValue"])]),_:1}),u(_,{label:"\u72B6\u6001",prop:"status"},{default:n(()=>[u(C,{modelValue:e(s).status,"onUpdate:modelValue":a[2]||(a[2]=l=>e(s).status=l),placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001",clearable:"",class:"!w-240px"},{default:n(()=>[(c(!0),P(J,null,K(e(Q)(e(Z).COMMON_STATUS),l=>(c(),N(q,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[O,e(m)]])]),_:1},8,["title","modelValue"])}}})});export{i as T,V as _,le as __tla};
