import{d as b,r as u,f as y,an as V,H as I,a as l,o as q,s as w,w as o,i as r,Z as x,K as D,N as U,Q as k,__tla as F}from"./index-B58vSXOX.js";import{b as G,__tla as H}from"./index-DbaD-n_S.js";let _,K=Promise.all([(()=>{try{return F}catch{}})(),(()=>{try{return H}catch{}})()]).then(async()=>{_=b({__name:"Demo03GradeForm",props:{studentId:{}},setup(c,{expose:p}){const f=c,t=u(!1),e=u([]),v=y({studentId:[{required:!0,message:"\u5B66\u751F\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],name:[{required:!0,message:"\u540D\u5B57\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],teacher:[{required:!0,message:"\u73ED\u4E3B\u4EFB\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),m=u();return V(()=>f.studentId,async s=>{if(e.value={id:void 0,studentId:void 0,name:void 0,teacher:void 0},s)try{t.value=!0;const a=await G(s);if(!a)return;e.value=a}finally{t.value=!1}},{immediate:!0}),p({validate:()=>m.value.validate(),getData:()=>e.value}),(s,a)=>{const n=x,i=D,h=U,g=k;return I((q(),w(h,{ref_key:"formRef",ref:m,model:l(e),rules:l(v),"label-width":"100px"},{default:o(()=>[r(i,{label:"\u540D\u5B57",prop:"name"},{default:o(()=>[r(n,{modelValue:l(e).name,"onUpdate:modelValue":a[0]||(a[0]=d=>l(e).name=d),placeholder:"\u8BF7\u8F93\u5165\u540D\u5B57"},null,8,["modelValue"])]),_:1}),r(i,{label:"\u73ED\u4E3B\u4EFB",prop:"teacher"},{default:o(()=>[r(n,{modelValue:l(e).teacher,"onUpdate:modelValue":a[1]||(a[1]=d=>l(e).teacher=d),placeholder:"\u8BF7\u8F93\u5165\u73ED\u4E3B\u4EFB"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[g,l(t)]])}}})});export{_,K as __tla};
