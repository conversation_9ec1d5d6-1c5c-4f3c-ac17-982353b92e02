import{d as E,r as _,f as I,C as P,o as h,s as w,w as r,H as R,a as l,i as e,G as c,g as b,O as k,P as z,Q as B,__tla as D}from"./index-B58vSXOX.js";import{_ as F,__tla as M}from"./ContentWrap.vue_vue_type_script_setup_true_lang-1EA99Qaa.js";import{_ as O,__tla as Y}from"./index.vue_vue_type_script_setup_true_lang-Bafh4YVk.js";import{_ as j,__tla as q}from"./DictTag.vue_vue_type_script_lang-Cjr03k2z.js";import{C as A,__tla as G}from"./index-BcIGxqWR.js";import{d as u,__tla as H}from"./formatTime-CjAREQvd.js";let x,Q=Promise.all([(()=>{try{return D}catch{}})(),(()=>{try{return M}catch{}})(),(()=>{try{return Y}catch{}})(),(()=>{try{return q}catch{}})(),(()=>{try{return G}catch{}})(),(()=>{try{return H}catch{}})()]).then(async()=>{let d,m;d=["src"],m=["src"],x=E({name:"UserCertifications",__name:"UserCertifications",props:{userId:{type:Number,required:!0}},setup(v){const p=_(!0),g=_([]),f=_(0),n=I({pageNo:1,pageSize:10,userId:NaN}),y=async()=>{p.value=!0;try{const s=await A.getCertificationPage(n);g.value=s.list,f.value=s.total}finally{p.value=!1}},{userId:S}=v;return P(()=>{n.userId=S,y()}),(s,i)=>{const a=k,o=j,T=z,N=O,U=F,C=B;return h(),w(U,null,{default:r(()=>[R((h(),w(T,{data:l(g),stripe:!0,"show-overflow-tooltip":!0},{default:r(()=>[e(a,{label:"ID",align:"center",prop:"id"}),e(a,{label:"\u5730\u533A",align:"center",prop:"areaName"}),e(a,{label:"\u771F\u540D",align:"center",prop:"realName"}),e(a,{label:"\u6027\u522B",align:"center",prop:"sex"},{default:r(t=>[e(o,{type:l(c).SYSTEM_USER_SEX,value:t.row.sex},null,8,["type","value"])]),_:1}),e(a,{label:"\u8BC1\u4EF6\u53F7\u7801",align:"center",prop:"credentialsCode",width:"250px"}),e(a,{label:"\u8BC1\u4EF6\u7C7B\u578B",align:"center",prop:"credentialsType",width:"100px"},{default:r(t=>[e(o,{type:l(c).DOCUMENT_TYPE,value:t.row.credentialsType},null,8,["type","value"])]),_:1}),e(a,{label:"\u72B6\u6001",align:"center",prop:"status",width:"120px"},{default:r(t=>[e(o,{type:l(c).USER_CERT_STATUS,value:t.row.status},null,8,["type","value"])]),_:1}),e(a,{label:"\u751F\u65E5",align:"center",prop:"birthday",formatter:l(u),width:"180px"},null,8,["formatter"]),e(a,{label:"\u8BC1\u4EF6\u6B63\u9762",align:"center",prop:"credentialsFront"},{default:r(t=>[b("img",{src:t.row.credentialsFront,style:{width:"150px"}},null,8,d)]),_:1}),e(a,{label:"\u8BC1\u4EF6\u53CD\u9762",align:"center",prop:"credentialsBack"},{default:r(t=>[b("img",{src:t.row.credentialsBack,style:{width:"150px"}},null,8,m)]),_:1}),e(a,{label:"\u5904\u7406\u5907\u6CE8",align:"center",prop:"handleRemark",width:"180px"}),e(a,{label:"\u5904\u7406\u4EBA",align:"center",prop:"handler"}),e(a,{label:"\u5904\u7406\u65F6\u95F4",align:"center",prop:"handleTime",formatter:l(u),width:"180px"},null,8,["formatter"]),e(a,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:l(u),width:"180px"},null,8,["formatter"])]),_:1},8,["data"])),[[C,l(p)]]),e(N,{total:l(f),page:l(n).pageNo,"onUpdate:page":i[0]||(i[0]=t=>l(n).pageNo=t),limit:l(n).pageSize,"onUpdate:limit":i[1]||(i[1]=t=>l(n).pageSize=t),onPagination:y},null,8,["total","page","limit"])]),_:1})}}})});export{x as _,Q as __tla};
