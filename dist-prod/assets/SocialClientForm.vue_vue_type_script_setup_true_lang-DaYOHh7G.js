import{aN as y,d as Q,l as G,I as H,r as m,f as L,o,s as p,w as s,i as u,a as l,j as _,H as Z,c as T,F as S,k as I,V as w,G as U,t as h,aa as z,y as B,Z as J,K as W,aQ as X,aR as $,N as ee,M as le,Q as ae,__tla as te}from"./index-B58vSXOX.js";import{_ as se,__tla as ue}from"./Dialog.vue_vue_type_style_index_0_lang-DX8U556d.js";let M,x,F,de=Promise.all([(()=>{try{return te}catch{}})(),(()=>{try{return ue}catch{}})()]).then(async()=>{F=async v=>await y.get({url:"/system/social-client/page",params:v}),x=async v=>await y.delete({url:"/system/social-client/delete?id="+v}),M=Q({__name:"SocialClientForm",emits:["success"],setup(v,{expose:N,emit:O}){const{t:g}=G(),k=H(),i=m(!1),q=m(""),c=m(!1),C=m(""),t=m({id:void 0,name:void 0,socialType:void 0,userType:void 0,clientId:void 0,clientSecret:void 0,agentId:void 0,status:0}),P=L({name:[{required:!0,message:"\u5E94\u7528\u540D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],socialType:[{required:!0,message:"\u793E\u4EA4\u5E73\u53F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],userType:[{required:!0,message:"\u7528\u6237\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],clientId:[{required:!0,message:"\u5BA2\u6237\u7AEF\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],clientSecret:[{required:!0,message:"\u5BA2\u6237\u7AEF\u5BC6\u94A5\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],status:[{required:!0,message:"\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),f=m();N({open:async(d,a)=>{if(i.value=!0,q.value=g("action."+d),C.value=d,A(),a){c.value=!0;try{t.value=await(async n=>await y.get({url:"/system/social-client/get?id="+n}))(a)}finally{c.value=!1}}}});const R=O,Y=async()=>{if(f&&await f.value.validate()){c.value=!0;try{const d=t.value;C.value==="create"?(await(async a=>await y.post({url:"/system/social-client/create",data:a}))(d),k.success(g("common.createSuccess"))):(await(async a=>await y.put({url:"/system/social-client/update",data:a}))(d),k.success(g("common.updateSuccess"))),i.value=!1,R("success")}finally{c.value=!1}}},A=()=>{var d;t.value={id:void 0,name:void 0,socialType:void 0,userType:void 0,clientId:void 0,clientSecret:void 0,agentId:void 0,status:0},(d=f.value)==null||d.resetFields()};return(d,a)=>{const n=J,r=W,b=X,V=$,K=ee,E=le,j=se,D=ae;return o(),p(j,{modelValue:l(i),"onUpdate:modelValue":a[8]||(a[8]=e=>B(i)?i.value=e:null),title:l(q)},{footer:s(()=>[u(E,{disabled:l(c),type:"primary",onClick:Y},{default:s(()=>[_("\u786E \u5B9A")]),_:1},8,["disabled"]),u(E,{onClick:a[7]||(a[7]=e=>i.value=!1)},{default:s(()=>[_("\u53D6 \u6D88")]),_:1})]),default:s(()=>[Z((o(),p(K,{ref_key:"formRef",ref:f,model:l(t),rules:l(P),"label-width":"120px"},{default:s(()=>[u(r,{label:"\u5E94\u7528\u540D",prop:"name"},{default:s(()=>[u(n,{modelValue:l(t).name,"onUpdate:modelValue":a[0]||(a[0]=e=>l(t).name=e),placeholder:"\u8BF7\u8F93\u5165\u5E94\u7528\u540D"},null,8,["modelValue"])]),_:1}),u(r,{label:"\u793E\u4EA4\u5E73\u53F0",prop:"socialType"},{default:s(()=>[u(V,{modelValue:l(t).socialType,"onUpdate:modelValue":a[1]||(a[1]=e=>l(t).socialType=e)},{default:s(()=>[(o(!0),T(S,null,I(l(w)(l(U).SYSTEM_SOCIAL_TYPE),e=>(o(),p(b,{key:e.value,label:e.value},{default:s(()=>[_(h(e.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),u(r,{label:"\u7528\u6237\u7C7B\u578B",prop:"userType"},{default:s(()=>[u(V,{modelValue:l(t).userType,"onUpdate:modelValue":a[2]||(a[2]=e=>l(t).userType=e)},{default:s(()=>[(o(!0),T(S,null,I(l(w)(l(U).USER_TYPE),e=>(o(),p(b,{key:e.value,label:e.value},{default:s(()=>[_(h(e.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),u(r,{label:"\u5BA2\u6237\u7AEF\u7F16\u53F7",prop:"clientId"},{default:s(()=>[u(n,{modelValue:l(t).clientId,"onUpdate:modelValue":a[3]||(a[3]=e=>l(t).clientId=e),placeholder:"\u8BF7\u8F93\u5165\u5BA2\u6237\u7AEF\u7F16\u53F7,\u5BF9\u5E94\u5404\u5E73\u53F0\u7684appKey"},null,8,["modelValue"])]),_:1}),u(r,{label:"\u5BA2\u6237\u7AEF\u5BC6\u94A5",prop:"clientSecret"},{default:s(()=>[u(n,{modelValue:l(t).clientSecret,"onUpdate:modelValue":a[4]||(a[4]=e=>l(t).clientSecret=e),placeholder:"\u8BF7\u8F93\u5165\u5BA2\u6237\u7AEF\u5BC6\u94A5,\u5BF9\u5E94\u5404\u5E73\u53F0\u7684appSecret"},null,8,["modelValue"])]),_:1}),l(t).socialType===30?(o(),p(r,{key:0,label:"agentId",prop:"agentId"},{default:s(()=>[u(n,{modelValue:l(t).agentId,"onUpdate:modelValue":a[5]||(a[5]=e=>l(t).agentId=e),placeholder:"\u6388\u6743\u65B9\u7684\u7F51\u9875\u5E94\u7528 ID\uFF0C\u6709\u5219\u586B"},null,8,["modelValue"])]),_:1})):z("",!0),u(r,{label:"\u72B6\u6001",prop:"status"},{default:s(()=>[u(V,{modelValue:l(t).status,"onUpdate:modelValue":a[6]||(a[6]=e=>l(t).status=e)},{default:s(()=>[(o(!0),T(S,null,I(l(w)(l(U).COMMON_STATUS),e=>(o(),p(b,{key:e.value,label:e.value},{default:s(()=>[_(h(e.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[D,l(c)]])]),_:1},8,["modelValue","title"])}}})});export{M as _,de as __tla,x as d,F as g};
