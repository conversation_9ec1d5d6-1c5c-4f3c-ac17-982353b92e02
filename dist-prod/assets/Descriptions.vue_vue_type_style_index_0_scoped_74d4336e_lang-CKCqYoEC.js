import{d as G,p,a0 as I,b as $,a$ as J,ci as K,r as L,o as r,c as v,a1 as f,a as t,g as x,j as m,t as u,s as g,w as s,aa as h,i as b,a5 as N,_ as Q,b3 as R,cZ as U,H as V,a9 as W,au as F,bP as X,at as d,F as Y,k as aa,bS as ea,__tla as ta}from"./index-B58vSXOX.js";import{a as la,E as ra,__tla as sa}from"./el-descriptions-item-Ce7RFMyo.js";import{_ as oa,__tla as da}from"./DictTag.vue_vue_type_script_lang-Cjr03k2z.js";let j,ia=Promise.all([(()=>{try{return ta}catch{}})(),(()=>{try{return sa}catch{}})(),(()=>{try{return da}catch{}})()]).then(async()=>{let k;k={class:"flex items-center"},j=G({name:"Descriptions",__name:"Descriptions",props:{title:p.string.def(""),message:p.string.def(""),collapse:p.bool.def(!0),columns:p.number.def(1),schema:{type:Array,default:()=>[]},data:{type:Object,default:()=>({})}},setup(e){const O=I(),P=$(()=>O.getMobile),C=J(),D=K(),_=e,{getPrefixCls:E}=N(),i=E("descriptions"),H=$(()=>{const l=["title","message","collapse","schema","data","class"],c={...C,..._};for(const o in c)l.indexOf(o)!==-1&&delete c[o];return c}),n=L(!0),T=()=>{_.collapse&&(n.value=!t(n))};return(l,c)=>{const o=Q,z=R,A=oa,M=ra,S=la,Z=U;return r(),v("div",{class:f([t(i),"bg-[var(--el-color-white)] dark:bg-[var(--el-bg-color)] dark:border-[var(--el-border-color)] dark:border-1px"])},[e.title?(r(),v("div",{key:0,class:f([`${t(i)}-header`,"h-50px flex justify-between items-center b-b-1 border-solid border-[var(--el-border-color)] px-10px cursor-pointer dark:border-[var(--el-border-color)]"]),onClick:T},[x("div",{class:f([`${t(i)}-header__title`,"relative font-18px font-bold ml-10px"])},[x("div",k,[m(u(e.title)+" ",1),e.message?(r(),g(z,{key:0,content:e.message,placement:"right"},{default:s(()=>[b(o,{class:"ml-5px",icon:"ep:warning"})]),_:1},8,["content"])):h("",!0)])],2),e.collapse?(r(),g(o,{key:0,icon:t(n)?"ep:arrow-down":"ep:arrow-up"},null,8,["icon"])):h("",!0)],2)):h("",!0),b(Z,null,{default:s(()=>[V(x("div",{class:f([`${t(i)}-content`,"p-10px"])},[b(S,F({column:_.columns,direction:t(P)?"vertical":"horizontal",border:""},t(H)),X({default:s(()=>[(r(!0),v(Y,null,aa(e.schema,a=>(r(),g(M,F({key:a.field,"min-width":"80"},(q=>{const B=["field"],y={...q};for(const w in y)B.indexOf(w)!==-1&&delete y[w];return y})(a)),{label:s(()=>[d(l.$slots,`${a.field}-label`,{row:{label:a.label}},()=>[m(u(a.label),1)],!0)]),default:s(()=>[a.dateFormat?d(l.$slots,"default",{key:0},()=>[m(u(e.data[a.field]!==null?t(ea)(e.data[a.field]).format(a.dateFormat):""),1)],!0):a.dictType?d(l.$slots,"default",{key:1},()=>[b(A,{type:a.dictType,value:e.data[a.field]+""},null,8,["type","value"])],!0):d(l.$slots,a.field,{key:2,row:e.data},()=>[m(u(a.mappedField?e.data[a.mappedField]:e.data[a.field]),1)],!0)]),_:2},1040))),128))]),_:2},[t(D).extra?{name:"extra",fn:s(()=>[d(l.$slots,"extra",{},void 0,!0)]),key:"0"}:void 0]),1040,["column","direction"])],2),[[W,t(n)]])]),_:3})],2)}}})});export{j as _,ia as __tla};
