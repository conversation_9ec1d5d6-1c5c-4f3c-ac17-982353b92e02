import{d as h,l as y,I as b,r as n,C as v,o as _,s as o,w as p,H as g,a as l,i as t,O as w,P as I,Q as x,__tla as P}from"./index-B58vSXOX.js";import{_ as C,__tla as D}from"./ContentWrap.vue_vue_type_script_setup_true_lang-1EA99Qaa.js";import{d as G,__tla as H}from"./formatTime-CjAREQvd.js";import{b as L,__tla as O}from"./index-C3hYB9KS.js";let c,Q=Promise.all([(()=>{try{return P}catch{}})(),(()=>{try{return D}catch{}})(),(()=>{try{return H}catch{}})(),(()=>{try{return O}catch{}})()]).then(async()=>{c=h({__name:"Demo03GradeList",props:{studentId:{}},setup(i){y(),b();const u=i,r=n(!1),s=n([]);return v(()=>{(async()=>{r.value=!0;try{const e=await L(u.studentId);if(!e)return;s.value.push(e)}finally{r.value=!1}})()}),(e,T)=>{const a=w,m=I,d=C,f=x;return _(),o(d,null,{default:p(()=>[g((_(),o(m,{data:l(s),stripe:!0,"show-overflow-tooltip":!0},{default:p(()=>[t(a,{label:"\u7F16\u53F7",align:"center",prop:"id"}),t(a,{label:"\u540D\u5B57",align:"center",prop:"name"}),t(a,{label:"\u73ED\u4E3B\u4EFB",align:"center",prop:"teacher"}),t(a,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:l(G),width:"180px"},null,8,["formatter"])]),_:1},8,["data"])),[[f,l(r)]])]),_:1})}}})});export{c as _,Q as __tla};
