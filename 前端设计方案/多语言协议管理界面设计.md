# 多语言协议管理界面设计方案

## 📋 **页面结构设计**

### 1. 协议列表页面 (AgreementMultiLangList.vue)

```vue
<template>
  <div class="agreement-multilang-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="协议类型" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择协议类型">
          <el-option label="隐私协议" value="1" />
          <el-option label="用户准则" value="2" />
          <el-option label="服务条款" value="3" />
          <el-option label="免责声明" value="4" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态">
          <el-option label="启用" value="1" />
          <el-option label="禁用" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" @click="handleAdd">新增协议</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" @click="handleExport">导出</el-button>
      </el-col>
    </el-row>

    <!-- 协议列表 -->
    <el-table v-loading="loading" :data="agreementList">
      <el-table-column label="协议ID" prop="id" width="80" />
      <el-table-column label="协议类型" prop="type" width="120">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.AGREEMENT_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column label="版本号" prop="version" width="100" />
      <el-table-column label="支持语言" width="200">
        <template #default="scope">
          <el-tag 
            v-for="lang in scope.row.supportedLanguages" 
            :key="lang" 
            size="small" 
            class="mr-1"
          >
            {{ getLanguageName(lang) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="status" width="80">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="生效时间" prop="effectiveTime" width="180" />
      <el-table-column label="创建时间" prop="createTime" width="180" />
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button type="text" @click="handleManageLanguages(scope.row)">管理语言</el-button>
          <el-button type="text" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 协议编辑对话框 -->
    <AgreementMultiLangForm 
      ref="formRef" 
      @success="getList" 
    />

    <!-- 语言管理对话框 -->
    <AgreementLanguageManager 
      ref="languageManagerRef" 
      @success="getList" 
    />
  </div>
</template>
```

### 2. 协议编辑表单 (AgreementMultiLangForm.vue)

```vue
<template>
  <el-dialog 
    :title="title" 
    v-model="open" 
    width="80%" 
    :close-on-click-modal="false"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
      <!-- 基础信息 -->
      <el-row>
        <el-col :span="12">
          <el-form-item label="协议类型" prop="type">
            <el-select v-model="form.type" placeholder="请选择协议类型">
              <el-option label="隐私协议" value="1" />
              <el-option label="用户准则" value="2" />
              <el-option label="服务条款" value="3" />
              <el-option label="免责声明" value="4" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="版本号" prop="version">
            <el-input v-model="form.version" placeholder="请输入版本号" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 多语言内容 -->
      <el-form-item label="多语言内容">
        <el-tabs v-model="activeLanguage" type="card" @tab-add="handleAddLanguage" @tab-remove="handleRemoveLanguage" editable>
          <el-tab-pane 
            v-for="(content, index) in form.contents" 
            :key="content.languageCode"
            :label="getLanguageName(content.languageCode)"
            :name="content.languageCode"
          >
            <el-form :model="content" :rules="contentRules" :ref="el => contentFormRefs[index] = el">
              <el-form-item label="语言" prop="languageCode">
                <el-select v-model="content.languageCode" placeholder="请选择语言">
                  <el-option 
                    v-for="lang in availableLanguages" 
                    :key="lang.code" 
                    :label="lang.name" 
                    :value="lang.code" 
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="标题" prop="title">
                <el-input v-model="content.title" placeholder="请输入协议标题" />
              </el-form-item>
              <el-form-item label="内容" prop="content">
                <Editor v-model="content.content" :min-height="300" />
              </el-form-item>
            </el-form>
          </el-tab-pane>
        </el-tabs>
      </el-form-item>

      <!-- 其他设置 -->
      <el-row>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio :label="1">启用</el-radio>
              <el-radio :label="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="生效时间" prop="effectiveTime">
            <el-date-picker
              v-model="form.effectiveTime"
              type="datetime"
              placeholder="请选择生效时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="备注" prop="remark">
        <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="submitForm">确定</el-button>
    </template>
  </el-dialog>
</template>
```

### 3. 语言管理器 (AgreementLanguageManager.vue)

```vue
<template>
  <el-dialog title="管理协议语言" v-model="open" width="60%">
    <el-table :data="languageList" style="width: 100%">
      <el-table-column label="语言代码" prop="languageCode" width="120" />
      <el-table-column label="语言名称" width="150">
        <template #default="scope">
          {{ getLanguageName(scope.row.languageCode) }}
        </template>
      </el-table-column>
      <el-table-column label="标题" prop="title" />
      <el-table-column label="最后更新" prop="updateTime" width="180" />
      <el-table-column label="操作" width="150">
        <template #default="scope">
          <el-button type="text" @click="handleEditContent(scope.row)">编辑</el-button>
          <el-button type="text" @click="handleDeleteContent(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="mt-4">
      <el-button type="primary" @click="handleAddContent">添加语言版本</el-button>
    </div>

    <template #footer>
      <el-button @click="close">关闭</el-button>
    </template>
  </el-dialog>
</template>
```

## 🔧 **核心功能实现**

### 1. 语言配置管理
- 支持动态添加/删除语言版本
- 语言代码标准化（zh-CN, en, ja, ko等）
- 语言名称本地化显示

### 2. 富文本编辑器集成
- 支持HTML格式的协议内容编辑
- 图片上传和管理
- 格式化工具栏

### 3. 数据验证
- 每个协议至少需要一种语言版本
- 同一协议下语言代码不能重复
- 标题和内容必填验证

### 4. 用户体验优化
- Tab切换编辑不同语言版本
- 实时预览功能
- 自动保存草稿
- 批量操作支持

## 📱 **移动端适配**

### 1. 响应式设计
- 表格在移动端自动转换为卡片布局
- 编辑表单适配小屏幕
- 触摸友好的操作按钮

### 2. 性能优化
- 懒加载语言内容
- 图片压缩和CDN加速
- 缓存策略优化
