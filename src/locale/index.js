import { createI18n } from 'vue-i18n'; // v9.x
// const zhCn = new URL("@/static/lang/zh-CN.webp", import.meta.url).href;


import zhcn from './zh-cn.json'; //
import en from './en.json'; //
import es from './es.json'; //
import fr from './fr.json'; //
import ja from './ja.json'; //
import ko from './ko.json'; //
import th from './th.json'; //
import vi from './vi.json'; //
import zhtw from './zh-tw.json'; //
// zh-cn 简体中文
// zh-Hant 繁体
// Français  法语
// en  英文
// es  西班牙语
// jp  日语
// kr  韩语
// vn  越南语
// th  泰语

// English Français  Español  日本語 ภาษาไทย  Tiếng Việt 조선말 中文(繁体)
const messages = {
	'zh-cn': zhcn,
	'zh-tw': zhtw,
	'vi': vi,
	'th': th,
	'ko': ko,
	'ja': ja,
	'fr': fr,
	'es': es,
	'en': en
}
const locale = uni.getLocale();
//如果要设置本地语言需要设置这里跟manifest.json内的语言
const i18nConfig = {
	locale: locale,// 获取已设置的语言
	// legacy:false,
	messages
}
const i18n = createI18n(i18nConfig);


export {
	messages,
	i18n
};