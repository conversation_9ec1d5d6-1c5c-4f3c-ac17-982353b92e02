import { t } from './useI18n';
import BigNumber from 'bignumber.js';
//拆分字符串为数组
export const splitNumber = (number) => {
    if (number) {
        return number.split(',');
    }
    return [];
}
//和值
export const combinedValue = (number) => {
    const numberArr = splitNumber(number);
    const sum = numberArr.reduce((a, b) => {
        return new BigNumber(a).plus(b)
    }, 0)
    return sum.toString()
}
//是否大
export const isItLarge = (sum) => {
    // const sum = combinedValue(number);
    if (sum >= 14 && sum <= 27) {
        return t('大')
    }
    return '-'
}
//是否小
export const isItSmall = (sum) => {
    // const sum = combinedValue(number);
    if (sum <= 13) {
        return t('小')
    }
    return '-'
}

//是否单
export const isItSingle = (sum) => {
    // const sum = combinedValue(number);
    if (sum % 2 !== 0) {
        return t('单')
    }
    return '-'
}
//是否双
export const isItDouble = (sum) => {
    // const sum = combinedValue(number);
    if (sum % 2 === 0) {
        return t('双')
    }
    return '-'
}


//是否大单
export const isItALargeOrder = (number) => {
    const sum = combinedValue(number);
    if (sum % 2 === 0 && sum <= 13) {
        return t('小双')
    }
    if (sum % 2 === 0 && sum >= 14 && sum <= 27) {
        return t('大双')
    }
    if (sum % 2 !== 0 && sum <= 13) {
        return t('小单')
    }

    if (sum % 2 !== 0 && sum >= 14 && sum <= 27) {
        return t('大单')
    }
    return '-'
}

export const isSumItALargeOrder = (sum) => {
    if (sum || sum == 0) {
        if (sum % 2 === 0 && sum <= 13) {
            return t('小双')
        }
        if (sum % 2 === 0 && sum >= 14 && sum <= 27) {
            return t('大双')
        }
        if (sum % 2 !== 0 && sum <= 13) {
            return t('小单')
        }

        if (sum % 2 !== 0 && sum >= 14 && sum <= 27) {
            return t('大单')
        }
    }
    return '-'
}
// const pong = {
//     type: 'pong'
// }
// this.socket.send(JSON.stringify(pong));