/**
 * 常用方法封装 请求，文件上传等
 * <AUTHOR>
 **/
import { i18n } from "../locale";
import { websocketClient } from "../plugins/websocket";
import { interfaceUrl } from "./env";
import FingerprintJS from "@fingerprintjs/fingerprintjs";

const RESCODETYPE = {
	SUCCESS: 0, //成功
	ERROR: 400, //错误
	NOT_LOGGED_IN: 401, //未登录
	CHANGEBUSPWD: 4, //设置交易密码
	REAL_NAME_IS_NOT_CERTIFIED: 1004023005, //实名未认证
	REAL_NAME_AUTHENTICATION: 1004023006, //实名认证中
	REAL_NAME_AUTHENTICATION_FAILED: 1004023005, //实名认证失败
};

function transformResponseHook(response) {}
const tui = {
	//接口地址
	toast: function (text, duration, success) {
		uni.showToast({
			title: text || i18n.global.t("出错啦"),
			icon: success ? "success" : "none",
			duration: duration || 2000,
		});
	},
	modal: function (title, content, showCancel, callback, confirmColor, confirmText) {
		uni.showModal({
			title: title || i18n.global.t("提示"),
			content: content,
			showCancel: showCancel,
			cancelColor: "#555",
			confirmColor: confirmColor || "#5677fc",
			confirmText: confirmText || i18n.global.t("确定"),
			success(res) {
				if (res.confirm) {
					callback && callback(true);
				} else {
					callback && callback(false);
				}
			},
		});
	},
	isAndroid: function () {
		const res = uni.getSystemInfoSync();
		return res.platform.toLocaleLowerCase() == "android";
	},
	isPhoneX: function () {
		const res = uni.getSystemInfoSync();
		let iphonex = false;
		let models = ["iphonex", "iphonexr", "iphonexsmax", "iphone11", "iphone11pro", "iphone11promax"];
		const model = res.model.replace(/\s/g, "").toLowerCase();
		if (models.includes(model)) {
			iphonex = true;
		}
		return iphonex;
	},
	constNum: function () {
		let time = 0;
		// #ifdef APP-PLUS
		time = this.isAndroid() ? 300 : 0;
		// #endif
		return time;
	},
	delayed: null,
	showLoading: function (title, mask = true) {
		uni.showLoading({
			mask: mask,
			title: title || i18n.global.t("请稍候"),
		});
	},
	/**
	 * 请求数据处理
	 * @param string url 请求地址
	 * @param string method 请求方式
	 *  GET or POST
	 * @param {*} postData 请求参数
	 * @param bool isDelay 是否延迟显示loading
	 * @param bool isForm 数据格式
	 *  true: 'application/x-www-form-urlencoded'
	 *  false:'application/json'
	 * @param bool hideLoading 是否隐藏loading
	 *  true: 隐藏
	 *  false:显示
	 */
	request: async function (url, method, postData, isDelay, isForm, hideLoading) {
		console.log(url);
		//接口请求
		let loadding = false;
		tui.delayed && uni.hideLoading();
		clearTimeout(tui.delayed);
		tui.delayed = null;
		if (!hideLoading) {
			if (isDelay) {
				tui.delayed = setTimeout(() => {
					loadding = true;
					tui.showLoading();
				}, 1000);
			} else {
				loadding = true;
				tui.showLoading();
			}
		}

		return new Promise((resolve, reject) => {
			uni.request({
				url: interfaceUrl() + url,
				data: postData,
				header: {
					"client-servername": tui.getClientServername(),
					"content-type": isForm ? "application/x-www-form-urlencoded" : "application/json",
					Authorization: tui.getToken(),
					lang: uni.getLocale(),
					"Accept-Language": uni.getLocale(),
				},
				method: method, //'GET','POST'
				dataType: "json",
				success: (res) => {
					clearTimeout(tui.delayed);
					tui.delayed = null;
					if (loadding && !hideLoading) {
						uni.hideLoading();
					}

					if (res.statusCode == 401) {
						tui.toast(i18n.global.t("errMsg401"));
						reject(res);
					}

					if (res.statusCode == 403) {
						tui.toast(i18n.global.t("errMsg403"));
						reject(res);
					}
					if (res.statusCode == 404) {
						tui.toast(i18n.global.t("errMsg404"));
						reject(res);
					}
					if (res.statusCode == 408) {
						tui.toast(i18n.global.t("errMsg408"));
						reject(res);
					}
					if (res.statusCode == 500) {
						tui.toast(i18n.global.t("errMsg500"));
						reject(res);
					}
					if (res.statusCode == 501) {
						tui.toast(i18n.global.t("errMsg501"));
						reject(res);
					}
					if (res.statusCode == 502) {
						tui.toast(i18n.global.t("errMsg502"));
						reject(res);
					}
					if (res.statusCode == 503) {
						tui.toast(i18n.global.t("errMsg503"));
						reject(res);
					}
					if (res.statusCode == 504) {
						tui.toast(i18n.global.t("errMsg504"));
						reject(res);
					}
					if (res.statusCode == 505) {
						tui.toast(i18n.global.t("errMsg505"));
						reject(res);
					}
					const href = window.location.hash;
					if (res.data.code == RESCODETYPE.NOT_LOGGED_IN && (href.indexOf("/pages/loginOrRegistration/index") == -1 || href.indexOf("/pages/signIn/index") == -1)) {
						// tui.toast(res.data.msg)
						tui.removeToken();
						if (websocketClient.socket && websocketClient.socket.readyState === WebSocket.OPEN) {
							const optios = {
								tyoe: "logout",
							};
							websocketClient.sendMessage(JSON.stringify(optios));
						}
						uni.reLaunch({
							url: "/pages/loginOrRegistration/index",
						});
						reject(res);
					}
					if (res.data.code == RESCODETYPE.CHANGEBUSPWD) {
						tui.toast(res.data.msg);
						uni.navigateTo({
							url: "/pages/changebuspwd/index",
						});
						reject(res);
					}
					//CODE 5实名未认证 7实名认证失败
					if (res.data.code == RESCODETYPE.REAL_NAME_IS_NOT_CERTIFIED || res.data.code == RESCODETYPE.REAL_NAME_AUTHENTICATION_FAILED) {
						uni.navigateTo({
							url: "/pages/authenticateIdentity/index",
							success: () => {
								tui.toast(res.data.msg);
							},
						});
						reject(res);
					}

					// CODE 6 实名认证中
					if (res.data.code == RESCODETYPE.REAL_NAME_AUTHENTICATION) {
						tui.toast(res.data.msg);
						reject(res);
					}
					//
					if (res.data.code == RESCODETYPE.SUCCESS) {
						resolve(res.data);
					} else {
						tui.toast(res.data.msg);
						reject(res);
					}
				},
				fail: (res) => {
					clearTimeout(tui.delayed);
					tui.delayed = null;
					tui.toast(i18n.global.t("网络不给力，请稍后再试"));
					// 在底层的网络请求抛出异常时，写uni.$emit('z-paging-error-emit');即可
					reject(res);
				},
			});
		});
	},
	/**
	 * 上传文件
	 * @param string url 请求地址
	 * @param string src 文件路径
	 */
	uploadFile: function (url, src) {
		tui.showLoading();
		return new Promise((resolve, reject) => {
			const uploadTask = uni.uploadFile({
				url: interfaceUrl() + url,
				filePath: src,
				name: "file",
				header: {
					"client-servername": tui.getClientServername(),
					Authorization: tui.getToken(),
					lang: uni.getLocale(),
					"Accept-Language": uni.getLocale(),
				},
				formData: {},
				success: function (res) {
					uni.hideLoading();
					console.log(res);
					const data = JSON.parse(res.data);
					if (data.code == RESCODETYPE.SUCCESS) {
						resolve(data);
					} else {
						reject(res);
						tui.toast(res.msg);
					}
					// resolve(JSON.parse(res.data))
				},
				fail: function (res) {
					reject(res);
					tui.toast(res.msg);
				},
			});
		});
	},
	tuiJsonp: function (url, callback, callbackname) {
		// #ifdef H5
		window[callbackname] = callback;
		let tuiScript = document.createElement("script");
		tuiScript.src = url;
		tuiScript.type = "text/javascript";
		document.head.appendChild(tuiScript);
		document.head.removeChild(tuiScript);
		// #endif
	},
	//设置用户信息
	setUserInfo: function (mobile, token) {
		//uni.setStorageSync("thorui_token", token)
		uni.setStorageSync("thorui_mobile", mobile);
	},
	setToken: function (token) {
		uni.setStorageSync("Token", token);
	},
	removeToken: function () {
		uni.removeStorageSync("Token");
		uni.removeStorageSync("account");
	},
	//获取token
	getToken() {
		return uni.getStorageSync("Token") ? uni.getStorageSync("Token") : "";
	},
	//获取租户编号
	getTenantId() {
		return uni.getStorageSync("TenantId") ? uni.getStorageSync("TenantId") : "1";
	},
	//客户端域名,示例值(www.xxx.com)
	getClientServername() {
		return import.meta.env.MODE === "development" ? "web.testdev8899.top" : location.hostname;
		// return import.meta.env.MODE === 'development'?'htxdemo.sryeflu.top':location.hostname
	},
	//判断是否登录
	isLogin: function () {
		return uni.getStorageSync("thorui_mobile") ? true : false;
	},
	//跳转页面，校验登录状态
	href(url, isVerify) {
		if (isVerify && !tui.isLogin()) {
			uni.navigateTo({
				url: "/pages/common/login/login",
			});
		} else {
			uni.navigateTo({
				url: url,
			});
		}
	},
	async getFingerprintJsVisitorId() {
		const fp = await FingerprintJS.load();
		const result = await fp.get();
		const visitorId = result.visitorId;
		return visitorId;
	},
};

export default tui;
