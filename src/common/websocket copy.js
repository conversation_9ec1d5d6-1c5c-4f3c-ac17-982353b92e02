
/**
 * 使用方法-已全局注入-消息已队列发送
 * import { inject } from "vue";
 * const websocketClient = inject("websocketClient");引入ws
 * 发送消息 websocketClient.sendMessage()
 * 全局关闭 websocketClient.close()
 * 全局开启 websocketClient.open()
 * 
 * 
 */
import dayjs from "dayjs";
import tui from './httpRequest'

export default class WebSocketClient {
    constructor(url, onOpen = null, onMessage = null, onClose = null, reconnectInterval = 5000, pingInterval = 20000) {
        this.url = url;
        this.onOpen = onOpen;
        this.onMessage = onMessage;
        this.onClose = onClose;
        this.reconnectInterval = reconnectInterval;
        this.pingInterval = pingInterval;
        this.socket = null;
        this.pingTimeout = null;
        this.isManualClose = false;  // 用来标识是否为手动关闭
        this.messageQueue = []; // 新增消息队列
    }

    // 手动打开WebSocket连接
    open() {
        if (!this.socket || this.socket.readyState === WebSocket.CLOSED) {
            this.connect();
            this.isManualClose = false;
        } else {
            console.log('WebSocket is already open or connecting.');
        }
    }

    connect() {
        this.socket = new WebSocket(this.url);

        this.socket.onopen = () => {
            console.log('WebSocket connection established.');
            this.ping();
              // 发送队列中的所有消息
            this.messageQueue.forEach((message) => {
                this.sendMessage(message);
            });
            this.messageQueue = []; // 清空队列
            if (this.onOpen) {
                this.onOpen();
            }
        };

        this.socket.onmessage = (event) => {
            // console.log('Received:', event.data);
            if(event.data === 'pong'){
                return
             }
            if (this.onMessage) {
                this.onMessage(event.data);
            }
        };

        this.socket.onerror = (event) => {
            console.error('WebSocket error:', event);
        };

        this.socket.onclose = (event) => {
            console.log('WebSocket connection closed:', event.reason);
            if (!this.isManualClose) {
                this.reconnect();
            }
            if (this.onClose) {
                this.onClose();
            }
        };
    }

    // 修改reconnect，防止在手动关闭后自动重连
    reconnect() {
        if (!this.isManualClose) {
            setTimeout(() => {
                console.log('Attempting to reconnect...');
                this.connect();
            }, this.reconnectInterval);
        }
    }

    ping() {
        if (this.socket.readyState === WebSocket.OPEN) {
            // const pong = {
            //     type:'ping'
            //   }
              
            // this.socket.send(JSON.stringify(pong));
            this.socket.send('ping');
        }
        this.pingTimeout = setTimeout(() => this.ping(), this.pingInterval);
    }

    async sendMessage(message) {
        if (this.socket.readyState === WebSocket.OPEN) {
            if( typeof message === 'string'){
                this.socket.send(message);
                return
            }
            const dataMessage = JSON.parse(message);
            const visitorId  = await tui.getFingerprintJsVisitorId();
            const msg ={
                ...dataMessage,
                ts:dayjs().unix(),
                fp:visitorId
            }
            this.socket.send(JSON.stringify(msg));
        } else {
            console.log('WebSocket is not open. Cannot send message:', message);
            if(message=='ping'){
               //存在ping就不继续push
                const index = this.messageQueue.filter(item => item == 'ping');
                index.length ==0 && this.messageQueue.push(message); // 将消息加入队列
                return
            }
            this.messageQueue.push(message); // 将消息加入队列
        }
    }

    // 修改close方法，添加可选的消息参数和手动关闭标识
    close(message = null) {
        if (message && this.socket.readyState === WebSocket.OPEN) {
            this.socket.send(message); // 发送自定义消息
        }
        if (this.socket) {
            this.isManualClose = true; // 标记为手动关闭
            this.socket.close();
        }
        clearTimeout(this.pingTimeout);
    }
}