// import dayjs from 'dayjs';
// import utcPlugin from 'dayjs/plugin/utc'; // 引入 UTC 插件
// import timezone from 'dayjs/plugin/timezone';
// dayjs.extend(utcPlugin);
// dayjs.extend(timezone);
// //越南时区
// dayjs.tz.setDefault("Asia/Ho_Chi_Minh");

import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);

//格式化时间
export function useFormatTime(time, format = "YYYY-MM-DD HH:mm:ss") {
    if(!time){
        return ''
    }
    return dayjs(time).format(format);
}

export function replaceExceeding(str) {
    str = str?.toString() || '';
    if (str.length > 4) {
        return "****" + str.substring(str.length - 4);
    } else {
        return str;
    }
}

// 根据时区获取时间
export function getTimeByTimeZone(time, timeZone = "Asia/Ho_Chi_Minh") {
    const endTime = dayjs.tz(time, timeZone);
    const currentTime = dayjs().tz(timeZone);
    const diff = endTime.diff(currentTime);
    // 将毫秒转换为小时、分钟和秒
    const diffInHours = Math.floor(diff / (1000 * 60 * 60));
    const diffInMinutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    const diffInSeconds = Math.floor((diff % (1000 * 60)) / 1000);
    return {
        diffInHours,
        diffInMinutes,
        diffInSeconds,
    };
}

//格式化金额
/**
 * console.log(useFormatMoney(12345.6789)); // 不使用货币符号
 * console.log(useFormatMoney(12345.6789, true)); // 使用默认 USD 货币符号
 * console.log(useFormatMoney(12345.6789, true, "EUR")); // 使用欧元符号
 * console.log(useFormatMoney(12345.6789, false)); // 不使用货币符号
 * 
 * @param {*} price 价格
 * @param {*} useCurrencySymbol 是否使用货币符号
 * @param {*} currency 使用哪个法币符号
 * @returns 
 */
export function useFormatMoney(price, useCurrencySymbol = false, currency = "USD") {
    if(!price){
        return '0.00'
    }
    const options = {
        minimumFractionDigits: 4, // 最少显示 0 位小数
        maximumFractionDigits: 4, // 最多显示 6 位小数
    };
    if (useCurrencySymbol) {
        options.style = "currency";
        options.currency = currency;
    }
    const number = Number(price);
    if (isNaN(number)) {
        throw new Error("Invalid input: price must be a number--->"+price);
    }
    // 格式化数字，根据是否包含货币符号
    return number.toLocaleString(undefined, options);
}



//拆分数组长度
export function splitArrayIntoChunks(arr, chunkSize = 3) {
    const result = [];
    for (let i = 0; i < arr.length; i += chunkSize) {
        result.push(arr.slice(i, i + chunkSize));
    }
    return result;
}

/**
 * 对数组对象进行排序
 * @param {Array} array - 需要排序的数组对象
 * @param {string} key - 用于排序的字段
 * @param {string} order - 排序方式，可选值为 'asc'（升序）、'desc'（降序）、'none'（不排序）
 * @returns {Array} 排序后的数组对象
 */
export function sortArrayByKey(array, key, order = 'asc') {
    if (order === 'none') {
        return array;
    }
    return array.sort((a, b) => {
        if (!a.hasOwnProperty(key) || !b.hasOwnProperty(key)) {
            return 0;
        }

        // 将 key 的值转换为数字进行比较
        const varA = isNaN(Number(a[key])) ? a[key] : Number(a[key]);
        const varB = isNaN(Number(b[key])) ? b[key] : Number(b[key]);

        let comparison = 0;
        if (varA > varB) {
            comparison = 1;
        } else if (varA < varB) {
            comparison = -1;
        }

        return (order === 'desc') ? (comparison * -1) : comparison;
    });
}

//时间戳格式化
export function formatDate(time, ) {
    var date=new Date(time);
    var year=date.getFullYear();
    /* 在日期格式中，月份是从0开始的，因此要加0
     * 使用三元表达式在小于10的前面加0，以达到格式统一  如 09:11:05
     * */
    var month= date.getMonth()+1<10 ? "0"+(date.getMonth()+1) : date.getMonth()+1;
    var day=date.getDate()<10 ? "0"+date.getDate() : date.getDate();
    var hours=date.getHours()<10 ? "0"+date.getHours() : date.getHours();
    var minutes=date.getMinutes()<10 ? "0"+date.getMinutes() : date.getMinutes();
    var seconds=date.getSeconds()<10 ? "0"+date.getSeconds() : date.getSeconds();
    // 拼接
    return year+"-"+month+"-"+day+" "+hours+":"+minutes+":"+seconds;
}

/**
 * @description 字符串格式化为Query语法
 * @param { string } str
 * @return { object } Query语法
 */
export const strToParams = (str) => {
    const newparams = {}
    for (const item of str.split('&')) {
        newparams[item.split('=')[0]] = item.split('=')[1]
    }
    return newparams
}

export const getPrecision=(num)=>{
    const match = String(num).match(/\.(\d+)/);
    if (match) {
        return match[1].length;
    }
    return 0;
}


// let tokenCheckTimeoutId = null;

// export function checkAndRefreshToken() {
//   return new Promise((resolve, reject) => {
//     // 从本地存储获取 token 和过期时间
//     let accessToken = uni.getStorageSync('accessToken');
//     let refreshToken = uni.getStorageSync('refreshToken');
//     let expiresTime = uni.getStorageSync('expiresTime');

//     if (!accessToken || !refreshToken || !expiresTime) {
//       // 如果任何一个值不存在，则认为 token 不存在或无效
//       reject(new Error('Token not found'));
//       return;
//     }

//     let currentTime = new Date().getTime();
//     let oneDay = 24 * 60 * 60 * 1000;

//     // 判断是否快要过期
//     if ((expiresTime - currentTime) <= oneDay) {
//       // 调用刷新 token 的接口
//       refreshAccessToken(refreshToken)
//         .then(newTokenData => {
//           // 更新本地存储
//           uni.setStorageSync('accessToken', newTokenData.accessToken);
//           uni.setStorageSync('refreshToken', newTokenData.refreshToken);
//           uni.setStorageSync('expiresTime', newTokenData.expiresTime);

//           // 开始新的一轮定时检查
//           scheduleTokenCheck();

//           resolve(newTokenData);
//         })
//         .catch(error => {
//           // 刷新 token 失败处理
//           console.error('Failed to refresh token:', error);
//           reject(error);
//         });
//     } else {
//       // 如果不需要刷新，则直接返回现有 token
//       resolve({ accessToken, refreshToken, expiresTime });
//     }
//   });
// }

// function refreshAccessToken(refreshToken) {
//   // 模拟调用刷新 token 的接口
//   return new Promise((resolve, reject) => {
//     uni.request({
//       url: 'https://example.com/api/refreshToken',
//       method: 'POST',
//       data: { refreshToken },
//       success: (res) => {
//         if (res.statusCode === 200 && res.data) {
//           resolve(res.data);
//         } else {
//           reject(new Error('Failed to refresh token'));
//         }
//       },
//       fail: (err) => {
//         reject(err);
//       }
//     });
//   });
// }

// export function scheduleTokenCheck() {
//   let expiresTime = uni.getStorageSync('expiresTime');
//   let currentTime = new Date().getTime();
//   let oneDay = 24 * 60 * 60 * 1000;
//   let timeUntilCheck = expiresTime - currentTime - oneDay;
//   // 清除当前的定时任务
//   if (tokenCheckTimeoutId !== null) {
//     clearTimeout(tokenCheckTimeoutId);
//   }

//   if (timeUntilCheck > 0) {
//     tokenCheckTimeoutId = setTimeout(checkAndRefreshToken, timeUntilCheck);
//   } else {
//     // 立即进行检查
//     checkAndRefreshToken();
//   }
// }

// export function resetTokenCheck() {
//   // 清除当前的定时任务
//   if (tokenCheckTimeoutId !== null) {
//     clearTimeout(tokenCheckTimeoutId);
//     tokenCheckTimeoutId = null;
//   }

//   // 重新开始定时检查
//   scheduleTokenCheck();
// }

// import { scheduleTokenCheck, resetTokenCheck } from './utils.js';

// App({
//   onLaunch() {
//     // 初始化应用时调用
//     scheduleTokenCheck();
//   }
// });

// // 替换 token 完成后再调用
// checkAndRefreshToken().then(tokenData => {
//   console.log('Token is valid or refreshed:', tokenData);
//   resetTokenCheck(); // 重置定时任务
// }).catch(error => {
//   console.error('Error checking or refreshing token:', error);
//   // 可以在这里处理需要重新登录的逻辑
// });

// // 在用户重新登录后调用 resetTokenCheck 来重新开启定时任务
// function onUserLogin() {
//   // 用户登录逻辑
//   // 保存新的 token 和 expiresTime 到本地存储
//   uni.setStorageSync('accessToken', newAccessToken);
//   uni.setStorageSync('refreshToken', newRefreshToken);
//   uni.setStorageSync('expiresTime', newExpiresTime);

//   // 重置定时任务
//   resetTokenCheck();
// }
