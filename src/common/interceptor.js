/**
 * 拦截路由js
 */
import tui from '@/common/httpRequest.js';
import pages from '@/pages.json';
function getLoginUrl() {
    const loggedInPaths = pages.pages.filter(page => page.isLogin === true).map(page => `/${page.path}`);
    return loggedInPaths;
}
uni.addInterceptor('switchTab', {
    invoke(args) {
        console.log(args);
        // request 触发前拼接 url 
        const list =  getLoginUrl();
        if (list.includes(args?.url)) {
            if (!tui.getToken()) {
                uni.navigateTo({
                    url: '/pages/loginOrRegistration/index'
                })
                return false;
            }
        }
    }
})
uni.addInterceptor('navigateTo', {
    invoke(args) {
        const list =  getLoginUrl();
        const url = args?.url.split('?')[0]
        if (list.includes(url)) {
            // 获取当前时间
            if (!tui.getToken()) {
                uni.navigateTo({
                    url: '/pages/loginOrRegistration/index'
                })
                return false;
            }
        }
    }
})

uni.addInterceptor('navigateBack', {
    invoke(args) {
      if (process.env.UNI_PLATFORM === 'h5') {
        window.history.back();
        return false; // 阻止 uni.navigateBack 的默认行为
      }
      // 对于其他环境，允许继续调用 uni.navigateBack
      return true;
    }
  });