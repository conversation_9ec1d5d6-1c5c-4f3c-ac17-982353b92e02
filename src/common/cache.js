const cache = {
    key: 'app_',
    //设置缓存(expire为缓存时效) 单位 秒
    set(key, value, expire) {
        key = this.getKey(key)
        let data = {
            expire: expire ? this.time() + expire : '',
            value
        }

        if (typeof data === 'object') {
            data = JSON.stringify(data)
        }
        try {
            uni.setStorageSync(key, data)
        } catch (e) {
            return undefined
        }
    },
    get(key) {
        key = this.getKey(key)
        try {
            const data = uni.getStorageSync(key)
            if (!data) {
                return undefined
            }
            const { value, expire } = JSON.parse(data)
            console.log("expire",expire)
            if (expire && expire < this.time()) {
                uni.removeStorageSync(key)
                return undefined
            }
            return value
        } catch (e) {
            return undefined
        }
    },
    //获取当前时间
    time() {
        console.log(Math.round(new Date().getTime() / 1000))
        return Math.round(new Date().getTime() / 1000)
    },
    remove(key) {
        key = this.getKey(key)
        uni.removeStorageSync(key)
    },
    get<PERSON>ey(key) {
        return this.key + key
    }
}

export default cache
