import { ref, watch } from "vue";
import { useWebSocket } from "@vueuse/core";
import dayjs from "dayjs";
import tui from './httpRequest';
import { EventBus } from '@/common/eventBus';

export default class WebSocketClient {
    constructor(url, onOpen = null, onMessage = null, onClose = null, reconnectInterval = 5000, pingInterval = 10000) {
        this.url = url;
        this.onOpen = onOpen;
        this.onMessage = onMessage;
        this.onClose = onClose;
        this.reconnectInterval = reconnectInterval;
        this.pingInterval = pingInterval;
        this.messageQueue = []; // 新增消息队列
        this.isManualClose = false;  // 用来标识是否为手动关闭
        this.messageQueue = []; // 新增消息队列
    }

    connect() {
        const { status, data, send, close, open } = useWebSocket(this.url, {
            heartbeat: {
                interval:this.pingInterval,
                pongTimeout:30000
            },
            autoReconnect: {
                delay: this.reconnectInterval,
            },
            // onConnected(ws) {
            //     console.log("WebSocket connected:"+JSON.stringify(ws));
            // },
            // onError(ws,event) {
            //     console.log(ws+"WebSocket onError:"+event);
            // },
            // onDisconnected(ws,event) {
            //     console.log(ws+"WebSocket onDisconnected:"+event);
            // },
            // onMessage(ws,event) {
            //     console.log(ws+"WebSocket onMessage:"+JSON.stringify(event));
            // }
        });

        this.socketStatus = status;
        this.socketData = data;
        this.send = send;
        this.closeConnection = close;
        this.openConnection = open;
        watch(status, (newStatus) => {
            console.log("WebSocket status:", newStatus);
            if (newStatus === 'OPEN') {
               const token = uni.getStorageSync("Token");
               if (token) {
                 const login = {
                   type: "cmd",
                   cmd: 999,
                   data:{
                     token:token
                   }
                 };
                 this.sendMessage(JSON.stringify(login));
               }
               const hash = window.location.hash;
               const code = uni.getStorageSync("WS-TRAN-CURRENCY-CODE");
               if(hash.indexOf('/pages/tabBar/transaction') != -1){
                const transaction = {
                    type: "cmd",
                    cmd: 1000,
                    data:{
                      code:code
                    }
                  };
                  this.sendMessage(JSON.stringify(transaction));
               }

               this.send('ping');
               this.messageQueue.forEach((message) => {
                this.sendMessage(message);
             });
              this.messageQueue = []; // 清空队列
            }
        });
        watch(data, (message) => {
            if (message === 'pong') {
                return;
            }
            const msg = JSON.parse(message);
            if (msg.type === "push" && msg.cmd === 2002) {
                const list = msg.data;
                list.forEach(i=>{
                  // uni.$emit(i.code,i)
                  EventBus.emit(i.code,i);
                })
            }
            if (msg.type === "push" && msg.cmd === 2001) { // 单个交易对
                  let {data} =msg
                  EventBus.emit('transaction-buyseller-list',{asks:data.asks,bids:data.bids,code:data.code});
                  EventBus.emit('transaction-price-info',{code:data.code,openPrice:data.openPrice||'0',price:data.price,closePrice:data.closePrice||'0',highPrice:data.highPrice||'',lowPrice:data.lowPrice||'',volume:data.volume, timestamp:data.ts||data.tick,percentage:data.percentage||''});
            }

            if (msg.type === "push" && msg.cmd === 5000) { // 委托成交
                EventBus.emit('commission',{orderNo:msg.data.orderNo});
            }

            if (msg.type === "push" && msg.cmd === 5001) { // 强平价或者平仓
                EventBus.emit('strongParityOrLiquidation',{recordNo:msg.data.recordNo, positionStatus:msg.data.positionStatus,estimateLiquidatePrice:msg.data.estimateLiquidatePrice});
            }

            if (this.onMessage) {
                this.onMessage(message);
            }
        });
    }

    async sendMessage(message) {
        if (this.socketStatus.value === 'OPEN') {
            if (message === 'ping') {
                this.send(message);
                return;
            }
            const dataMessage = JSON.parse(message);
            const visitorId = await tui.getFingerprintJsVisitorId();
           const account =  uni.getStorageSync("account");
            const msg = {
                ...dataMessage,
                ts: dayjs().unix(),
                fp: visitorId + 'account='+account
            };
            this.send(JSON.stringify(msg));
        } else {
            console.log('WebSocket is not open. Cannot send message:', message);
            if (message === 'ping') {
                //存在ping就不继续push
                const index = this.messageQueue.filter(item => item === 'ping');
                index.length === 0 && this.messageQueue.push(message); // 将消息加入队列
                return;
            }
            this.messageQueue.push(message); // 将消息加入队列
        }
    }

    // 手动打开WebSocket连接
    open() {
        this.isManualClose = false;
        this.openConnection();
    }

    // 修改close方法，添加可选的消息参数和手动关闭标识
    close(message = null) {
        if (message && this.socketStatus.value === 'OPEN') {
            this.send(message); // 发送自定义消息
        }
        this.isManualClose = true; // 标记为手动关闭
        this.closeConnection();
    }
}
