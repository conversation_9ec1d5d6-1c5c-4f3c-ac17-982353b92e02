import { computed, reactive } from "vue";

// config.js
const config = reactive({
  tenantName: uni.getStorageSync('tenantName') || '',
  tenantLogo: uni.getStorageSync('tenantLogo') || '',
  currencyUnit: uni.getStorageSync('currencyUnit') || '',
  currencySymbol: uni.getStorageSync('currencySymbol') || '$',
  assetType: [],//废弃前端写死
  country: [],
  currency: [],
  language: [],
  exchangeRates: [],
  defaultTradePair: {},
  defaultLanguage: '',
  currentRate:computed(()=>{
    return config.exchangeRates.find(item=>item.quoteCurrency===config.currencyUnit)
  })
});
export default config;
