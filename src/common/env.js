export const interfaceUrl = () => {
	return import.meta.env.MODE === "development" ? "https://web.testdev8899.top" : "";
	// return import.meta.env.MODE === 'development' ? 'https://htxdemo.sryeflu.top' : ''
};

export const interWSUrl = () => {
	return import.meta.env.MODE === "development" ? "wss://web.testdev8899.top/ws" : `wss://${location.hostname}/ws`;
	// return import.meta.env.MODE === 'development' ? 'wss://htxdemo.sryeflu.top/ws' : `wss://${location.hostname}/ws`
};
