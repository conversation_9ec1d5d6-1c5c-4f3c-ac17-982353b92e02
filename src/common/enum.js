
import { t } from "@/hooks/useI18n";
import { computed } from "vue";
import tui from "@/common/httpRequest.js";
import { ref, watchEffect } from 'vue';

const tabListFrist = computed(() => {
  return { name: t("自选"), value: -1 };
});



//tab值  不知道为什么这样用computed在页面使用不需要.value不然国际化就失效了
// export const _TabStatus = computed(()=>{
// const list = [
//   {
//     "value": 0,
//     "name": t('加密货币'),
//     "sort": 1
//   },
//   {
//     "value": 2,
//     "name": t("大宗商品"),
//     "sort": 2
//   },
//   {
//     "value": 3,
//     "name": t("外汇"),
//     "sort": 3
//   }
// ]
// debugger
//   if (tui.getToken()) {
//     if(list.filter(item => item.value == -1).length == 0){
//       list.unshift(tabListFrist.value)
//     }
//   } else {
//     if (list.filter((item) => item.value == -1).length > 0) {
//       list.shift();
//     }
//   }
//   return list;
// })

export const _TabStatus = () => {
  const list = [
    {
      "value": 0,
      "name": t('加密货币'),
      "sort": 1
    },
    {
      "value": 2,
      "name": t("大宗商品"),
      "sort": 2
    },
   
  
    {
      "value": 3,
      "name": t("外汇"),
      "sort": 3
    }
  ]
  
  if (tui.getToken()) {
    if (list.filter(item => item.value == -1).length == 0) {
      list.push(tabListFrist.value)
    }
  } else {
    if (list.filter((item) => item.value == -1).length > 0) {
      list.pop();
    }
  }
  return list;
}



export const TabStatus = () => {
  const list = [..._TabStatus()];
  return list;
};