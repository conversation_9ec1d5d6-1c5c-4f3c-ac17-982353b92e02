package com.rf.exchange.server.data.dal.redis;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import com.rf.exchange.server.data.enums.CandleDataSource;

import jakarta.annotation.Resource;

/**
 * 交易对行情监控的redis DAO
 *
 * <AUTHOR>
 * @since 2024-06-22
 */
@Repository
public class CandleDataSourceMonitorRedisDAO {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 更新交易对的最后更新时间
     *
     * @param code 交易对代码
     * @param updateInfo 更新信息 格式：数据源:时间戳
     */
    public void set(String code, String updateInfo) {
        redisTemplate.opsForHash().put(RedisKeyConstants.CANDLE_SOURCE_LAST_UPDATE, code, updateInfo);
    }

    /**
     * 更新所有交易对的
     * 
     * @param values 交易对更新信息 格式: 数据源:时间戳
     */
    public void setAll(Map<String, String> values) {
        redisTemplate.opsForHash().putAll(RedisKeyConstants.CANDLE_SOURCE_LAST_UPDATE, values);
    }

    /**
     * 获取所有交易对的最后更新时间
     *
     * @return 最后更新时间列表
     */
    public Map<String, String> getAll() {
        Map<Object, Object> entries = redisTemplate.opsForHash().entries(RedisKeyConstants.CANDLE_SOURCE_LAST_UPDATE);
        Map<String, String> result = new HashMap<>();
        for (Map.Entry<Object, Object> entry : entries.entrySet()) {
            result.put((String)entry.getKey(), (String)entry.getValue());
        }
        return result;
    }

    /// **
    // * 获取指定交易对id的最后更新时间
    // *
    // * @param tradeId 交易对id
    // * @return 最后更新时间
    // */
    // public Integer get(Long tradeId) {
    // Object val = redisTemplate.opsForHash().get(RedisKeyConstants.CANDLE_SOURCE_LAST_UPDATE, tradeId);
    // return val == null ? 0 : (Integer) val;
    // }

    public void updateLastPushTime(Integer candleDataSource) {
        redisTemplate.opsForHash().put(RedisKeyConstants.CANDLE_SOURCE_LAST_PUSH_UPDATE,
            String.valueOf(candleDataSource), System.currentTimeMillis());
    }

    public Long getLastPushTime(CandleDataSource candleDataSource) {
        Object lastPushTime = redisTemplate.opsForHash().get(RedisKeyConstants.CANDLE_SOURCE_LAST_PUSH_UPDATE,
            String.valueOf(candleDataSource.getValue()));
        if (Objects.nonNull(lastPushTime)) {
            return (Long)lastPushTime;
        }
        return null;
    }
}
