package com.rf.exchange.server.data.core.websocket.container;

import com.rf.exchange.module.candle.config.CandleDataSourceProperties;
import com.rf.exchange.server.data.datasource.alltick.websocket.AllTickCandleIdMessage;
import com.rf.exchange.server.data.datasource.alltick.websocket.AllTickMessageQueue;
import com.rf.exchange.server.data.datasource.alltick.websocket.AllTickWSMessage;
import com.rf.exchange.server.data.datasource.alltick.websocket.AllTickWebSocketHandler;
import com.rf.exchange.server.data.enums.CandleDataSource;
import jakarta.annotation.PreDestroy;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.client.WebSocketClient;
import org.springframework.web.socket.client.WebSocketConnectionManager;
import org.springframework.web.socket.client.standard.StandardWebSocketClient;

/**
 * <AUTHOR>
 * @since 2024-06-27
 */
@Slf4j
@Data
@Component
public class CandleWebSocketManagerContainer {

    private WebSocketConnectionManager allTickSocketConnectionManager;
    private WebSocketClient alltickWSClient = new StandardWebSocketClient();

    @Resource
    private CandleDataSourceProperties properties;
    @Resource
    private AllTickWebSocketHandler allTickWebSocketHandler;
    @Resource
    private AllTickMessageQueue allTickMessageQueue;

    /**
     * 获取指定数据源的connection Manager
     *
     * @param dataSource 数据源
     * @return 返回connection manager，可能为空
     */
    public WebSocketConnectionManager getManager(CandleDataSource dataSource) {
        return createManagerIfNeed(dataSource);
    }

    /**
     * 如果需要则创建datasource的connectionManager
     *
     * @param dataSource 数据源
     * @return 返回connection manager，可能为空
     */
    public WebSocketConnectionManager createManagerIfNeed(CandleDataSource dataSource) {
        if (CandleDataSource.ALLTICK.equals(dataSource)) {
            if (!properties.getAlltick().getEnable()) {
                log.info("数据源 AllTick 没有启用");
                return null;
            }
            if (allTickSocketConnectionManager == null) {
                CandleDataSourceProperties.AllTickDataSource propertiesAllTick = properties.getAlltick();
                String url = propertiesAllTick.getWsHost() + "?token=" + propertiesAllTick.getSecret();
                this.allTickSocketConnectionManager = new WebSocketConnectionManager(alltickWSClient, allTickWebSocketHandler, url);
            }
            return this.allTickSocketConnectionManager;
        }
        return null;
    }

    /**
     * 任意数据源的manager是否已连接
     *
     * @return true:已连接
     */
    public boolean isAnyManagerConnected() {
        for (CandleDataSource dataSource : CandleDataSource.values()) {
            if (isManagerConnected(dataSource)) {
                return true;
            }
        }
        return false;
    }

    public void stopManager(CandleDataSource dataSource) {
        if (CandleDataSource.ALLTICK.equals(dataSource) && allTickSocketConnectionManager != null) {
            allTickSocketConnectionManager.stop(() -> {
                allTickSocketConnectionManager = null;
            });
        }
    }

    /**
     * datasource的manager是否连接成功了
     *
     * @param dataSource 数据源
     * @return true:已连接
     */
    public boolean isManagerConnected(CandleDataSource dataSource) {
        if (CandleDataSource.ALLTICK.equals(dataSource) && allTickSocketConnectionManager != null) {
            return allTickSocketConnectionManager.isConnected();
        }
        return false;
    }

    /**
     * 发送消息
     *
     * @param dataSource 数据源
     * @param message 消息对象
     */
    public void sendMessage(CandleDataSource dataSource, AllTickCandleIdMessage message) {
        if (CandleDataSource.ALLTICK.equals(dataSource) && allTickWebSocketHandler != null) {
            allTickMessageQueue.put(dataSource, (AllTickWSMessage) message);
        }
    }

    @PreDestroy
    public void destroy() throws Exception {
        stopManager(CandleDataSource.ALLTICK);
    }
}
