package com.rf.exchange.server.data.config;

import com.rf.exchange.module.candle.dal.redis.CandleTodayKlinePriceRedisDAO;
import com.rf.exchange.server.data.context.TodayKlineHolder;
import com.rf.exchange.server.data.datasource.alltick.websocket.AllTickWebSocketHandler;
import com.rf.exchange.server.data.enums.CandleDataSource;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @since 2024-06-27
 */
@EnableAsync
@Configuration
@EnableConfigurationProperties(CandleDataSyncProperties.class)
public class CandleDataConfig {

    /**
     * 线程池大小
     */
    private static final int POOL_SIZE = Runtime.getRuntime().availableProcessors();

    @Bean
    public AllTickWebSocketHandler allTickWebSocketHandler() {
        return new AllTickWebSocketHandler(CandleDataSource.ALLTICK);
    }

    @Bean("websocketProcessExecutor")
    public ThreadPoolTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(POOL_SIZE);
        executor.setMaxPoolSize(POOL_SIZE * 2);
        executor.setQueueCapacity(500);
        executor.setKeepAliveSeconds(15);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardPolicy());
        executor.setThreadNamePrefix("websocket-message-task");
        executor.initialize();
        return executor;
    }

    @Bean("websocketTaskScheduler")
    public ThreadPoolTaskScheduler websocketTaskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(1);
        scheduler.setThreadNamePrefix("websocket-send-");
        scheduler.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        scheduler.initialize();
        return scheduler;
    }

    @Bean("historyCandleSyncTaskScheduler")
    public ThreadPoolTaskScheduler historyCandleSyncGuardScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(1);
        scheduler.setThreadNamePrefix("candle-sync-history-scheduler-");
        return scheduler;
    }

    @Bean("realTimeCandleSyncTaskScheduler")
    public ThreadPoolTaskScheduler realTimeCandleSyncGuardScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(1);
        scheduler.setThreadNamePrefix("candle-sync-realtime-scheduler-");
        return scheduler;
    }

    @Bean("polygonRealTimeCandleSyncScheduler")
    public ThreadPoolTaskScheduler polygonRealTimeCandleSyncScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(1);
        scheduler.setThreadNamePrefix("candle-sync-realtime-scheduler-polygon-");
        return scheduler;
    }

    @Bean("polygonHistoryCandleSyncScheduler")
    public ThreadPoolTaskScheduler polygonHistoryCandleSyncScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(1);
        scheduler.setThreadNamePrefix("candle-sync-realtime-scheduler-polygon-");
        return scheduler;
    }

    @Bean
    public CloseableHttpClient httpClient() {
        return HttpClients.createDefault();
    }

    @Bean
    @SuppressWarnings("InstantiationOfUtilityClass")
    public TodayKlineHolder todayKlineHolder(CandleTodayKlinePriceRedisDAO redisDAO) {
        TodayKlineHolder.initialize(redisDAO);
        return new TodayKlineHolder();
    }

    @Bean("marketCloseGeneratorTaskScheduler")
    public ThreadPoolTaskScheduler marketCloseGeneratorTaskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(1);
        scheduler.setThreadNamePrefix("candle-market-close-scheduler-");
        scheduler.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        scheduler.initialize();
        return scheduler;
    }
}
