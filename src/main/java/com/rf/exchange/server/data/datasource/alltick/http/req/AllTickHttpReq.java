package com.rf.exchange.server.data.datasource.alltick.http.req;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024-07-22
 */
@Data
public class AllTickHttpReq<T extends AllTickParamData> {
    // 追踪码
    private String trace;
    private T data;

    public Map<String, Object> getParams() {
        Map<String, Object> param = new HashMap<>(2);
        param.put("trace", trace);
        param.put("data", data.getMap());
        return param;
    }
}
