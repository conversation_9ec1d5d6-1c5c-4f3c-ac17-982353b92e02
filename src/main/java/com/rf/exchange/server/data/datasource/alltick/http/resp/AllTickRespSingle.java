package com.rf.exchange.server.data.datasource.alltick.http.resp;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-07-22
 */
@Data
public class AllTickRespSingle {
    private int ret;
    private String msg;
    private String trace;
    private DataPojo data;

    @Data
    public static class DataPojo {
        private String code;
        private Integer klineType;
        private List<AllTickKLine> klineList;
    }
}
