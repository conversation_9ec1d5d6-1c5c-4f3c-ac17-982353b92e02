package com.rf.exchange.server.data;

import com.rf.exchange.server.data.datasource.alltick.util.AllTickUtil;
import com.rf.exchange.server.data.datasource.jiangshan.util.JiangShanUtil;
import com.rf.exchange.server.data.datasource.polygon.http.PolygonService;
import com.rf.exchange.server.data.service.CandleDataSyncService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * <AUTHOR>
 * @since 2024-06-05
 */
@Slf4j
@SuppressWarnings("SpringComponentScan") // 忽略 IDEA 无法识别 ${exchange.info.base-package}
@SpringBootApplication(
    scanBasePackages = {"${exchange.info.base-package}.server", "${exchange.info.base-package}.module"})
public class ExchangeDataApplication implements CommandLineRunner {

    @Resource
    private CandleDataSyncService candleDataSyncService;
    @Resource
    private PolygonService polygonService;

    public static void main(String[] args) {
        SpringApplication.run(ExchangeDataApplication.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
        AllTickUtil.init();
        JiangShanUtil.init();
        // 初始化数据库表
        candleDataSyncService.createCandleDataTable();
        // 请求一次Polygon的TickerType
        polygonService.fetchPolygonTickerTypeAll();
        //
        polygonService.connect();
    }
}
