package com.rf.exchange.server.data.datasource.alltick.http.resp;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.rf.exchange.module.candle.enums.CandleTimeRangeEnum;
import com.rf.exchange.module.candle.enums.CandleTimeTypeEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-07-22
 */
@Data
public class AllTickKLine {
    /**
     * 该K线时间戳
     */
    private String timestamp;
    /**
     * 该K线开盘价
     */
    private String openPrice;
    /**
     * 该K线收盘价
     */
    private String closePrice;
    /**
     * 该K线最高价
     */
    private String highPrice;
    /**
     * 该K线最低价
     */
    private String lowPrice;
    /**
     * 该K线成交数量
     */
    private String volume;
    /**
     * 该K线成交金额
     */
    private String turnover;

    @JsonIgnore
    private Integer timeRange;
    @JsonIgnore
    private Integer timeType;
}
