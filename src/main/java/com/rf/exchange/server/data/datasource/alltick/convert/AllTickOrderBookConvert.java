package com.rf.exchange.server.data.datasource.alltick.convert;

import com.rf.exchange.module.candle.dal.redis.dto.CandleOrderBookDTO;
import com.rf.exchange.module.candle.dal.redis.dto.CandleOrderBookListDTO;
import com.rf.exchange.server.data.datasource.alltick.dto.AllTickPKQuoteDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2024-07-03
 */
@Mapper
public interface AllTickOrderBookConvert {

    AllTickOrderBookConvert INSTANCE = Mappers.getMapper(AllTickOrderBookConvert.class);

    CandleOrderBookDTO convert(AllTickPKQuoteDTO.OrderBook orderBook);

    CandleOrderBookListDTO convert2(AllTickPKQuoteDTO allTickPKQuoteDTO);
}
