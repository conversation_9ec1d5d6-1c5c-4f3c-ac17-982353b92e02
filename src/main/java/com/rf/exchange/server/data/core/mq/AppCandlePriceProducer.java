package com.rf.exchange.server.data.core.mq;

import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.rf.exchange.server.data.core.mq.message.CandlePriceMessage;
import com.rf.exchange.server.data.datasource.alltick.dto.AllTickDealQuoteDTO;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * APP服务中交易对价格消息的rocketMQ生产者
 *
 * <AUTHOR>
 * @since 2024-08-29
 */
@Slf4j
@Service
public class AppCandlePriceProducer {

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    @Value("${exchange.rocketmq.candle.topic}")
    private String topic;

    @Value("${exchange.rocketmq.candle.tag-price}")
    private String tagPrice;

    @Value("${exchange.rocketmq.candle.tag-price-custom}")
    private String tagPriceCustom;

    public void sendAllTickPriceMessage(String tradePairCode, AllTickDealQuoteDTO quoteDTO) {
        CandlePriceMessage mqMessage = CandlePriceMessage.builder().code(tradePairCode).price(quoteDTO.getPrice())
            .timestamp(quoteDTO.getTickTime()).volume(quoteDTO.getVolume()).turnover(quoteDTO.getTurnover()).build();
        sendPriceMessage(tagPrice, mqMessage);
    }

    /**
     * 发送mq通用方法
     * 
     * @param message
     */
    public void sendPriceMessage(CandlePriceMessage message) {
        sendPriceMessage(tagPrice, message);
    }

    public void sendCustomTradePriceMessage(CandlePriceMessage message) {
        sendPriceMessage(tagPriceCustom, message);
    }

    private void sendPriceMessage(String tag, CandlePriceMessage priceMessage) {
        String dest = topic + ":" + tag;
        SendResult sendResult = rocketMQTemplate.syncSend(dest, priceMessage);
        if (sendResult.getSendStatus() != SendStatus.SEND_OK) {
            log.error("RocketMQ 转发交易对价格消息到APP主题 失败 {}", sendResult.getSendStatus());
        }
    }
}
