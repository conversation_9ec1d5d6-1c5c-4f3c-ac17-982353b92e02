package com.rf.exchange.server.data.datasource.jiangshan.http;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;

import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.math.NumberUtils;
import org.dromara.hutool.core.net.url.UrlEncoder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.google.common.collect.Maps;
import com.rf.exchange.module.candle.api.dto.TodayKlinePriceDTO;
import com.rf.exchange.module.candle.dal.redis.CandleTodayKlinePriceRedisDAO;
import com.rf.exchange.module.candle.enums.CandleTimeRangeEnum;
import com.rf.exchange.module.exc.api.tradepair.dto.TradePairRespDTO;
import com.rf.exchange.server.data.context.TodayKlineHolder;
import com.rf.exchange.server.data.datasource.alltick.http.resp.AllTickKLine;
import com.rf.exchange.server.data.datasource.jiangshan.dto.CurbarDTO;
import com.rf.exchange.server.data.datasource.jiangshan.dto.KineHistoryDTO;
import com.rf.exchange.server.data.datasource.jiangshan.dto.KineItemDTO;
import com.rf.exchange.server.data.datasource.jiangshan.util.JiangShanTradePairStatusHolder;
import com.rf.exchange.server.data.datasource.jiangshan.util.JiangShanUtil;
import com.rf.exchange.server.data.service.CandleDataSyncService;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * @description:
 * @author: Petter
 * @create: 2024-10-09
 **/
@Component
@Slf4j
public class JiangShanHttpClient {
    @Value("${exchange.candle-datasource.jiangShan.host:'http://f-test.js-stock.top'}")
    private String reqHost;

    @Value("${exchange.candle-datasource.jiangShan.secret:1KcHQ8nPaicZeqIsnlOE}")
    private String reqSecret;

    @Resource
    private CandleDataSyncService dataSyncService;
    @Resource
    private CandleTodayKlinePriceRedisDAO todayKlinePriceRedisDAO;

    private final Map<String, Map<CandleTimeRangeEnum, List<KineItemDTO>>> kineDataCachedMap = Maps.newHashMap();

    public void sendGetRequest(TradePairRespDTO tradePairRespDTO) {
        String productCode = JiangShanUtil.getProductCode(tradePairRespDTO.getCode());
        for (CandleTimeRangeEnum rangeEnum : CandleTimeRangeEnum.values()) {
            if (rangeEnum != CandleTimeRangeEnum.HOUR_TWO || rangeEnum != CandleTimeRangeEnum.MONTH_ONE) {
                String resp = HttpUtil.get(getHistoryReqUrl(productCode, getPeriodByTimeRange(rangeEnum)));
                try {
                    KineHistoryDTO kineHistoryDTO = JSONUtil.toBean(resp, KineHistoryDTO.class);
                    if (kineHistoryDTO.getCode() == 1) {

                        // 此处过滤可不用，在保存数据时候有根据条件：timestamp,range,rangeType做判断保存还是更新
                        List<KineItemDTO> kineItemList = kineHistoryDTO.getList();
                        if (CollectionUtil.isNotEmpty(kineItemList)) {
                            // Map<CandleTimeRangeEnum, List<KineItemDTO>> productCodeTimeRangeEnumListMap =
                            // kineDataCachedMap.get(productCode);
                            // List<KineItemDTO> unSavedKineItemList = null;
                            // if (CollectionUtil.isNotEmpty(productCodeTimeRangeEnumListMap)) {
                            // List<KineItemDTO> kineItemListCached = productCodeTimeRangeEnumListMap.get(rangeEnum);
                            // if (CollectionUtil.isNotEmpty(kineItemListCached)) {
                            // unSavedKineItemList = this.getUnSavedKineItemList(kineItemList, kineItemListCached);
                            // } else {
                            // unSavedKineItemList = kineItemList;
                            // }
                            //
                            // } else {
                            // productCodeTimeRangeEnumListMap = Maps.newHashMap();
                            // kineDataCachedMap.put(productCode, productCodeTimeRangeEnumListMap);
                            // unSavedKineItemList = kineItemList;
                            // }
                            // // 重置列表
                            // productCodeTimeRangeEnumListMap.put(rangeEnum, kineItemList);

                            // 根据1分钟k线的时间戳判断该交易对是否已经休市
                            if (rangeEnum == CandleTimeRangeEnum.MIN_ONE) {
                                JiangShanTradePairStatusHolder.updateStatus(tradePairRespDTO.getCode(),
                                    kineHistoryDTO.getCurbar().getTimestamp());
                            }

                            // 今日k线数据处理
                            if (rangeEnum == CandleTimeRangeEnum.DAY_ONE) {
                                final TodayKlinePriceDTO todayKline =
                                    this.convertToTodayKinePrice(kineHistoryDTO.getCurbar());
                                todayKline.setTradePairCode(tradePairRespDTO.getCode());
                                todayKlinePriceRedisDAO.update(tradePairRespDTO.getCode(), todayKline);
                                // 更新Holder中的今日开盘价
                                TodayKlineHolder.updateTodayOpenPriceMap(tradePairRespDTO.getCode(), todayKline);
                            }

                            // 24小小时交易量处理
                            if (rangeEnum == CandleTimeRangeEnum.HOUR_ONE) {
                                BigDecimal volume24H = BigDecimal.ZERO;
                                List<KineItemDTO> last24HourKineItemList = kineItemList.reversed();
                                // 取24条每小时的k线
                                if (last24HourKineItemList.size() > 24) {
                                    last24HourKineItemList = last24HourKineItemList.subList(0, 24);
                                }
                                for (KineItemDTO line : last24HourKineItemList) {
                                    volume24H = volume24H.add(new BigDecimal(line.getVolume()));
                                }
                                final TodayKlinePriceDTO updateKline = new TodayKlinePriceDTO();
                                updateKline.setTradePairCode(tradePairRespDTO.getCode());
                                updateKline.setVolume24H(volume24H.toString());
                                // 更新24小时成交量
                                todayKlinePriceRedisDAO.update(tradePairRespDTO.getCode(), updateKline);
                            }

                            // 保存k线数据
                            dataSyncService.insertCandleRecords(tradePairRespDTO.getCode(), rangeEnum,
                                this.convertToAllTickKLine(kineItemList, rangeEnum));
                        }

                    }
                } catch (Exception e) {
                    log.error("获取匠山历史K线异常:{}", e.getMessage());
                }

            }
        }
    }

    // 数据转换
    private List<AllTickKLine> convertToAllTickKLine(List<KineItemDTO> unSavedKineItemList,
        CandleTimeRangeEnum rangeEnum) {
        List<AllTickKLine> allTickKLineList = Lists.newArrayList();
        for (KineItemDTO kineItemDTO : unSavedKineItemList) {
            AllTickKLine allTickKLine = new AllTickKLine();
            allTickKLine.setTimestamp(String.valueOf(kineItemDTO.getTimestamp()));
            allTickKLine.setOpenPrice(kineItemDTO.getOpen());
            allTickKLine.setClosePrice(kineItemDTO.getClose());
            allTickKLine.setLowPrice(kineItemDTO.getLow());
            allTickKLine.setHighPrice(kineItemDTO.getHigh());
            allTickKLine.setVolume(kineItemDTO.getVolume());
            allTickKLine.setTimeRange(rangeEnum.getRange());
            allTickKLine.setTimeType(rangeEnum.getTypeValue());
            BigDecimal turnOver = (new BigDecimal(kineItemDTO.getLow()).add(new BigDecimal(kineItemDTO.getHigh())))
                .divide(new BigDecimal(2)).multiply(new BigDecimal(kineItemDTO.getVolume()))
                .setScale(4, RoundingMode.HALF_UP);
            allTickKLine.setTurnover(String.valueOf(turnOver));
            allTickKLineList.add(allTickKLine);
        }
        return allTickKLineList;
    }

    private TodayKlinePriceDTO convertToTodayKinePrice(CurbarDTO curbarDTO) {
        TodayKlinePriceDTO todayKlinePriceDTO = new TodayKlinePriceDTO();
        todayKlinePriceDTO.setClosePrice(curbarDTO.getClose());
        todayKlinePriceDTO.setOpenPrice(curbarDTO.getOpen());
        todayKlinePriceDTO.setHighPrice(curbarDTO.getHigh());
        todayKlinePriceDTO.setLowPrice(curbarDTO.getLow());
        todayKlinePriceDTO.setTimestamp(String.valueOf(curbarDTO.getTimestamp()));
        todayKlinePriceDTO.setVolume(curbarDTO.getVolume());
        todayKlinePriceDTO.setTurnover(String.valueOf(
            new BigDecimal(curbarDTO.getHigh()).add(new BigDecimal(curbarDTO.getLow())).divide(new BigDecimal(2))
                .multiply(new BigDecimal(curbarDTO.getVolume())).setScale(4, RoundingMode.HALF_UP)));

        return todayKlinePriceDTO;
    }

    private List<KineItemDTO> getUnSavedKineItemList(List<KineItemDTO> kineItemList,
        List<KineItemDTO> kineItemListCached) {
        List<KineItemDTO> unSavedList = Lists.newArrayList();
        for (KineItemDTO newItem : kineItemList) {
            boolean unSaved = true;
            for (KineItemDTO oldItem : kineItemListCached) {
                if (newItem.getTimestamp().equals(oldItem.getTimestamp())) {
                    unSaved = false;
                    break;
                }
            }
            if (unSaved) {
                unSavedList.add(newItem);
            }
        }
        return unSavedList;
    }

    /**
     *
     * @param productCode
     * @param period 1分钟就是1 目前支持 1分钟 5分钟 15分钟 30分钟 1小时 4小时 1天 1周 传对应的分钟数就好了 1天是 传1440
     * @return
     */
    private String getHistoryReqUrl(String productCode, Integer period) {
        return UrlEncoder.encodeQuery(
            reqHost + "/getkine?symbol=" + productCode + "&cmd=history&period=" + period + "&key=" + reqSecret);
    }

    private String getQuoteReqUrl(String productCode) {
        return UrlEncoder.encodeQuery(reqHost + "/getkine?symbol=" + productCode + "&cmd=quote&key=" + reqSecret);
    }

    private Integer getPeriodByTimeRange(CandleTimeRangeEnum candleTimeRangeEnum) {
        switch (candleTimeRangeEnum) {
            case MIN_ONE:
                return 1;
            case MIN_FIVE:
                return 5;
            case MIN_FIFTEEN:
                return 15;
            case MIN_THIRTY:
                return 30;
            case HOUR_ONE:
                return 60;
            case HOUR_TWO:
                // 不支持
                break;
            case HOUR_FOUR:
                return 240;
            case DAY_ONE:
                return 1440;
            case WEEK_ONE:
                return 10080;
            case MONTH_ONE:
                // 不支持
                break;
            default:
                break;
        }
        return NumberUtils.INTEGER_ONE;
    }

}
