package com.rf.exchange.server.data.datasource.alltick.http;

import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.google.common.base.Joiner;
import com.rf.exchange.framework.common.util.json.JsonUtils;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.xxljob.core.log.XJLog;
import com.rf.exchange.module.candle.api.dto.TodayKlinePriceDTO;
import com.rf.exchange.module.candle.config.CandleDataSourceProperties;
import com.rf.exchange.module.candle.dal.redis.CandleTodayKlinePriceRedisDAO;
import com.rf.exchange.module.candle.enums.CandleTimeRangeEnum;
import com.rf.exchange.module.candle.enums.CandleTimeTypeEnum;
import com.rf.exchange.server.data.context.TodayKlineHolder;
import com.rf.exchange.server.data.core.http.executor.CandleSyncGuardTaskExecutor;
import com.rf.exchange.server.data.dal.dataobject.CandleLastSyncDO;
import com.rf.exchange.server.data.datasource.alltick.http.req.AllTickHttpReq;
import com.rf.exchange.server.data.datasource.alltick.http.req.AllTickParamDataBatchKLine;
import com.rf.exchange.server.data.datasource.alltick.http.req.AllTickParamDataSingleKLine;
import com.rf.exchange.server.data.datasource.alltick.http.resp.AllTickRespBatch;
import com.rf.exchange.server.data.datasource.alltick.http.resp.AllTickKLine;
import com.rf.exchange.server.data.datasource.alltick.http.resp.AllTickRespSingle;
import com.rf.exchange.server.data.datasource.alltick.util.AllTickUtil;
import com.rf.exchange.server.data.dto.DataSyncRequestDTO;
import com.rf.exchange.server.data.service.CandleDataSyncService;
import com.rf.exchange.server.data.service.CandleLastSyncService;
import com.xxl.job.core.context.XxlJobHelper;
import jakarta.annotation.Resource;
import org.apache.hc.client5.http.classic.methods.HttpGet;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.core5.http.HttpStatus;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.apache.hc.core5.http.io.entity.StringEntity;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2024-07-22
 */
@Component
public class AllTickHttpClient {

    private static final ObjectMapper MAPPER = new ObjectMapper().setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);

    @Resource
    private CandleDataSourceProperties properties;
    @Resource
    private CloseableHttpClient httpClient;
    @Resource
    private CandleDataSyncService dataSyncService;
    @Resource
    private CandleLastSyncService lastSyncService;
    @Resource
    private CandleTodayKlinePriceRedisDAO todayKlinePriceRedisDAO;

    /**
     * 发送单产品k线查询(用于拉取历史k线)
     *
     * @param requestDTO 请求信息
     */
    public void sendGetRequest(DataSyncRequestDTO requestDTO) {
        CandleDataSourceProperties.AllTickDataSource alltickProperties = properties.getAlltick();
        try {
            AllTickHttpReq<AllTickParamDataSingleKLine> httpParam = getSingleKLineParamData(requestDTO);
            String json = JsonUtils.toJsonString(httpParam.getParams());
            XJLog.info("AllTick 发送k线查询请求 [单个K线] json参数:{}", json);

            final String responseBody = internalSendHttp(alltickProperties.getSecret(), alltickProperties.getHost(), alltickProperties.getSingleKLine(), json, false);
            if (responseBody == null) {
                XJLog.error("请求失败 响应转json为空 body:{}", responseBody);
                return;
            }
            AllTickRespSingle kLineResponse = MAPPER.readValue(responseBody, AllTickRespSingle.class);
            if (kLineResponse == null) {
                XJLog.log("请求响应数据为null 拉取k线数据异常");
                return;
            }
            if (kLineResponse.getRet() != 200) {
                XJLog.error("请求失败 ret:{} trace:{}", kLineResponse.getRet(), kLineResponse.getTrace());
                return;
            }

            long reqEndTime = requestDTO.getEndTS();
            int reqBarCount = requestDTO.getBarCount();
            CandleTimeRangeEnum timeRangeEnum = requestDTO.getTimeRangeEnum();
            List<AllTickKLine> kLineList = kLineResponse.getData().getKlineList();

            // 插入k线到数据库中
            dataSyncService.insertCandleRecords(requestDTO.getTradePairCode(), requestDTO.getTimeRangeEnum(), kLineList);

            // 如果没有任何一条数据返回，需要检查交易对是否是休市
            if (kLineList.isEmpty() && timeRangeEnum.equals(CandleTimeRangeEnum.DAY_ONE) || kLineList.size() >= reqBarCount) {
                // 标记请求参数中的k线类型的数据已经拉取完成了，如果所有类型的k线类型都拉取完成了则更新交易对的最后数据同步时间
                boolean isAllFinished = CandleSyncGuardTaskExecutor.setAndReturn(reqEndTime, httpParam.getData().getKlineType(), !requestDTO.isNeedRequestMinKline());
                if (isAllFinished) {
                    if (requestDTO.isNeedUpdateLastSyncDate()) {
                        final CandleLastSyncDO syncRecord = new CandleLastSyncDO();
                        syncRecord.setCode(requestDTO.getTradePairCode());
                        syncRecord.setLastSyncDate(requestDTO.getStartTime());
                        lastSyncService.updateLastSyncRecord(syncRecord);
                    }
                    XJLog.info("{} 所有请求类型的k线拉取数据请求都已经完成", requestDTO.getTradePairCode());
                    // 移除时间戳关联的BitSet
                    CandleSyncGuardTaskExecutor.removeBitByTS(reqEndTime);
                }
            }

        } catch (IOException e) {
            XJLog.error("AllTick 发送k线查询请求失败 {}", e.getMessage());
            XxlJobHelper.log(e);
        }
    }

    /**
     * 发送批量k线查询请求
     *
     * @param requestDTO 请求信息
     */
    public void sendBatchGetRequest(DataSyncRequestDTO requestDTO) {
        CandleDataSourceProperties.AllTickDataSource alltickProperties = properties.getAlltick();
        try {
            AllTickHttpReq<AllTickParamDataBatchKLine> httpParam = getBatchKLineParamData(requestDTO);

            String json = JsonUtils.toJsonString(httpParam.getParams());
            XJLog.info("AllTick 发送k线查询请求 [批量k线] json参数:{}", json);

            final String responseBody = internalSendHttp(alltickProperties.getSecret(), alltickProperties.getHost(), alltickProperties.getBatchKLine(), json, true);
            if (responseBody == null) {
                XJLog.error("请求失败 响应转json为空 body:{}", responseBody);
                return;
            }
            AllTickRespBatch klineResponse = MAPPER.readValue(responseBody, AllTickRespBatch.class);
            if (klineResponse == null) {
                XJLog.log("请求响应数据为null 拉取k线数据异常");
                return;
            }
            if (klineResponse.getRet() != 200) {
                XJLog.error("请求失败 ret:{} trace:{}", klineResponse.getRet(), klineResponse.getTrace());
                return;
            }

            final List<AllTickRespBatch.KlineListItem> klineList = klineResponse.getData().getKlineList();
            List<AllTickKLine> tkLines = new ArrayList<>();

            // 记录日线级别的昨日收盘价格
            for (AllTickRespBatch.KlineListItem item : klineList) {
                final List<AllTickKLine> lines = item.getKlineData();
                if (lines.isEmpty()) {
                    continue;
                }
                String productCode = item.getCode();
                final String tradePairCode = AllTickUtil.getTradePairCode(productCode);

                int klineType = item.getKlineType();
                // 如果是日线级别的数据
                if (CandleTimeRangeEnum.DAY_ONE.getKLineTypeAllTick() == klineType) {
                    // 如果k线的时间戳是在昨日时间戳的范围内则表示是昨日的k线
                    final AllTickKLine kLine = lines.getFirst();
                    if (kLine != null) {
                        final TodayKlinePriceDTO todayKline = BeanUtils.toBean(kLine, TodayKlinePriceDTO.class);
                        todayKline.setTradePairCode(tradePairCode);
                        todayKlinePriceRedisDAO.update(requestDTO.getTradePairCode(), todayKline);
                        // 更新Holder中的今日开盘价
                        TodayKlineHolder.updateTodayOpenPriceMap(requestDTO.getTradePairCode(), todayKline);
                    }
                } else if (CandleTimeRangeEnum.HOUR_ONE.getKLineTypeAllTick() == klineType) {
                    if (CollUtil.isNotEmpty(item.getKlineData())) {
                        // 今日k线统计项
                        BigDecimal volume24H = BigDecimal.ZERO;
                        for (AllTickKLine line : lines) {
                            volume24H = volume24H.add(new BigDecimal(line.getVolume()));
                        }
                        final TodayKlinePriceDTO updateKline = new TodayKlinePriceDTO();
                        updateKline.setTradePairCode(tradePairCode);
                        updateKline.setVolume24H(volume24H.toString());
                        // 更新24小时成交量
                        todayKlinePriceRedisDAO.update(requestDTO.getTradePairCode(), updateKline);
                    }
                }

                for (AllTickKLine line : item.getKlineData()) {
                    CandleTimeRangeEnum timeRangeEnum = CandleTimeRangeEnum.enumOfAllTickLineType(item.getKlineType());
                    if (timeRangeEnum != null) {
                        line.setTimeType(timeRangeEnum.getTypeValue());
                        line.setTimeRange(timeRangeEnum.getRange());
                        tkLines.add(line);
                    }
                }
            }

            // 插入k线到数据库中
            dataSyncService.insertCandleRecords(requestDTO.getTradePairCode(), requestDTO.getTimeRangeEnum(), tkLines);
        } catch (IOException e) {
            XJLog.error("AllTick 发送k线查询请求失败 {}", e.getMessage());
            XxlJobHelper.log(e);
        }
    }

    private String internalSendHttp(String secret, String host, String path, String queryJson, boolean isSendGetBody) throws IOException {

        Map<String, String> queryMap = new HashMap<>(2);
        queryMap.put("token", secret);
        if (!isSendGetBody) {
            // 不使用body携带请求参数的情况下，需要对请求参数做encode，否则无法请求成功
            String encodeJson = URLEncoder.encode(queryJson, StandardCharsets.UTF_8);
            queryMap.put("query", encodeJson);
        }
        String paramStr = Joiner.on("&").withKeyValueSeparator("=").join(queryMap);

        String url = host + path + "?" + paramStr;
        HttpGet httpGet = new HttpGet(url);
        if (isSendGetBody) {
            httpGet.setEntity(new StringEntity(queryJson));
            httpGet.setHeader("Accept", "application/json");
            httpGet.setHeader("Content-type", "application/json");
        }
        return httpClient.execute(httpGet, response -> {
            XJLog.info("收到请求响应 {}", response);
            if (response.getCode() != HttpStatus.SC_OK) {
                XJLog.error("请求失败 http状态码:{} 原因:{}", response.getCode(), response.getReasonPhrase());
                return null;
            }
            if (response.getEntity() == null) {
                XJLog.error("请求返回异常 entity 为空");
                return null;
            }
            return EntityUtils.toString(response.getEntity());
        });
    }

    /**
     * 构建批量k线查询的参数
     *
     * @param requestDTO 请求信息
     * @return 参数
     */
    private static AllTickHttpReq<AllTickParamDataBatchKLine> getBatchKLineParamData(DataSyncRequestDTO requestDTO) {
        // 转换TradePairCode为AllTick的产品code
        String productCode = AllTickUtil.getProductCode(requestDTO.getTradePairCode());
        AllTickHttpReq<AllTickParamDataBatchKLine> httpParam = new AllTickHttpReq<>();

        final ArrayList<AllTickParamDataSingleKLine> kLines = new ArrayList<>(requestDTO.getTimeRangeEnums().size());
        for (CandleTimeRangeEnum anEnum : requestDTO.getTimeRangeEnums()) {
            final AllTickParamDataSingleKLine singleKLine = new AllTickParamDataSingleKLine();
            singleKLine.setCode(productCode);
            // 如果是分钟级别的k线则获取5条
            if (anEnum.getType().equals(CandleTimeTypeEnum.MINUTES)) {
                if (anEnum.getRange().equals(CandleTimeRangeEnum.MIN_ONE.getRange())) {
                    singleKLine.setQueryKlineNum(60);
                } else if (anEnum.getRange().equals(CandleTimeRangeEnum.MIN_FIVE.getRange())) {
                    singleKLine.setQueryKlineNum(12);
                } else if (anEnum.getRange().equals(CandleTimeRangeEnum.MIN_FIFTEEN.getRange())) {
                    singleKLine.setQueryKlineNum(4);
                } else if (anEnum.getRange().equals(CandleTimeRangeEnum.MIN_THIRTY.getRange())) {
                    singleKLine.setQueryKlineNum(2);
                } else {
                    singleKLine.setQueryKlineNum(10);
                }
            } else if (CandleTimeRangeEnum.HOUR_ONE.equals(anEnum)) {
                // 1小时线只获取24根k线
                singleKLine.setQueryKlineNum(24);
            } else {
                singleKLine.setQueryKlineNum(1);
            }
            if (requestDTO.isStock() && requestDTO.getAllTickStockAdjustType() != null) {
                singleKLine.setAdjustType(requestDTO.getAllTickStockAdjustType());
            }
            singleKLine.setKlineType(anEnum.getKLineTypeAllTick());
            singleKLine.setKlineTimestampEnd(requestDTO.getEndTS());

            kLines.add(singleKLine);
        }

        final AllTickParamDataBatchKLine paramData = new AllTickParamDataBatchKLine();
        paramData.setDataList(kLines);
        httpParam.setTrace(AllTickUtil.getTraceString());
        httpParam.setData(paramData);
        return httpParam;
    }

    /**
     * 构建单个k线请求的参数
     *
     * @param requestDTO 请求信息
     * @return 参数
     */
    private static AllTickHttpReq<AllTickParamDataSingleKLine> getSingleKLineParamData(DataSyncRequestDTO requestDTO) {
        // 转换TradePairCode为AllTick的产品code
        String productCode = AllTickUtil.getProductCode(requestDTO.getTradePairCode());
        AllTickHttpReq<AllTickParamDataSingleKLine> httpParam = new AllTickHttpReq<>();
        final AllTickParamDataSingleKLine paramData = new AllTickParamDataSingleKLine();
        paramData.setCode(productCode);
        paramData.setQueryKlineNum(requestDTO.getBarCount());
        paramData.setKlineTimestampEnd(requestDTO.getEndTS());
        if (requestDTO.isStock() && requestDTO.getAllTickStockAdjustType() != null) {
            paramData.setAdjustType(requestDTO.getAllTickStockAdjustType());
        }
        paramData.setKlineType(requestDTO.getTimeRangeEnum().getKLineTypeAllTick());
        httpParam.setTrace(AllTickUtil.getTraceString());
        httpParam.setData(paramData);
        return httpParam;
    }

    /**
     * 是否是昨日的时间戳
     *
     * @param timestamp 时间戳
     * @return true:是
     */
    private boolean isWithinYesterday(long timestamp) {
        // 获取当前日期的 UTC 零点时间戳
        long startOfToday = LocalDate.now().atStartOfDay().toEpochSecond(ZoneOffset.UTC);

        // 计算昨日的开始和结束时间戳
        long startOfYesterday = startOfToday - 86400; // 昨日零点
        long endOfYesterday = startOfToday - 1; // 昨日结束时间（今日零点减去 1 秒）

        // 检查时间戳是否在昨日的时间范围内
        return timestamp >= startOfYesterday && timestamp <= endOfYesterday;
    }

    /**
     * 是否是今天的日期
     *
     * @param timestamp 时间戳
     * @return true: 是今天的日期
     */
    private boolean isTimestampToday(long timestamp) {
        Instant instant = Instant.ofEpochMilli(timestamp * 1000);
        // 将 Instant 对象转换为 LocalDate 对象，采用UTC时区
        LocalDate date = instant.atZone(ZoneId.of("UTC")).toLocalDate();
        LocalDate today = LocalDate.now();
        return date.equals(today);
    }
}
