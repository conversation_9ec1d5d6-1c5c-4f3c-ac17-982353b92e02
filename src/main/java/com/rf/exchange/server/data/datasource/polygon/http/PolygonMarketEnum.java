package com.rf.exchange.server.data.datasource.polygon.http;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024-10-08
 */
@Getter
@AllArgsConstructor
public enum PolygonMarketEnum {

    FOREX("forex", "外汇", "CAS.", "C.", ""),
    CRYPTO("crypto", "加密货币","XAS.", "XQ.", "XT."),

    ;
    private final String market;
    private final String name;
    // 聚合消息(每秒)
    private final String evAggSec;
    // 报价消息
    private final String evQuote;
    // 成交消息
    private final String evTrade;

}
