package com.rf.exchange.server.data.datasource.alltick.websocket;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024-06-26
 */
@Getter
@AllArgsConstructor
public enum AllTickCommand {
    HEART(22000, "心跳"),
    HEART_RESP(22001, "心跳消息返回"),
    PK_PRICE(22002, "盘口报价"),
    PK_PRICE_RESP(22003, "盘口报价订阅返回"),
    DEAL_PRICE(22004, "成交报价（会返回实时成交价格）"),
    DEAL_PRICE_RESP(22005, "成交报价订阅返回"),
    CANCEL_SUB(22006, "取消报价订阅"),
    CANCEL_RESP(22007, "取消报价订阅返回"),
    DEAL_PRICE_MSG(22998, "实时成交报价消息"),
    PK_PRICE_MSG(22999, "实时盘口报价消息");

    private final Integer cmdCode;
    private final String name;
}
