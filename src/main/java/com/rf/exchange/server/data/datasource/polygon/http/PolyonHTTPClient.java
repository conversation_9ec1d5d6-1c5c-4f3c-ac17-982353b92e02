package com.rf.exchange.server.data.datasource.polygon.http;

import cn.hutool.core.collection.CollUtil;
import com.google.common.base.Joiner;
import com.rf.exchange.framework.common.util.json.JsonUtils;
import com.rf.exchange.module.candle.config.CandleDataSourceProperties;
import com.rf.exchange.server.data.datasource.polygon.dto.PolygonKLinePathArgus;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.classic.methods.HttpGet;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.core5.http.HttpStatus;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024-10-08
 */
@Slf4j
@Component
public class PolyonHTTPClient {

    @Resource
    private CloseableHttpClient httpClient;

    /**
     *
     */
    private static final String API_POLYGON_TICK_SNAPSHOTS = "/v2/snapshot/locale/global/markets/{market}/tickers";
    /**
     * 聚合k线
     */
    private static final String API_POLYGON_AGGREGATES_BARS = "/v2/aggs/ticker/{ticker}/range/{multiplier}/{timespan}/{from}/{to}";

    /**
     * Polygon的市场快照
     * <p>
     * 用于获取所有支持的外汇交易对
     *
     * @param polygonDataSource polygon数据源
     * @param marketEnum Polygon的市场类型
     * @return ticker编码集合
     */
    public Map<String, Object> sendTickerSnapshotsRequest(CandleDataSourceProperties.PolygonDataSource polygonDataSource, PolygonMarketEnum marketEnum) {
        //https://api.polygon.io/v2/snapshot/locale/global/markets/forex/tickers?apiKey=DZfU3ExOw1f9ZmE3YakPK_M6PdtKjJAk
        //https://api.polygon.io/v2/snapshot/locale/global/markets/crypto/tickers?apiKey=DZfU3ExOw1f9ZmE3YakPK_M6PdtKjJAk
        try {
            String formattedPath = API_POLYGON_TICK_SNAPSHOTS
                    .replace("{market}", marketEnum.getMarket());
            final Map<String, Object> responseMap = internalSend(polygonDataSource.getSecret(), polygonDataSource.getHost(), formattedPath, null);
            if (CollUtil.isNotEmpty(responseMap)) {
                return responseMap;
            } else {
                log.error("Polygon的请求返回为空，请检查");
            }
        } catch (IOException e) {
            log.error("发送Polygon外汇市场的Ticker快照请求失败");
        }
        return Map.of();
    }

    /**
     * 获取外汇的k线数据
     *
     * @param polygonDataSource polygon数据源
     * @param pathArgus         路由参数
     * @return 请求结果
     */
    public Map<String, Object> sendKLineRequest(CandleDataSourceProperties.PolygonDataSource polygonDataSource,
                                                PolygonKLinePathArgus pathArgus) {
        //https://api.polygon.io/v2/aggs/ticker/C:USDJPY/range/1/minute/2023-01-09/2023-02-10?sort=desc&apiKey=DZfU3ExOw1f9ZmE3YakPK_M6PdtKjJAk
        //https://api.polygon.io/v2/aggs/ticker/{forexTicker}/range/{multiplier}/{timespan}/{from}/{to}
        //https://api.polygon.io/v2/aggs/ticker/{cryptoTicker}/range/{multiplier}/{timespan}/{from}/{to}
        try {
            // 使用 String.replace() 替换占位符
            String formattedPath = API_POLYGON_AGGREGATES_BARS
                    .replace("{ticker}", pathArgus.getTicker())
                    .replace("{multiplier}", pathArgus.getMultiplier())
                    .replace("{timespan}", pathArgus.getTimespan())
                    .replace("{from}", pathArgus.getFrom())
                    .replace("{to}", pathArgus.getTo());
            Map<String, Object> params = new HashMap<>();
            params.put("sort", "desc");
            final Map<String, Object> response = internalSend(polygonDataSource.getSecret(), polygonDataSource.getHost(), formattedPath, params);
            if (CollUtil.isNotEmpty(response)) {
                return response;
            } else {
                log.error("Polygon的k线请求为空");
            }
        } catch (IOException e) {
            log.error("发送Polygon的K线请求失败 pathArgus:[{}]", pathArgus);
        }
        return Map.of();
    }

    private Map<String, Object> internalSend(String secret, String host, String path, Map<String, Object> params) throws IOException {
        Map<String, Object> queryMap = new HashMap<>(2);
        if (null != params) {
            queryMap.putAll(params);
        }
        queryMap.put("apiKey", secret);

        String paramStr = Joiner.on("&").withKeyValueSeparator("=").join(queryMap);
        String url = host + path + "?" + paramStr;

        HttpGet httpGet = new HttpGet(url);
        httpGet.setHeader("Accept", "application/json");

        log.info("Polygon 请求URL:[{}]", url);

        return httpClient.execute(httpGet, response -> {
            log.info("Polygon收到请求响应 res:[{}] url:[{}]", response, url);
            if (response.getCode() != HttpStatus.SC_OK) {
                final String json = EntityUtils.toString(response.getEntity());
                log.error("Polygon请求失败 http状态码:{} 原因:{}", response.getCode(), json);
                return null;
            }
            if (response.getEntity() == null) {
                log.error("Polygon请求返回异常 entity 为空");
                return null;
            }

            final String json = EntityUtils.toString(response.getEntity());
            @SuppressWarnings("unchecked") final Map<String, Object> map = JsonUtils.parseObject(json, Map.class);
            if (null != map) {
                final String status = (String) map.get("status");
                if (null == status || !status.equalsIgnoreCase("OK")) {
                    log.error("Polygon请求失败: 返回[{}]", json);
                    return null;
                }
            }
            return map;
        });
    }
}
