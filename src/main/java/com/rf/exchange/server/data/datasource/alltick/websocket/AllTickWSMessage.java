package com.rf.exchange.server.data.datasource.alltick.websocket;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024-06-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AllTickWSMessage implements AllTickCandleIdMessage {

    private Integer cmdId;
    private Long seqId;
    private String trace;
    private Map<String, Object> data;
    private Set<String> tradePairCodes;

    @Override
    public String getMessageId() {
        return String.valueOf(seqId);
    }

    @Override
    public Object getPayload() {
        Map<String, Object> payload = new HashMap<>(4);
        payload.put("cmd_id", cmdId);
        payload.put("seq_id", seqId);
        payload.put("trace", trace);
        payload.put("data", data);
        return payload;
    }
}
