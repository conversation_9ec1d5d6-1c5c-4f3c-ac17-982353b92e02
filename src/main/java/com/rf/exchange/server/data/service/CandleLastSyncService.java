package com.rf.exchange.server.data.service;

import com.rf.exchange.server.data.dal.dataobject.CandleLastSyncDO;

import java.util.Collection;

/**
 * <AUTHOR>
 * @since 2024-07-22
 */
public interface CandleLastSyncService {

    /**
     * 获取所有交易对的最后数据同步记录
     *
     * @return 同步记录列表
     */
    Collection<CandleLastSyncDO> getLastSyncRecords();

    /**
     * 更新交易对最后的数据同步时间
     *
     * @param candleLastSyncDO 最后同步记录
     */
    void updateLastSyncRecord(CandleLastSyncDO candleLastSyncDO);
}
