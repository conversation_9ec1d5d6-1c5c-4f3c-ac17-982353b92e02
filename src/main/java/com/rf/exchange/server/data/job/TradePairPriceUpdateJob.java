package com.rf.exchange.server.data.job;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import com.rf.exchange.framework.common.util.date.DateUtils;
import com.rf.exchange.module.candle.api.dto.CurrentPriceRespDTO;
import com.rf.exchange.module.candle.api.dto.TodayKlinePriceDTO;
import com.rf.exchange.module.candle.dal.dataobject.candle.CandleDO;
import com.rf.exchange.module.candle.dal.redis.CandleCurrentOrderBookRedisDAO;
import com.rf.exchange.module.candle.dal.redis.CandleCurrentPriceRedisDAO;
import com.rf.exchange.module.candle.dal.redis.CandleCustomTradePairRedisDAO;
import com.rf.exchange.module.candle.dal.redis.CandleTodayKlinePriceRedisDAO;
import com.rf.exchange.module.candle.dal.redis.dto.CandleOrderBookDTO;
import com.rf.exchange.module.candle.dal.redis.dto.CandleOrderBookListDTO;
import com.rf.exchange.module.candle.service.candle.CandleService;
import com.rf.exchange.module.exc.api.tradepair.TradePairApi;
import com.rf.exchange.module.exc.api.tradepair.dto.TradePairRespDTO;
import com.rf.exchange.server.data.core.mq.AppCandlePriceProducer;
import com.rf.exchange.server.data.core.mq.message.CandlePriceMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@Slf4j
@Component
public class TradePairPriceUpdateJob {
    @Resource
    private CandleCurrentPriceRedisDAO priceRedisDAO;
    @Resource
    private CandleTodayKlinePriceRedisDAO todayKlinePriceRedisDAO;
    @Resource
    private CandleCurrentOrderBookRedisDAO orderBookRedisDAO;
    @Resource
    private CandleService candleService;
    @Resource
    private CandleCustomTradePairRedisDAO customTradePairCandleRedisDAO;
    @Resource
    private AppCandlePriceProducer appCandlePriceProducer;
    @Resource
    @Lazy
    private TradePairApi tradePairApi;
    static DecimalFormat df = new DecimalFormat("0.00");

    public void genRealPrice() throws ExecutionException {
        List<TradePairRespDTO> tradePairList = tradePairApi.getCustomTradePair();
        tradePairList = tradePairList.stream().filter(t -> !t.getIsCopy()).collect(Collectors.toList());
        for (TradePairRespDTO item : tradePairList) {
            setPrice(item);
        }
    }

    private void setPrice(TradePairRespDTO item) {

        long nowSeconds = DateUtils.getUnixTimestampNow() / 1000 / 60 * 60;
        //Map<Long, CandleDO> candle30mCacheList = candle30mCache.get(item.getCode());
        Map<Long, CandleDO> candleCacheList = customTradePairCandleRedisDAO.get(item.getCode(), nowSeconds);
        if (candleCacheList == null || candleCacheList.isEmpty()) {
            return;
        }
        //找到这一分钟的那条k线
        long currentMinuteIndex = DateUtils.getUnixTimestampNow() / 1000 / 60;
        CandleDO candleDO = candleCacheList.get(currentMinuteIndex);
        if (candleDO == null) {
            return;
        }
        // 更新redis中的对应交易对的价格信息
        CurrentPriceRespDTO priceDto = priceRedisDAO.get(item.getCode());
        if (priceDto == null) {
            priceDto = new CurrentPriceRespDTO();
            priceDto.setTurnover(BigDecimal.ZERO);//初始化成交额
            priceDto.setVolume(BigDecimal.ZERO);//初始化成交量
        }
        priceDto.setVolume(BigDecimal.ZERO);
        priceDto.setTradePairCode(item.getCode());
        BigDecimal currentPrice = candleDO.getOpenPrice();

        //如果当前秒数在50-58秒之前，要指定收盘价才与提前生成的k线收盘价相符
        if (getNowSecondsGreater(RandomUtil.randomInt(55, 58))) {
            currentPrice = candleDO.getClosePrice();
        } else {
            //这里生成最高价和最低价
            if (RandomUtil.randomBoolean() && nowSeconds % (RandomUtil.randomInt(22, 28)) == 0) {
                currentPrice = candleDO.getHighPrice();
            } else if (RandomUtil.randomBoolean() && nowSeconds % (RandomUtil.randomInt(30, 36)) == 0) {
                currentPrice = candleDO.getLowPrice();
            } else {
                if (candleDO.getLowPrice().compareTo(candleDO.getHighPrice()) != 0) {
                    currentPrice = RandomUtil.randomBigDecimal(candleDO.getLowPrice(), candleDO.getHighPrice());
                }
            }
        }
        priceDto.setCurrentPrice(currentPrice);
        //生成交易额
        if (RandomUtil.randomBoolean()) {
            float turnover = candleDO.getTurnover().floatValue() * RandomUtil.randomFloat(0.01f, 0.08f);//item.getOneMinuteTurnover().floatValue() + (item.getOneMinuteTurnover().floatValue() * RandomUtil.randomFloat(-0.5f, 0.5f));
            if (priceDto.getTurnover() == null) {
                priceDto.setTurnover(BigDecimal.ZERO);
            }
            priceDto.setTurnover(priceDto.getTurnover().add(new BigDecimal(turnover)).setScale(2, RoundingMode.HALF_UP));
            // 每分钟成交量
            float volume = turnover / (currentPrice.floatValue());
            priceDto.setVolume(new BigDecimal(volume).setScale(2, RoundingMode.HALF_UP));
        }
        priceDto.setTickTime(DateUtils.getUnixTimestampNow());
        priceDto.setTimestamp(DateUtils.getUnixTimestampNow() / 60 * 60);

        // 更新24小时成交量
        TodayKlinePriceDTO todayKlinePrice = todayKlinePriceRedisDAO.get(item.getCode());
        if (todayKlinePrice == null) {
            todayKlinePrice = new TodayKlinePriceDTO();
            todayKlinePrice.setTimestamp("");
            todayKlinePrice.setVolume("0");
            todayKlinePrice.setVolume24H("0");
            todayKlinePrice.setTurnover("0");
        }

        long todayTime = DateUtils.getTodayStartTimeSecondsUtc();
        //如果今日k线的时间戳不等于引用交易对的时间戳，就要从数据库获取，今天的第一条k线
        if (StringUtils.isEmpty(todayKlinePrice.getTimestamp()) || Long.valueOf(todayKlinePrice.getTimestamp()) != todayTime) {
            CandleDO firstCandle = candleService.getFirstCandle(item.getCode(), todayTime);
            if (firstCandle != null) {
                todayKlinePrice.setTimestamp(todayTime + "");
                todayKlinePrice.setOpenPrice(firstCandle.getOpenPrice().setScale(item.getScale(), RoundingMode.HALF_UP).toString());
                todayKlinePrice.setClosePrice(currentPrice.setScale(item.getScale(), RoundingMode.HALF_UP).toString());

                long endTime = DateUtils.getUnixTimestampNow() / 1000;
                BigDecimal todayHighPrice = candleService.getHighPrice(item.getCode(), todayTime, endTime);
                if (todayHighPrice != null) {
                    todayKlinePrice.setHighPrice(todayHighPrice.setScale(item.getScale(), RoundingMode.HALF_UP).toString());
                    //candleService.setTodayHigh(item.getCode(), todayTime, todayHighPrice);
                }
                BigDecimal todayLowPrice = candleService.getLowPrice(item.getCode(), todayTime, endTime);
                if (todayLowPrice != null) {
                    todayKlinePrice.setLowPrice(todayLowPrice.setScale(item.getScale(), RoundingMode.HALF_UP).toString());
                    //candleService.setTodayLow(item.getCode(), todayTime, todayLowPrice);
                }
            }
        }
        if (todayKlinePrice.getHighPrice() == null || new BigDecimal(todayKlinePrice.getHighPrice()).compareTo(currentPrice) < 0) {
            todayKlinePrice.setHighPrice(currentPrice.toString());
        }
        if (todayKlinePrice.getLowPrice() == null || new BigDecimal(todayKlinePrice.getLowPrice()).compareTo(currentPrice) > 0) {
            todayKlinePrice.setLowPrice(currentPrice.toString());
        }
        todayKlinePrice.setTradePairCode(item.getCode());
        float volume24 = NumberUtil.parseFloat(todayKlinePrice.getVolume24H(), 0f) + priceDto.getVolume().floatValue();
        todayKlinePrice.setVolume(df.format(priceDto.getVolume()));
        todayKlinePrice.setTurnover(df.format(new BigDecimal(todayKlinePrice.getTurnover()).add(priceDto.getTurnover())));
        todayKlinePrice.setVolume24H(df.format(volume24));
        todayKlinePriceRedisDAO.update(item.getCode(), todayKlinePrice);

        // 计算涨跌幅
        final BigDecimal changePercentage = calculateChangePercentage(todayKlinePrice, currentPrice);
        priceDto.setPercentage(changePercentage);
        priceRedisDAO.set(priceDto.getTradePairCode(), priceDto);

        // 转发自发币的价格到MQ
        CandlePriceMessage mqMessage = CandlePriceMessage.builder()
                .code(priceDto.getTradePairCode())
                .price(String.valueOf(priceDto.getCurrentPrice()))
                .timestamp(String.valueOf(priceDto.getTickTime()))
                .volume(String.valueOf(priceDto.getVolume()))
                .turnover(String.valueOf(priceDto.getTurnover()))
                .build();
        appCandlePriceProducer.sendCustomTradePriceMessage(mqMessage);

        //更新委托单
        CandleOrderBookListDTO refOrderBook = orderBookRedisDAO.get(item.getReferenceCode());
        CandleOrderBookListDTO orderBook = orderBookRedisDAO.get(item.getCode());
        if (orderBook == null) {
            orderBook = new CandleOrderBookListDTO();
            orderBook.setBids(new ArrayList<>());
            orderBook.setAsks(new ArrayList<>());
        }
        orderBook.setTradePairCode(item.getCode());
        orderBook.getBids().clear();
        orderBook.getAsks().clear();
        final CandleOrderBookListDTO finalOrderBook = orderBook;
        List<Float> bidsList = genBidAskPriceList(candleDO.getHighPrice().floatValue(), -0.01f, refOrderBook.getBids().size());
        List<Float> asksList = genBidAskPriceList(candleDO.getLowPrice().floatValue(), 0.01f, refOrderBook.getBids().size());
        //模拟成交额生成挂单量
        float turnover = candleDO.getTurnover().floatValue() * RandomUtil.randomFloat(0.01f, 0.08f);//item.getOneMinuteTurnover().floatValue() + (item.getOneMinuteTurnover().floatValue() * RandomUtil.randomFloat(-0.5f, 0.5f));
        priceDto.setTurnover(new BigDecimal(turnover));
        // 每分钟成交量
        float finalVolume = turnover / (currentPrice.floatValue());
        bidsList.forEach(o ->
        {
            CandleOrderBookDTO bids = new CandleOrderBookDTO();
            bids.setPrice(df.format(o));
            bids.setVolume(df.format(finalVolume + (finalVolume * RandomUtil.randomFloat(-0.05f, 0.05f))));
            finalOrderBook.getBids().add(bids);
        });
        asksList.forEach(o ->
        {
            CandleOrderBookDTO asks = new CandleOrderBookDTO();
            asks.setPrice(df.format(o));
            asks.setVolume(df.format(finalVolume + (finalVolume * RandomUtil.randomFloat(-0.05f, 0.05f))));
            finalOrderBook.getAsks().add(asks);
        });
        orderBookRedisDAO.set(item.getCode(), orderBook);
    }

    /**
     * 判断当前时间的秒数是不是大于传入的秒数，因为每分钟k线在一分钟结束时要指定价格
     *
     * @param seconds
     * @return
     */
    private boolean getNowSecondsGreater(int seconds) {
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();

        // 获取当前秒数
        int second = now.getSecond();

        return second > seconds;
    }

    private BigDecimal calculateChangePercentage(TodayKlinePriceDTO todayKlinePriceDTO, BigDecimal currentPrice) {
        if (currentPrice == null || todayKlinePriceDTO == null || todayKlinePriceDTO.getOpenPrice() == null) {
            return BigDecimal.valueOf(0, 2);
        }
        try {
            final BigDecimal openPrice = new BigDecimal(todayKlinePriceDTO.getOpenPrice());
            final BigDecimal change = currentPrice.subtract(openPrice);
            //final MathContext mc = new MathContext(3, RoundingMode.HALF_UP);
            //return change.divide(closePrice, 8, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100), mc);
            //return change.divide(openPrice, 8, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
            return change.divide(openPrice, 8, RoundingMode.HALF_UP);
        } catch (NumberFormatException | ArithmeticException ignored) {
        }
        return BigDecimal.valueOf(0, 2);
    }

    private List<Float> genBidAskPriceList(float minPrice, float step, int count) {
        // 计算可以生成的最大不重复数值数量
        //int maxPossibleCount = (int) ((maxPrice - minPrice) / step);

        // 如果 count 超过了可生成的最大数量，自动调整 count
        //if (count > maxPossibleCount) {
        //   count = maxPossibleCount;
        //}

        Set<Float> priceSet = new HashSet<>();
        //Random random = new Random();

        for (int i = 0; i < count; i++) {
            // 生成符合step精度的随机数
            float randomPrice = minPrice + (i * step);
            priceSet.add(randomPrice);
        }

        return new ArrayList<>(priceSet);
    }

}
