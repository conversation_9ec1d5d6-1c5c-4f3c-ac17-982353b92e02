package com.rf.exchange.server.data.datasource.alltick.http.req;

import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024-07-25
 */
@Data
public class AllTickParamDataBatchKLine implements AllTickParamData {

    private List<AllTickParamDataSingleKLine> dataList;

    @Override
    public Map<String, Object> getMap() {
        Map<String, Object> param = new HashMap<>(1);
        List<Map<String, Object>> singleKlineMapList = new ArrayList<>();
        for (AllTickParamDataSingleKLine data : dataList) {
            singleKlineMapList.add(data.getMap());
        }
        param.put("data_list", singleKlineMapList);
        return param;
    }
}
