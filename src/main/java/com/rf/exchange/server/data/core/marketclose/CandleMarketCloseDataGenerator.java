package com.rf.exchange.server.data.core.marketclose;

import cn.hutool.core.util.RandomUtil;
import com.rf.exchange.module.candle.api.dto.CurrentPriceRespDTO;
import com.rf.exchange.module.candle.dal.redis.CandleCurrentPriceRedisDAO;
import com.rf.exchange.module.exc.api.tradepair.dto.TradePairRespDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 休市k线数据生成器
 *
 * <AUTHOR>
 * @since 2024-10-04
 */
@Slf4j
@Component
public class CandleMarketCloseDataGenerator {

    @Resource
    private CandleCurrentPriceRedisDAO priceRedisDAO;

    // 价格波动上下不超过3美金，即上下1.5美金
    private static final BigDecimal PRICE_CHANGE_RANGE = new BigDecimal("1.5");

    /**
     * 前一秒的价格
     */
    private final ConcurrentHashMap<String, BigDecimal> tmpLastPriceMap = new ConcurrentHashMap<>();
    /**
     * 交易对休市时的价格
     * 作为生成价格的基准价格
     */
    private final ConcurrentHashMap<String, BigDecimal> tmpBaseClosePriceMap = new ConcurrentHashMap<>();

    /**
     * 初始化休市数据生成器
     */
    public void initGenerator() {
        // 从redis中获取所有交易对的休市价格用于初始化生成器中的价格数据，作为基准的价格
        final Map<String, CurrentPriceRespDTO> allTradePriceMap = priceRedisDAO.getAllCloseBackupPrice();
        for (Map.Entry<String, CurrentPriceRespDTO> entry : allTradePriceMap.entrySet()) {
            final CurrentPriceRespDTO value = entry.getValue();
            final String code = entry.getKey();
            //assert value.getCurrentPrice() != null : code + "实时价格不能为空";
            final BigDecimal price = value.getCurrentPrice();
            if (null != price) {
                tmpLastPriceMap.put(code, price);
                tmpBaseClosePriceMap.put(code, price);
            }
        }
    }

    /**
     * 生成随机的k线价格数据
     *
     * @param tradePair 交易对
     */
    public Map<String, Object> randomGenerateCandlePriceData(TradePairRespDTO tradePair) {
        Map<String, Object> resultMap = new HashMap<>(3);

        final BigDecimal lastPrice = tmpLastPriceMap.get(tradePair.getCode());
        final BigDecimal baseClosePrice = tmpBaseClosePriceMap.get(tradePair.getCode());
        if (lastPrice == null || baseClosePrice == null) {
            return resultMap;
        }

        int scale = lastPrice.scale() == 0 ? 2 : lastPrice.scale();
        final BigDecimal minLimit = BigDecimal.valueOf(Math.pow(10, scale * -1));
        final BigDecimal maxLimit = BigDecimal.valueOf(Math.pow(10, scale * -1) * 2);
        BigDecimal change = RandomUtil.randomBigDecimal(minLimit, maxLimit).setScale(scale, RoundingMode.HALF_UP);

        int upDown = RandomUtil.randomInt(2);
        if (upDown == 0) {
            change = change.negate();
        }

        // 获取符合波动范围的假价格
        final BigDecimal resultPrice = makePriceWithinChangeRange(tradePair.getCode(), lastPrice, baseClosePrice, change, scale);
        tmpLastPriceMap.put(tradePair.getCode(), resultPrice);

        log.debug("code:[{}] >>> lastPrice:[{}] scale:[{}] change:[{}] result:[{}]", tradePair.getCode(), lastPrice, scale, change, resultPrice);

        resultMap.put("price", resultPrice);
        // 生成订单薄
        generateOrderBooks(resultPrice, 5, resultMap);

        return resultMap;
    }

    /**
     * @param tradePairCode 交易对代码
     * @param prevPrice     前一秒的价格
     * @param basePrice     基础价格
     * @param change        变动值
     * @param scale         保留的小数位数
     * @return 符合变动范围的价格信息
     */
    private BigDecimal makePriceWithinChangeRange(String tradePairCode, BigDecimal prevPrice, BigDecimal basePrice, BigDecimal change, int scale) {
        BigDecimal validChange = change;

        // 如果价格变动值超过了允许的波动范围则取波动值的50%
        if (validChange.abs().compareTo(PRICE_CHANGE_RANGE) > 0) {
            validChange = change.multiply(new BigDecimal("0.5"));

            // 如果50%值还超过了允许的波动范围
            if (validChange.abs().compareTo(PRICE_CHANGE_RANGE) > 0) {
                validChange = RandomUtil.randomBigDecimal(PRICE_CHANGE_RANGE).setScale(change.scale(), RoundingMode.HALF_UP);

                // 如果change是负数
                if (change.signum() < 0) {
                    validChange = validChange.abs().multiply(BigDecimal.valueOf(-1));
                }
            }
        }

        BigDecimal maxPrice = basePrice.add(PRICE_CHANGE_RANGE);
        BigDecimal minPrice = basePrice.subtract(PRICE_CHANGE_RANGE);
        BigDecimal result = prevPrice.add(validChange);

        // 如果最终价格超出了最小值和最大值范围则需要约束最终价格的值
        if (result.compareTo(maxPrice) > 0 || result.compareTo(minPrice) < 0) {
            result = prevPrice.add(validChange.negate());
        }
        return result.setScale(scale, RoundingMode.HALF_UP);
    }

    /**
     * 生成订单
     *
     * @param centerPrice 中心价格
     * @param depth       深度价格
     */
    private void generateOrderBooks(BigDecimal centerPrice, int depth, Map<String, Object> resultMap) {
        final int scale = centerPrice.scale();

        final TreeMap<BigDecimal, BigDecimal> bidsTreeMap = new TreeMap<>(Comparator.reverseOrder());
        final TreeMap<BigDecimal, BigDecimal> asksTreeMap = new TreeMap<>();

        double step = Math.pow(10, scale * -1);
        // 买方价格
        BigDecimal bidsPrevPrice = centerPrice.subtract(BigDecimal.valueOf(step));
        // 卖方价格
        BigDecimal asksPrevPrice = centerPrice.add(BigDecimal.valueOf(step));

        BigDecimal bidsVolume = RandomUtil.randomBigDecimal(new BigDecimal("0.1"), new BigDecimal("100")).setScale(3, RoundingMode.HALF_UP);
        BigDecimal askVolume = RandomUtil.randomBigDecimal(new BigDecimal("0.1"), new BigDecimal("100")).setScale(3, RoundingMode.HALF_UP);

        for (int i = 0; i < depth; i++) {
            // 先存
            bidsTreeMap.put(bidsPrevPrice, bidsVolume.setScale(2, RoundingMode.HALF_UP).abs());
            asksTreeMap.put(asksPrevPrice, askVolume.setScale(2, RoundingMode.HALF_UP).abs());

            // price
            bidsPrevPrice = bidsPrevPrice.subtract(BigDecimal.valueOf(step));
            asksPrevPrice = asksPrevPrice.add(BigDecimal.valueOf(step));

            // volume
            BigDecimal randomVolumeBid = RandomUtil.randomBigDecimal(new BigDecimal("0.01"), new BigDecimal("10"));
            BigDecimal randomVolumeAsk = RandomUtil.randomBigDecimal(new BigDecimal("0.01"), new BigDecimal("10"));
            bidsVolume = randomVolumeBid;
            askVolume = randomVolumeAsk;
        }
        resultMap.put("bids", bidsTreeMap);
        resultMap.put("asks", asksTreeMap);
    }

    /**
     * 更新交易对的价格
     *
     * @param code  交易对代码
     * @param price 价格
     */
    public void updateCloseBasePrice(String code, BigDecimal price) {
        tmpBaseClosePriceMap.put(code, price);
        tmpLastPriceMap.put(code, price);
    }
}
