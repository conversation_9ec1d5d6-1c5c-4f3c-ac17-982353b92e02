package com.rf.exchange.server.data.service;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.dynamic.datasource.annotation.Master;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.rf.exchange.framework.desensitize.core.slider.handler.ChineseNameDesensitization;
import com.rf.exchange.server.data.dal.dataobject.CandleLastSyncDO;
import com.rf.exchange.server.data.dal.mysql.CandleLastSyncMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;

/**
 * <AUTHOR>
 * @since 2024-07-22
 */
@Slf4j
@Service
public class CandleLastSyncServiceImpl implements CandleLastSyncService {

    @Resource
    private CandleLastSyncMapper candleLastSyncMapper;

    @Override
    @Slave
    public Collection<CandleLastSyncDO> getLastSyncRecords() {
        return candleLastSyncMapper.selectList();
    }

    @Override
    @Master
    @DSTransactional
    public void updateLastSyncRecord(CandleLastSyncDO candleLastSyncDO) {
        candleLastSyncMapper.insertOrUpdateOnDuplicate(candleLastSyncDO.getCode(), candleLastSyncDO);

        //candleLastSyncMapper.insertOrUpdate(candleLastSyncDO);

        //final CandleLastSyncDO existsRecord = checkSyncRecordExists(candleLastSyncDO);
        //if (existsRecord != null) {
        //    existsRecord.setLastSyncDate(candleLastSyncDO.getLastSyncDate());
        //    candleLastSyncMapper.updateById(existsRecord);
        //} else {
        //    candleLastSyncMapper.insert(candleLastSyncDO);
        //}
    }

    private CandleLastSyncDO checkSyncRecordExists(CandleLastSyncDO candleLastSyncDO) {
        return candleLastSyncMapper.selectByCode(candleLastSyncDO.getCode());
    }
}
