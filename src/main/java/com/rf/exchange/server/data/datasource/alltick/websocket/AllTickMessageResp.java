package com.rf.exchange.server.data.datasource.alltick.websocket;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024-07-01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AllTickMessageResp implements Serializable {

    private Integer ret;
    private String msg;

    private Integer cmdId;
    private Long seqId;
    private String trace;

    //private Map<String, Object> data;
    private AllTickMessageRespData data;

    @Data
    static class AllTickMessageRespData {
        private String code;
        private String msg;
    }
}
