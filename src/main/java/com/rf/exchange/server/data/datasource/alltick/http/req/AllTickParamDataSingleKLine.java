package com.rf.exchange.server.data.datasource.alltick.http.req;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024-07-25
 */
@Data
public class AllTickParamDataSingleKLine implements AllTickParamData {

    // 产品代码
    private String code;
    // k线类型，1分钟K，2为5分钟K，3为15分钟K，4为30分钟K，5为小时K，6为2小时K，7为4小时K，8为日K，9为周K，10为月K
    private Integer klineType;
    // 从那个时间点往前查，为0表示从当前时间
    private Long klineTimestampEnd;
    // 查询多少k线
    private Integer queryKlineNum;
    // 复权类型,对于股票类的code才有效，例如：0:除权,1:前复权
    private Integer adjustType;

    public Map<String, Object> getMap() {
        Map<String, Object> param = new HashMap<>(5);
        param.put("code", code);
        param.put("kline_type", klineType);
        param.put("kline_timestamp_end", klineTimestampEnd);
        param.put("query_kline_num", queryKlineNum);
        if (adjustType != null) {
            param.put("adjust_type", adjustType);
        }
        return param;
    }
}
