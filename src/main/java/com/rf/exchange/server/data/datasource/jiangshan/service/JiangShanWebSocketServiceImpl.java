package com.rf.exchange.server.data.datasource.jiangshan.service;

import com.github.rholder.retry.*;
import com.rf.exchange.server.data.datasource.jiangshan.websocket.JiangShanWebSocketHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.client.WebSocketClient;
import org.springframework.web.socket.client.WebSocketConnectionManager;
import org.springframework.web.socket.client.standard.StandardWebSocketClient;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @description: 方法实现
 * @author: Petter
 * @create: 2024-10-01
 **/
@Service
@RequiredArgsConstructor
@Slf4j
public class JiangShanWebSocketServiceImpl implements JiangShanWebSocketService {

    private final WebSocketClient webSocketClient = new StandardWebSocketClient();
    private final JiangShanWebSocketHandler jiangShanWebSocketHandler;
    private WebSocketConnectionManager webSocketConnectionManager;

    @Value("${exchange.candle-datasource.jiangShan.ws-host:f-test.js-stock.top:3001}")
    private String wsHost;

    @Value("${exchange.candle-datasource.jiangShan.enable:false}")
    private Boolean enabled;

    @Override
    public void connect() {
        try {
            webSocketConnectionManager =
                new WebSocketConnectionManager(webSocketClient, jiangShanWebSocketHandler, wsHost);
            webSocketConnectionManager.start();
        } catch (Exception e) {
            log.error("jiangShan websocket connect error", e);
        }
        if (!isConnected()) {
            this.retryConnect();
        }
    }

    @Override
    public boolean isConnected() {
        if (Objects.nonNull(webSocketConnectionManager)) {
            return webSocketConnectionManager.isConnected();
        }
        return false;
    }

    @Override
    public void disconnect() {
        webSocketConnectionManager.stop();
    }

    @Override
    public boolean isEnable() {
        return enabled;
    }

    /**
     * 连接重试
     */

    private void retryConnect() {
        Retryer<Boolean> retryer = RetryerBuilder.<Boolean>newBuilder()
            // 无论出现什么异常，都进行重试
            .retryIfException()
            // 如果返回true，则重试
            .retryIfResult(input -> {
                // 如果manager为空则不重试
                if (Objects.isNull(webSocketConnectionManager)) {
                    return false;
                }
                // 没有连接成功则重试
                return !webSocketConnectionManager.isConnected();
            })
            // 重试等待策略：等待 3s 后再进行重试
            .withWaitStrategy(WaitStrategies.fixedWait(3, TimeUnit.SECONDS))
            // 重试停止策略：重试达到 3 次
            .withStopStrategy(StopStrategies.stopAfterAttempt(3)).withRetryListener(new RetryListener() {
                @Override
                public <V> void onRetry(Attempt<V> attempt) {
                    log.info("RetryListener: 第" + attempt.getAttemptNumber() + "次调用" + " source:JiangShan");
                }
            }).build();
        try {
            retryer.call(() -> {
                if (Objects.nonNull(webSocketConnectionManager)) {
                    webSocketConnectionManager.start();
                }
                return true;
            });
        } catch (Exception e) {
            log.error("多次重新连接失败: {}", e.getMessage());
        }
    }

}
