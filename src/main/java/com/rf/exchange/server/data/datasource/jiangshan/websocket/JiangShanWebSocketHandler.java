package com.rf.exchange.server.data.datasource.jiangshan.websocket;

import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @description: 匠山websocket
 * @author: Petter
 * @create: 2024-10-01
 **/
@Component
@RequiredArgsConstructor
@Slf4j
public class Jiang<PERSON>hanWebSocketHandler extends TextWebSocketHandler {
    private final JiangShanMessageProcessor jiangShanMessageProcessor;

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        super.handleTextMessage(session, message);
        jiangShanMessageProcessor.handleWebSocketResponseMessage(message.getPayload());
    }

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        super.afterConnectionEstablished(session);
        log.info("jiangShanWebSocketHandler afterConnectionEstablished");
        jiangShanMessageProcessor.setWebSocketSession(session);
        // 发送认证消息
        jiangShanMessageProcessor.sendAuthMessage();
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        super.afterConnectionClosed(session, status);
        // 关闭心跳
        jiangShanMessageProcessor.stopHeartbeat();
        log.info("jiangShanWebSocketHandler afterConnectionClosed");
    }

}
