package com.rf.exchange.server.data.core.marketclose.pojo;

import com.rf.exchange.module.candle.dal.redis.dto.CandleOrderBookDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-10-05
 */
@Data
public class CandleRandomPrice {
    private Long timestamp;
    private String code;
    private BigDecimal price;
    private BigDecimal volume;
    // 买单
    private List<CandleOrderBookDTO> bids;
    // 买单
    private List<CandleOrderBookDTO> asks;
}
