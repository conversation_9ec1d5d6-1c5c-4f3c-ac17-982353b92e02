package com.rf.exchange.server.data.service;

import com.rf.exchange.server.data.enums.CandleDataSource;

import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024-06-26
 */
public interface CandleDataWebSocketService {

    /**
     * 连接 dataSource
     *
     * @param dataSource k线数据源
     */
    void connect(CandleDataSource dataSource);

    /**
     * 断开连接
     *
     * @param dataSource k线数据源
     */
    void disconnect(CandleDataSource dataSource);

    /**
     * 发送ws的订阅消息
     *
     * @param dataSource k线数据源
     * @param codeList   需要订阅的产品列表
     */
    void sendUpdateSubscribeMessage(CandleDataSource dataSource, Set<String> codeList);

    /**
     * 发送取消订阅消息
     * @param dataSource k线数据源
     * @param codeList 需要取消订阅的产品列表
     */
    void sendUnsubscribeMessage(CandleDataSource dataSource, Set<String> codeList);

    /**
     * 发送ws的订阅消息
     *
     * @param dataSource k线数据源
     */
    void sendSubscribeMessage(CandleDataSource dataSource);

    /**
     * 发送ws的心跳消息
     *
     * @param dataSource k线数据源
     */
    void sendHeartbeatMessage(CandleDataSource dataSource);
}
