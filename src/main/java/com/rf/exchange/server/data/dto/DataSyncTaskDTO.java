package com.rf.exchange.server.data.dto;

import com.rf.exchange.module.exc.enums.TradeAssetTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024-07-22
 */
@Data
public class DataSyncTaskDTO {
    /**
     * 数据源
     */
    private int dataSource;
    /**
     * 是否是股票交易对
     */
    private boolean isStock;
    /**
     * 交易对代码
     */
    private String tradePairCode;
    /**
     * 基础code
     */
    private String baseCode;
    /**
     * 报价code
     */
    private String quoteCode;
    /**
     * 交易对类型
     * {@link TradeAssetTypeEnum}
     */
    private Integer tradeAssetType;
    /**
     * 同步数据的日期(开始日期)
     */
    private LocalDateTime startTime;
    /**
     * 同步数据的日期(截止日期)
     */
    private LocalDateTime endTime;
    /**
     * 需要同步的数据条数
     */
    private Integer barCount;
    /**
     * 是否是同步的历史数据
     * true:同步历史k线 false:同步实时k线
     */
    private boolean isHistorySync = false;
    /**
     * 是否需要创建分钟级别k线数据的请求
     * true:创建分钟级别请求 false:不创建分钟级别数据的请求
     */
    private boolean isNeedRequestMinKline = false;
    /**
     * 是否需要更新最后同步记录的时间
     */
    private boolean isNeedUpdateLastSyncDate = true;
}
