package com.rf.exchange.server.data.dal.mysql;

import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.server.data.dal.dataobject.CandleLastSyncDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @since 2024-07-06
 */
@Mapper
public interface CandleLastSyncMapper extends BaseMapperX<CandleLastSyncDO> {

    default CandleLastSyncDO selectByCode(String code) {
        return selectOne(new LambdaQueryWrapperX<CandleLastSyncDO>().eqIfPresent(CandleLastSyncDO::getCode, code));
    }

    void insertOrUpdateOnDuplicate(@Param("code") String code, @Param("updateObj") CandleLastSyncDO updateObj);
}
