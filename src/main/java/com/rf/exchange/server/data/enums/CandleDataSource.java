package com.rf.exchange.server.data.enums;

import java.util.Arrays;

import com.rf.exchange.framework.common.core.IntArrayValuable;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024-06-27
 */
@Getter
@AllArgsConstructor
public enum CandleDataSource implements IntArrayValuable {
//    @formatter:off
    POLYGON(0, "polygon"),
    ALLTICK(1, "allTick"),
    JIANG_SHAN(2, "jiang_shan");

    private final int value;
    private final String name;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(CandleDataSource::getValue).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }
}
