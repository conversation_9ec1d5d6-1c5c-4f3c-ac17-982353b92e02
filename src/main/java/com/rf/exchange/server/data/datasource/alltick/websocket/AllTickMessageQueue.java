package com.rf.exchange.server.data.datasource.alltick.websocket;

import com.rf.exchange.server.data.dal.redis.CandleDataSourceSubscribeRedisDAO;
import com.rf.exchange.server.data.enums.CandleDataSource;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.support.PeriodicTrigger;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketSession;

import java.time.Duration;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * <AUTHOR>
 * @since 2024-07-01
 */
@Slf4j
@Setter
@Getter
@Component
public class AllTickMessageQueue {

    // 等待发送消息队列
    private final BlockingQueue<AllTickWSMessage> allTickMessageQueue = new LinkedBlockingQueue<>();
    // 等待响应的消息
    private final ConcurrentHashMap<String, AllTickWSMessage> pendingResponses = new ConcurrentHashMap<>();

    private WebSocketSession allTickWebSocketSession;

    @Resource
    private AllTickMessageSender messageSender;
    @Resource
    private AllTickProductSubscriber subscriber;
    @Resource
    private CandleDataSourceSubscribeRedisDAO dataSourceRedisDAO;
    @Resource(name = "websocketTaskScheduler")
    private ThreadPoolTaskScheduler sendMessageScheduler;

    @PostConstruct
    public void init() {
        sendMessageScheduler.schedule(this::read, new PeriodicTrigger(Duration.ofSeconds(1)));
    }

    public void put(CandleDataSource dataSource, AllTickWSMessage message) {
        // 如果是心跳且websocketSession不可用
        if (message.getCmdId().equals(AllTickCommand.HEART.getCmdCode()) && (allTickWebSocketSession == null || !allTickWebSocketSession.isOpen())) {
            return;
        }
        try {
            allTickMessageQueue.put(message);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    public void read() {
        if (allTickWebSocketSession == null || !allTickWebSocketSession.isOpen()) {
            return;
        }
        if (allTickMessageQueue.isEmpty()) {
            return;
        }
        AllTickWSMessage message = allTickMessageQueue.poll();
        if (message != null) {
            messageSender.send(message, allTickWebSocketSession);
            // 非心跳消息则放入pendingResponses，检查消息的响应是否正确
            if (!AllTickCommand.HEART.getCmdCode().equals(message.getCmdId())) {
                pendingResponses.put(message.getMessageId(), message);
            }
            log.info("AllTick 发送ws消息:{} 等待返回消息数量:{}", message, pendingResponses.size());
        }
    }

    @Async("websocketTaskScheduler")
    public void processResponse(Integer ret, String messageId) {
        AllTickWSMessage message = pendingResponses.get(messageId);
        if (message != null) {
            if (!AllTickCommand.HEART.getCmdCode().equals(message.getCmdId())) {
                if (200 == ret) {
                    messageSuccess(message);
                } else {
                    messageFailed(message);
                }
            }
        } else {
            log.error("AllTick 待处理的websocket应答消息不存在 messageId:{}", messageId);
        }
    }

    public void messageSuccess(AllTickWSMessage message) {
        // 因为是先发送的PK_PRICE消息，所以这里保存PK_PRICE中的code列表
        if (message.getCmdId().equals(AllTickCommand.PK_PRICE.getCmdCode())) {
            // 更新订阅关系
            subscriber.updateSubscribeCods(message.getTradePairCodes());
        } else if (message.getCmdId().equals(AllTickCommand.CANCEL_SUB.getCmdCode())) {
            // 清除订阅关系
            subscriber.cleanAllSubscribedCodes();
        }

        // 从待处理的消息map中移除已经处理完的消息
        pendingResponses.remove(message.getMessageId());

        // 如果收到了盘口报价消息的成功响应再发送成交报价订阅消息
        if (message.getCmdId().equals(AllTickCommand.DEAL_PRICE.getCmdCode())) {
            final AllTickWSMessageBuilder builder = new AllTickWSMessageBuilder();
            // 产品列表已经转换过一次了，所以这里不需要再转换
            AllTickCandleIdMessage pkPriceMessage = builder.buildSubscribeMessage(message.getTradePairCodes(), AllTickCommand.PK_PRICE.getCmdCode(), null);
            put(CandleDataSource.ALLTICK, (AllTickWSMessage) pkPriceMessage);
        }
    }

    public void messageFailed(AllTickWSMessage oldMessage) {
        // 从待处理的消息中移除
        pendingResponses.remove(oldMessage.getMessageId());
        // 重新生成一个新的消息
        AllTickWSMessageBuilder builder = new AllTickWSMessageBuilder();
        AllTickCandleIdMessage message = builder.rebuildMessage(oldMessage);
        // 将新消息重新加入队列中
        put(CandleDataSource.ALLTICK, (AllTickWSMessage) message);
    }
}
