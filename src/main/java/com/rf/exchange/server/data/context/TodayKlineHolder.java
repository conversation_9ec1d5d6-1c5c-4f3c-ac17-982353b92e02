package com.rf.exchange.server.data.context;

import cn.hutool.core.collection.CollUtil;
import com.rf.exchange.framework.common.util.date.DateUtils;
import com.rf.exchange.module.candle.api.dto.TodayKlinePriceDTO;
import com.rf.exchange.module.candle.dal.redis.CandleTodayKlinePriceRedisDAO;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @since 2024-07-26
 */
@Slf4j
public class TodayKlineHolder {

    /**
     * 系统交易对的今日日线价格，在获取到k线的实时价格之后需要取计算涨跌幅
     * key1: 时间戳
     * key2: 交易对代码
     */
    private static final ConcurrentHashMap<String, TodayKlinePriceDTO> TODAY_KLINE_MAP = new ConcurrentHashMap<>(100);

    public static void initialize(CandleTodayKlinePriceRedisDAO redisDAO) {
        // 初始化k线的昨日收盘价格
        final Map<String, TodayKlinePriceDTO> todayKlinePriceDTOMap = redisDAO.getAll();
        TODAY_KLINE_MAP.putAll(todayKlinePriceDTOMap);
    }

    /**
     * 获取今日k线
     *
     * @param tradePairCode 系统交易对代码
     * @return 今日k线
     */
    public static TodayKlinePriceDTO get(String tradePairCode) {
        return TODAY_KLINE_MAP.get(tradePairCode);
    }

    /**
     * 更新交易对的今日k线
     *
     * @param tradePairCode 系统交易对的代码
     * @param kline         今日k线
     */
    public static void updateTodayOpenPriceMap(String tradePairCode, TodayKlinePriceDTO kline) {
        TODAY_KLINE_MAP.put(tradePairCode, kline);
    }
}
