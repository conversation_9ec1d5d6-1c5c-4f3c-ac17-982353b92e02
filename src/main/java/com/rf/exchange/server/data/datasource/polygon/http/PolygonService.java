package com.rf.exchange.server.data.datasource.polygon.http;

import com.rf.exchange.module.exc.api.tradepair.dto.TradePairRespDTO;
import com.rf.exchange.server.data.dto.DataSyncRequestDTO;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-10-08
 */
public interface PolygonService {

    /**
     * Polygon是否支持交易对
     *
     * @param marketEnum 市场类型
     * @param polygonTicker  polygon的ticker名称
     * @return true:polygon支持该交易对
     */
    boolean isSupportedTicker(PolygonMarketEnum marketEnum, String polygonTicker);

    ///**
    // * 获取Polygon的Ticker
    // *
    // * @param assetType   系统交易对资产类型
    // * @param baseCode    基础资产
    // * @param quoteCode   报价资产
    // * @param isWebSocket
    // * @return ticker名称
    // */
    //String getPolygonTickerName(Integer assetType, String baseCode, String quoteCode, boolean isWebSocket);

    /**
     * 获取Polygon的交易对类型(外汇和加密货币)
     */
    default void fetchPolygonTickerTypeAll() {
        fetchPolygonTickerTypes(PolygonMarketEnum.FOREX);
        fetchPolygonTickerTypes(PolygonMarketEnum.CRYPTO);
    }

    /**
     * 获取Polygon的交易对类型
     */
    void fetchPolygonTickerTypes(PolygonMarketEnum marketEnum);

    /**
     * 获取Polygon的外汇k线交易数据
     */
    void fetchPolygonForexKLine(PolygonMarketEnum marketEnum, DataSyncRequestDTO requestDTO);

    /**
     * 设置websocket的会话
     *
     * @param marketEnum 类型
     * @param session    ws的会话
     */
    void saveWSSession(PolygonMarketEnum marketEnum, WebSocketSession session);

    /**
     * 处理WS的消息
     *
     * @param textMessage ws消息
     * @param session     ws会话
     * @param marketEnum polygon的market枚举
     */
    void handleWSMessage(TextMessage textMessage, WebSocketSession session, PolygonMarketEnum marketEnum);

    /**
     * 更新需要订阅的交易对
     *
     * @param tradePairs 交易对代码
     */
    void updateSubscribeTradePair(List<TradePairRespDTO> tradePairs);

    /**
     */
    void connect();

    /**
     * 重新连接Polygon的ws
     * @param marketEnum Polygon的市场类型
     */
    void connectWithMarket(PolygonMarketEnum marketEnum);
}

