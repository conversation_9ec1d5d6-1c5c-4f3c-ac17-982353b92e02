package com.rf.exchange.server.data.service;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.rf.exchange.module.candle.api.CandleDataApi;
import com.rf.exchange.module.candle.api.dto.CandleDTO;
import com.rf.exchange.module.candle.enums.CandleTimeRangeEnum;
import com.rf.exchange.module.exc.api.tradepair.TradePairApi;
import com.rf.exchange.module.exc.api.tradepair.TradePairControlApi;
import com.rf.exchange.module.exc.api.tradepair.dto.TradePairControlRespDTO;
import com.rf.exchange.module.exc.api.tradepair.dto.TradePairRespDTO;
import com.rf.exchange.server.data.datasource.alltick.http.resp.AllTickKLine;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class CandleDataSyncServiceImpl implements CandleDataSyncService {

    @Resource
    private CandleDataApi candleDataApi;
    @Resource
    private TradePairApi tradePairApi;

    @Resource
    private TradePairControlApi tradePairControlApi;

    @Override
    public void createCandleDataTable() {
        candleDataApi.createAllCandleTableIfNeed();
    }

    @Override
    public void insertCandleRecords(String tradePairCode, CandleTimeRangeEnum timeRangeEnum, List<AllTickKLine> kLineList) {
        if (kLineList.isEmpty()) {
            return;
        }
        log.debug("插入数据库 交易对:[{}] 数据条数:{}", tradePairCode, kLineList.size());
        List<CandleDTO> candleDTOList = new ArrayList<>(kLineList.size());
        for (AllTickKLine kLine : kLineList) {
            CandleDTO candleDTO = new CandleDTO();
            long id = IdUtil.getSnowflake().nextId();
            candleDTO.setId(id);
            if (kLine.getTimeRange() != null) {
                candleDTO.setTimeRange(kLine.getTimeRange());
            } else {
                candleDTO.setTimeRange(timeRangeEnum.getRange());
            }
            if (kLine.getTimeType() != null) {
                candleDTO.setTimeType(kLine.getTimeType());
            } else {
                candleDTO.setTimeType(timeRangeEnum.getTypeValue());
            }
            candleDTO.setTimestamp(Long.valueOf(kLine.getTimestamp()));
            candleDTO.setHighPrice(new BigDecimal(kLine.getHighPrice()));
            candleDTO.setLowPrice(new BigDecimal(kLine.getLowPrice()));
            candleDTO.setOpenPrice(new BigDecimal(kLine.getOpenPrice()));
            candleDTO.setClosePrice(new BigDecimal(kLine.getClosePrice()));
            if (StrUtil.isNotEmpty(kLine.getVolume()) && NumberUtil.isNumber(kLine.getVolume())) {
                candleDTO.setVolume(new BigDecimal(kLine.getVolume()));
            }
            if (StrUtil.isNotEmpty(kLine.getTurnover()) && NumberUtil.isNumber(kLine.getTurnover())) {
                candleDTO.setTurnover(new BigDecimal(kLine.getTurnover()));
            }
            candleDTO.setCode(tradePairCode);
            candleDTOList.add(candleDTO);
        }
        candleDataApi.batchInsertCandles(tradePairCode, candleDTOList);

        // 同步存入这个币的复制币中
        List<TradePairRespDTO> list = tradePairApi.getCustomTradePair();
        list = list.stream().filter(t -> t.getIsCopy() && t.getReferenceCode().equalsIgnoreCase(tradePairCode)).toList();
        for (TradePairRespDTO item : list) {
            for (CandleDTO candleDTO : candleDTOList) {
                candleDTO.setCode(item.getCode());
                // 获取复制币的控盘信息
                TradePairControlRespDTO tradePairControlDO = tradePairControlApi.getCacheByCode(item.getCode());
                if (tradePairControlDO != null) {
                    //控的那分钟
                    long controlMinuteTime = tradePairControlDO.getStartTime() / 1000 / 60 * 60;
                    if (controlMinuteTime == candleDTO.getTimestamp()) {
                        candleDTO.setHighPrice(tradePairControlDO.getHighPrice());
                        candleDTO.setLowPrice(tradePairControlDO.getLowPrice());
                        candleDTO.setClosePrice(tradePairControlDO.getEndPrice());
                    }

                    //这里判断是什么时间级别的k线,判断控盘的那根k线是否在这个级别里最高
                    if (candleDTO.getTimeType() != CandleTimeRangeEnum.MIN_ONE.getTypeValue() && candleDTO.getTimeRange() != CandleTimeRangeEnum.MIN_ONE.getRange()) {
                        long startTime = candleDTO.getTimestamp();//k线的时间戳做为开始时间
                        long endTime = CandleTimeRangeEnum.getEndTs(CandleTimeRangeEnum.enumOfAllTickLineType(candleDTO.getTimeType()), startTime);//加上这个k线时间级别的总毫秒数做为结束时间
                        if (controlMinuteTime >= startTime && controlMinuteTime <= endTime) {
                            if (tradePairControlDO.getHighPrice().compareTo(candleDTO.getHighPrice()) > 0) {
                                candleDTO.setHighPrice(tradePairControlDO.getHighPrice());
                            }
                            if (tradePairControlDO.getLowPrice().compareTo(candleDTO.getLowPrice()) < 0) {
                                candleDTO.setLowPrice(tradePairControlDO.getLowPrice());
                            }
                            //如果刚好是这个时间级别k线的结束时间，那要把这个时间级别的k线收盘价设置
                            if (controlMinuteTime + 60 == endTime) {
                                candleDTO.setClosePrice(tradePairControlDO.getEndPrice());
                            }
                        }
                    }
                }
            }
            candleDataApi.batchInsertCandlesNoUpdate(item.getCode(), candleDTOList, true);
        }
    }
}
