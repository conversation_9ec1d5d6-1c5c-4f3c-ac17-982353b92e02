//package com.rf.exchange.server.data.datasource.polygon.websocket;
//
//import cn.hutool.core.lang.Dict;
//import cn.hutool.json.JSONUtil;
//import com.rf.exchange.server.data.core.mq.RocketCandleProducer;
//import jakarta.annotation.Resource;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.scheduling.annotation.Async;
//import org.springframework.stereotype.Component;
//
///**
// * <AUTHOR>
// * @since 2024-07-01
// */
//@Slf4j
//@Component
//public class PolygonWSMessageProcessor {
//
//    @Resource
//    private RocketCandleProducer rocketCandleProducer;
//
//    @Async("websocketProcessExecutor")
//    public void process(String ev, Dict message) {
//        final String jsonStr = JSONUtil.toJsonStr(message);
//    }
//}
