package com.rf.exchange.server.data.datasource.alltick.websocket;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.IOException;

/**
 * <AUTHOR>
 * @since 2024-07-01
 */
@Slf4j
@Component
public class AllTickMessageSender {

    private static final ObjectMapper mapper = new ObjectMapper().setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);

    public void send(AllTickWSMessage message, WebSocketSession session) {
        if (session == null || !session.isOpen()) {
            log.error("session为空或者session关闭");
            return;
        }
        String jsonMessage = convert2Json(message);
        if (StrUtil.isEmpty(jsonMessage)) {
            return;
        }
        try {
            TextMessage textMessage = new TextMessage(jsonMessage);
            //synchronized (session) {
            session.sendMessage(textMessage);
            log.info("发送 AllTick 的ws消息 json[{}]", jsonMessage);
            //}
        } catch (IOException e) {
            log.error("发送 AllTick 的ws消息失败: {}", message.getPayload());
        }
    }

    /**
     * CandleIdMessage转json
     * @param message 消息对象
     * @return json字符串
     */
    private String convert2Json(AllTickWSMessage message) {
        try {
            return mapper.writeValueAsString(message.getPayload());
        } catch (JsonProcessingException e) {
            log.error("AllTick的ws消息异常 无法格式化成json {}", e.getMessage());
        }
        return null;
    }
}
