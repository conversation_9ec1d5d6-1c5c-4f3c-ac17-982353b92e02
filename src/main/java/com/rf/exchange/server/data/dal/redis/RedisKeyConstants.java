package com.rf.exchange.server.data.dal.redis;

/**
 * <AUTHOR>
 * @since 2024-06-27
 */
public interface RedisKeyConstants {

    String PRIVATE_KEY_PREFIX = "exch:";

    /**
     * 交易对数据源的监控
     * <p>
     * KEY 格式: candle_current_price:{id} VALUE 数据格式：String 模版信息 *
     */
    String CANDLE_SOURCE_LAST_UPDATE = PRIVATE_KEY_PREFIX + "candle:source_last_update";

    /**
     * 格式: source_last_update_time + 数据源名称 : 最后推送时间
     */
    String CANDLE_SOURCE_LAST_PUSH_UPDATE = PRIVATE_KEY_PREFIX + "candle:source_last_push_time";

    /**
     * 交易对数据源的websocket订阅信息
     * <p>
     * KEY 格式: candle_current_price:{id} VALUE 数据格式：String 模版信息 *
     */
    String CANDLE_DATASOURCE_SUBSCRIBE = PRIVATE_KEY_PREFIX + "candle:datasource_subscribe";

    /**
     * polygon数据源的ticker type
     */
    String POLYGON_TICKER_TYPE = PRIVATE_KEY_PREFIX + "polygon_ticker_type";


}
