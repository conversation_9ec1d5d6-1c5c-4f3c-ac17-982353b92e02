package com.rf.exchange.server.data.job;

import com.rf.exchange.framework.common.util.date.DateUtils;
import com.rf.exchange.module.candle.dal.dataobject.candle.CandleDO;
import com.rf.exchange.module.candle.enums.CandleTimeRangeEnum;
import com.rf.exchange.module.candle.service.candle.CandleService;
import com.rf.exchange.module.candle.service.candlecontrol.CandleControlService;
import com.rf.exchange.module.exc.api.tradepair.TradePairApi;
import com.rf.exchange.module.exc.api.tradepair.dto.TradePairRespDTO;
import com.rf.exchange.server.data.service.CandleDataSyncService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * 自动生成k线及报价数据任务
 */
@Component
public class TradePairDataAutoGenJob {
    @Resource
    private CandleDataSyncService dataSyncService;
    @Resource
    private CandleService candleService;
    @Resource
    private TradePairApi tradePairApi;
    @Resource
    private TradePairPriceUpdateJob tradePairPriceUpdateJob;
    @Resource
    private CandleControlService candleControlService;

    /**
     * 实时价格变动
     */
    //@Scheduled(cron = "0/2 * * * * ?")
    public void genRealPrice() {
        try {
            tradePairPriceUpdateJob.genRealPrice();
        } catch (ExecutionException e) {
            //throw new RuntimeException(e);
        }
    }


    /**
     * 合并一分钟k线
     */
    //@Scheduled(cron = "0/20 * * * * ?")
    public void startKLineAggregationScheduler() {
        List<TradePairRespDTO> list = tradePairApi.getCustomTradePair();
        //List<TradePairRespDTO>   customPairList = list.stream().filter(t -> !t.getIsCopy()).collect(Collectors.toList());
        for (TradePairRespDTO tradePair : list) {
            List<CandleDO> allCandle = new ArrayList<>();
            allCandle.addAll(merge(tradePair, CandleTimeRangeEnum.MIN_FIVE));
            allCandle.addAll(merge(tradePair, CandleTimeRangeEnum.MIN_FIFTEEN));
            allCandle.addAll(merge(tradePair, CandleTimeRangeEnum.MIN_THIRTY));
            allCandle.addAll(merge(tradePair, CandleTimeRangeEnum.HOUR_ONE));
            allCandle.addAll(merge(tradePair, CandleTimeRangeEnum.HOUR_FOUR));
            allCandle.addAll(merge(tradePair, CandleTimeRangeEnum.DAY_ONE));
            //allCandle.addAll(merge(tradePair, CandleTimeRangeEnum.WEEK_ONE));
            candleControlService.batchSaveCandleList(tradePair, allCandle);
        }
        //周线
        for (TradePairRespDTO tradePair : list) {
            List<CandleDO> weekList = mergeWeek((tradePair));
            if (weekList.size() > 0) {
                candleControlService.batchSaveCandleList(tradePair, weekList);
            }
        }
    }

    private List<CandleDO> mergeWeek(TradePairRespDTO tradePair) {
//        long weekStartTime = DateUtils.getWeekStartTime();
//        long lastWeekTime = DateUtils.getWeekStartTime() - (7 * 24 * 60 * 60);
        //先取最后一根周k线
        CandleDO lastWeekCandle = candleService.getLastCandle(tradePair.getCode(), CandleTimeRangeEnum.WEEK_ONE, null, null);
        if (lastWeekCandle == null) {
            //取历史第一根k线
            lastWeekCandle = candleService.getFirstCandle(tradePair.getCode(), 0);
        } else {
            long thisWeekStartTime = DateUtils.getWeekStartTime();
            if (lastWeekCandle.getTimestamp() == thisWeekStartTime - (7 * 24 * 60 * 60)) {
                return Collections.emptyList();
            }
        }
        if (lastWeekCandle != null) {
            //取周k线的开始时间
            long startTime = DateUtils.getWeekStartTime(lastWeekCandle.getTimestamp());
            long endTime = DateUtils.getWeekStartTime();
            int totalMinutes = (int) (endTime / 60 - startTime / 60);
            // 交易对保留小数位
            String pattern = "#." + "0".repeat(tradePair.getScale());
            int kLineCount = 7 * 24 * 60;
            // 查询上一个时间区间的1分钟K线数据
            List<CandleDO> oneMinuteKLines = candleService.getCandleOneMinuteList(tradePair.getCode(), startTime, totalMinutes);
            Collections.sort(oneMinuteKLines, new Comparator<CandleDO>() {
                @Override
                public int compare(CandleDO o1, CandleDO o2) {
                    return (int) (o1.getTimestamp() - o2.getTimestamp());
                }
            });
            int totalCount = totalMinutes / kLineCount;
            //从早上8点开始计算合并时间
            List<CandleDO> candleDOList = new ArrayList<>();
            BigDecimal previewClosePrice = null;

            for (int i = 0; i < totalCount; i++) {
                long thisStartTime = startTime + (i * kLineCount) * 60;
                long thisEndTime = thisStartTime + (kLineCount * 60);
                List<CandleDO> typeList = oneMinuteKLines.stream().filter(c -> c.getTimestamp() >= thisStartTime && c.getTimestamp() < thisEndTime).toList();
                if(typeList.size()==0){
                    break;
                }
                List<BigDecimal> highPriceList = typeList.stream().map(CandleDO::getHighPrice).toList();
                List<BigDecimal> lowPriceList = typeList.stream().map(CandleDO::getLowPrice).toList();
                List<BigDecimal> openPriceList = typeList.stream().map(CandleDO::getOpenPrice).toList();
                List<BigDecimal> closePriceList = typeList.stream().map(CandleDO::getClosePrice).toList();
                List<BigDecimal> allPriceList = new ArrayList<>();
                allPriceList.addAll(highPriceList);
                allPriceList.addAll(lowPriceList);
                allPriceList.addAll(openPriceList);
                allPriceList.addAll(closePriceList);
                BigDecimal openPrice = typeList.get(0).getOpenPrice();
                if (previewClosePrice != null) {
                    openPrice = previewClosePrice;
                }
                BigDecimal closePrice = typeList.get(typeList.size() - 1).getClosePrice();
                previewClosePrice = closePrice;
                BigDecimal highPrice = Collections.max(allPriceList);
                BigDecimal lowPrice = Collections.min(allPriceList);
                BigDecimal volume = typeList.stream().map(CandleDO::getVolume)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal turnover = typeList.stream().map(CandleDO::getTurnover)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                CandleDO candleDO = new CandleDO();
                candleDO.setCode(tradePair.getCode());
                candleDO.setTimeRange(CandleTimeRangeEnum.WEEK_ONE.getRange());
                candleDO.setTimeType(CandleTimeRangeEnum.WEEK_ONE.getTypeValue());
                candleDO.setTimestamp(startTime + ((i * kLineCount) * 60));
                candleDO.setHighPrice(highPrice);
                candleDO.setLowPrice(lowPrice);
                candleDO.setOpenPrice(openPrice);
                candleDO.setClosePrice(closePrice);
                candleDO.setVolume(volume);
                candleDO.setTurnover(turnover);
                candleDOList.add(candleDO);
            }
            return candleDOList;
        }
        return Collections.emptyList();
    }

    private List<CandleDO> merge(TradePairRespDTO tradePair, CandleTimeRangeEnum type) {
        // 交易对保留小数位
        String pattern = "#." + "0".repeat(tradePair.getScale());
        DecimalFormat df = new DecimalFormat(pattern);

        CandleDO lastCandle = candleService.getLastCandle(tradePair.getCode(), type, null, null);
        long startTime = tradePair.getCandleStartTime() / 1000;
        startTime = DateUtils.getAddDayStartTimeSecondsUtc(startTime * 1000, 0);
        //如果还没有这个时间级别的k线
        if (lastCandle != null) {
            startTime = lastCandle.getTimestamp();
        }
        long candleEndTime = DateUtils.getAddDayStartTimeSecondsUtc(1);
        //long candleEndTime = DateUtils.getUnixTimestampNow() / 1000;
        int totalMinutes = (int) (candleEndTime / 60 - startTime / 60);
        int kLineCount = CandleTimeRangeEnum.getDurationMinutes(type);
        if (totalMinutes >= kLineCount) {
            // 查询上一个时间区间的1分钟K线数据
            List<CandleDO> oneMinuteKLines = candleService.getCandleOneMinuteList(tradePair.getCode(), startTime, totalMinutes);
            Collections.sort(oneMinuteKLines, new Comparator<CandleDO>() {
                @Override
                public int compare(CandleDO o1, CandleDO o2) {
                    return (int) (o1.getTimestamp() - o2.getTimestamp());
                }
            });
            int totalCount = oneMinuteKLines.size() / kLineCount;
            //从早上8点开始计算合并时间
            List<CandleDO> candleDOList = new ArrayList<>();
            BigDecimal previewClosePrice = null;
            for (int i = 0; i < totalCount; i++) {
                List<CandleDO> typeList = oneMinuteKLines.subList(i * kLineCount, kLineCount + (i * kLineCount));
                if (typeList.size() != kLineCount) {
                    break;
                }
                List<BigDecimal> highPriceList = typeList.stream().map(CandleDO::getHighPrice).toList();
                List<BigDecimal> lowPriceList = typeList.stream().map(CandleDO::getLowPrice).toList();
                List<BigDecimal> openPriceList = typeList.stream().map(CandleDO::getOpenPrice).toList();
                List<BigDecimal> closePriceList = typeList.stream().map(CandleDO::getClosePrice).toList();
                List<BigDecimal> allPriceList = new ArrayList<>();
                allPriceList.addAll(highPriceList);
                allPriceList.addAll(lowPriceList);
                allPriceList.addAll(openPriceList);
                allPriceList.addAll(closePriceList);
                BigDecimal openPrice = typeList.get(0).getOpenPrice();
                if (previewClosePrice != null) {
                    openPrice = previewClosePrice;
                }
                BigDecimal closePrice = typeList.get(typeList.size() - 1).getClosePrice();
                previewClosePrice = closePrice;
                BigDecimal highPrice = Collections.max(allPriceList);
                BigDecimal lowPrice = Collections.min(allPriceList);
                BigDecimal volume = typeList.stream().map(CandleDO::getVolume)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal turnover = typeList.stream().map(CandleDO::getTurnover)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                CandleDO candleDO = new CandleDO();
                candleDO.setCode(tradePair.getCode());
                candleDO.setTimeRange(type.getRange());
                candleDO.setTimeType(type.getTypeValue());
                candleDO.setTimestamp(typeList.get(0).getTimestamp());
                candleDO.setHighPrice(highPrice);
                candleDO.setLowPrice(lowPrice);
                candleDO.setOpenPrice(openPrice);
                candleDO.setClosePrice(closePrice);
                candleDO.setVolume(volume);
                candleDO.setTurnover(turnover);
                candleDOList.add(candleDO);
            }
            return candleDOList;
        }
        return new ArrayList<>();
    }

    /**
     * 生成历史的和未来的1分钟k线
     */
    //@Scheduled(cron = "0/30 * * * * ?")
    public void genHistoryAndFutureKline() {
        //找出自发币的k线时间小于一天后的，保证提前一天生成
        long candleEndTime = DateUtils.getAddDayStartTimeSecondsUtc(1);
        List<TradePairRespDTO> list = tradePairApi.getCustomTradePair();
        list = list.stream().filter(t -> !t.getIsCopy()).collect(Collectors.toList());
        for (TradePairRespDTO item : list) {
            //取最后一根k线时间
            Long lastCandleTimestamp = candleService.getLastKlineTimestamp(item.getCode());
            //如果为null，就是还没有k线，要生成历史k线
            if (lastCandleTimestamp == null) {
                genHistoryKline(item);
            } else if (candleEndTime > lastCandleTimestamp) {
                //candle的时间戳是秒为单位 所以不用/1000
                int durationMinutes = (int) (candleEndTime / 60 - lastCandleTimestamp / 60);//换算成以分钟为单位的时间戳，相减就知道差几分钟
                if (durationMinutes <= 0) return;//如果分钟数小于等于0不处理
                //获取参考交易对的k线数据
                List<CandleDO> refCandleList = candleService.getRefCandleMinuteOneList(item.getReferenceCode(), durationMinutes);
                CandleDO candleDO = candleService.getLastCandle(item.getCode(), CandleTimeRangeEnum.MIN_ONE, null, null);
                //生成交易对一分钟中k线
                List<CandleDO> minute1TkLines = candleControlService.genMinuteOne(item, refCandleList, lastCandleTimestamp, durationMinutes, item.getOneMinuteTurnover().floatValue(), candleDO.getClosePrice());
                //List<AllTickKLine> allKlineList = genTimeRangeKline(df, minute1TkLines);
                candleControlService.batchSaveCandleList(item, minute1TkLines);
            }
        }
    }

    /**
     * 这边检查新添加的自发币生成之前的历史k线数据，生成到第二天的8:00
     */
    public void genHistoryKline(TradePairRespDTO item) {
        //找到还没有k线时间的自发币
//        List<TradePairDO> list = tradePairService.getCustomTradePairNoCandle();
//        for (TradePairDO item : list) {
        //生成到明天，提前一天生成
        long endTime = DateUtils.getAddDayStartTimeSecondsUtc(1);
        long startTime = item.getCandleStartTime() / 1000;
        //没有开始时间默认从60天前开始
        if (startTime <= 0) {
            startTime = DateUtils.getAddDayStartTimeSecondsUtc(-30);
        }
        startTime = DateUtils.getAddDayStartTimeSecondsUtc(startTime * 1000, 0);
        int durationMinutes = (int) (endTime / 60 - startTime / 60);//换算为分钟为单位的时间戳
        //获取参考交易对的k线数据
        List<CandleDO> refCandleList = candleService.getRefCandleMinuteOneList(item.getReferenceCode(), durationMinutes);
        //生成交易对一分钟中k线
        List<CandleDO> minute1TkLines = candleControlService.genMinuteOne(item, refCandleList, startTime, durationMinutes, item.getOneMinuteTurnover().floatValue(), item.getIssuedPrice());
        //聚合一分钟为其它时间范围的k线
        //List<CandleDO> allKlineList = genTimeRangeKline(df, minute1TkLines);
        candleControlService.batchSaveCandleList(item, minute1TkLines);
//        }
    }
}
