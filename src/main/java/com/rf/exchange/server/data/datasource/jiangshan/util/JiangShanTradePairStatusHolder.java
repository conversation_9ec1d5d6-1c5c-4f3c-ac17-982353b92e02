package com.rf.exchange.server.data.datasource.jiangshan.util;

import java.util.HashMap;
import java.util.Optional;

import lombok.experimental.UtilityClass;

/**
 * @description: 交易对休市状态
 * @author: Petter
 * @create: 2024-10-09
 **/
@UtilityClass
public class JiangShanTradePairStatusHolder {
    private static final HashMap<String, Boolean> TRADE_PAIR_LAST_UPDATE_MAP = new HashMap<>();
    // 时间相差-单位秒 = 4分钟
    private static final Long TIME_DIFF_SECONDS = 240L;

    public static Boolean isMarketClosed(String tradePairCode) {
        return Optional.ofNullable(TRADE_PAIR_LAST_UPDATE_MAP.get(tradePairCode)).orElse(Boolean.FALSE);
    }

    /**
     * 根据1分钟k线的时间戳判断是否已经休市
     * 
     * @param tradePair
     * @param timestamp
     */
    public static void updateStatus(String tradePair, Long timestamp) {
        if (System.currentTimeMillis() / 1000 - timestamp > TIME_DIFF_SECONDS) {
            TRADE_PAIR_LAST_UPDATE_MAP.put(tradePair, Boolean.TRUE);
        } else {
            TRADE_PAIR_LAST_UPDATE_MAP.put(tradePair, Boolean.FALSE);
        }
    }

}
