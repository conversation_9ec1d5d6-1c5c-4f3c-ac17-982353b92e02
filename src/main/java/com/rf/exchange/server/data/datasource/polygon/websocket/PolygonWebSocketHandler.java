package com.rf.exchange.server.data.datasource.polygon.websocket;

import com.rf.exchange.server.data.datasource.polygon.http.PolygonMarketEnum;
import com.rf.exchange.server.data.datasource.polygon.http.PolygonService;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

/**
 * <AUTHOR>
 * @since 2024-06-27
 */
@Slf4j
@Getter
public class PolygonWebSocketHandler extends TextWebSocketHandler {

    private WebSocketSession session;

    private final PolygonMarketEnum marketEnum;

    private final PolygonService polygonService;

    public PolygonWebSocketHandler(PolygonMarketEnum marketEnum, PolygonService polygonService) {
        this.marketEnum = marketEnum;
        this.polygonService = polygonService;
    }

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        super.afterConnectionEstablished(session);
        log.info("Polygon建立ws连接 session:{}", session.getId());
        this.session = session;
        polygonService.saveWSSession(marketEnum, session);
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        super.handleTextMessage(session, message);
        // 收到消息
        polygonService.handleWSMessage(message, session, marketEnum);
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        super.handleTransportError(session, exception);
        log.error("Polygon的ws连接出现传输错误 session:{}", session.getId());
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        super.afterConnectionClosed(session, status);
        log.warn("Polygon的ws连接自动关闭 session:{}", session.getId());
        polygonService.connectWithMarket(marketEnum);
    }
}
