package com.rf.exchange.server.data.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.tenant.core.context.TenantContextHolder;
import com.rf.exchange.module.candle.api.CandleDataApi;
import com.rf.exchange.module.candle.api.ControlPlanApi;
import com.rf.exchange.module.candle.api.dto.ControlCandleDTO;
import com.rf.exchange.module.candle.api.dto.ControlPlanDTO;
import com.rf.exchange.module.candle.api.dto.ControlPlanKLineDTO;
import com.rf.exchange.module.candle.api.dto.CurrentPriceRespDTO;
import com.rf.exchange.module.candle.enums.CandleControlPlanStatusEnum;
import jakarta.annotation.Resource;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-11-02
 */
@Component
public class ControlCandlePersistJob {

    @Resource
    private ControlPlanApi controlPlanApi;
    @Resource
    private CandleDataApi candleDataApi;

    /**
     * 持久化控盘k线
     */
    @Scheduled(cron = "0/30 * * * * ? ")
    public void persistControlCandles() {
        TenantContextHolder.setIgnore(true);
        long now = DateUtil.currentSeconds();

        // 获取所有的运行中的控盘计划
        final List<ControlPlanDTO> runningPlanList = controlPlanApi.getRunningPlanList();
        for (ControlPlanDTO runningPlan : runningPlanList) {
            if (!runningPlan.getStatus().equals(CandleControlPlanStatusEnum.RUNNING.getStatus())) {
                continue;
            }
            boolean isFinish = runningPlan.getEndTime() <= now;
            if (isFinish) {
                // 如果运行中的计划已经结束了则将结束的计划删除
                controlPlanApi.finishPlan(runningPlan.getId());
            }

            // 转存kline, 同时这里需要
            transferKLineToCandles(runningPlan, runningPlan.getExecPrice(), now);
        }

        // 获取所有等待中的控盘计划
        final List<ControlPlanDTO> waitPlanList = controlPlanApi.getWaitPlanList();
        // 判断等待中的控盘计划是否需要转变成运行中的控盘计划
        for (ControlPlanDTO waitPlan : waitPlanList) {
            if (!waitPlan.getStatus().equals(CandleControlPlanStatusEnum.WAITING.getStatus())) {
                continue;
            }

            // 因为是每分钟的59秒运行这个任务，所以这里加1秒钟，如果开始时间符合则开始将等待中的控盘计划转变成运行中的控盘计划
            boolean isCouldRun = waitPlan.getStartTime() <= now + 1 && waitPlan.getEndTime() > now + 1;
            // 如果当前时间在控盘计划的时间范围内，则转换预先生成的k线并保存到data_control_candle表中
            if (isCouldRun) {
                // 获取当前交易对的价格
                CurrentPriceRespDTO currentPrice = candleDataApi.getCurrentPrice(waitPlan.getTradePairCode());
                // 转存kline
                transferKLineToCandles(waitPlan, currentPrice.getCurrentPrice(), now);
                // 修改
                controlPlanApi.startPlan(waitPlan.getId(), currentPrice.getCurrentPrice());
            }
            // 如果因为某些问题导致了控盘计划的结束时间超过时间则结束计划
            boolean isFinished = waitPlan.getEndTime() <= now;
            if (isFinished) {
                controlPlanApi.finishPlan(waitPlan.getId());
            }
        }
        TenantContextHolder.setIgnore(false);
    }

    /**
     * 转存kline到data_control_candle表中
     *
     * @param planDTO       控盘计划
     * @param realTimePrice 实时价格
     * @param now           时间戳
     */
    private void transferKLineToCandles(ControlPlanDTO planDTO, BigDecimal realTimePrice, long now) {
        final BigDecimal referPrice = planDTO.getReferPrice();
        // 引用价格和当前价格的差值
        final BigDecimal priceDiff = realTimePrice.subtract(referPrice);

        // 插入控盘的k线
        final List<ControlPlanKLineDTO> klineList = controlPlanApi.getPlanKlineList(planDTO.getId());
        if (CollUtil.isNotEmpty(klineList)) {
            //
            List<ControlCandleDTO> candles = new ArrayList<>();
            for (ControlPlanKLineDTO kline : klineList) {
                if (kline.getTimestamp() >= now) {
                    final BigDecimal highPrice = kline.getHighPrice().add(priceDiff);
                    final BigDecimal lowPrice = kline.getLowPrice().add(priceDiff);
                    final BigDecimal openPrice = kline.getOpenPrice().add(priceDiff);
                    final BigDecimal closePrice = kline.getClosePrice().add(priceDiff);
                    final ControlCandleDTO candleDO = BeanUtils.toBean(kline, ControlCandleDTO.class);
                    candleDO.setOpenPrice(openPrice);
                    candleDO.setClosePrice(closePrice);
                    candleDO.setHighPrice(highPrice);
                    candleDO.setLowPrice(lowPrice);
                    candleDO.setVolume(kline.getVolume());
                    candles.add(candleDO);
                }
            }
            controlPlanApi.saveControlCandles(candles);
        }
    }
}
