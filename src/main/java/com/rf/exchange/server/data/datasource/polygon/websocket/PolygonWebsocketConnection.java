package com.rf.exchange.server.data.datasource.polygon.websocket;

import com.github.rholder.retry.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.socket.client.WebSocketClient;
import org.springframework.web.socket.client.WebSocketConnectionManager;
import org.springframework.web.socket.client.standard.StandardWebSocketClient;

import java.io.IOException;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2024-10-19
 */
@Slf4j
public class PolygonWebsocketConnection {

    private final WebSocketConnectionManager connectionManager;

    public PolygonWebsocketConnection(String wsHost, PolygonWebSocketHandler socketHandler) {
        WebSocketClient webSocketClient = new StandardWebSocketClient();
        this.connectionManager = new WebSocketConnectionManager(webSocketClient, socketHandler, wsHost);
    }

    public void connect() {
        if (!connectionManager.isConnected()) {
            retryConnect();
        }
    }

    private Callable<Boolean> retryConnectCallable() {
        return () -> {
            if (connectionManager.isConnected()) {
                return true;
            }
            connectionManager.start();
            return true;
        };
    }

    private Boolean isConnected() {
        boolean isConnect = connectionManager.isConnected();
        log.info("数据源:Polygon isConnect:[{}]", isConnect);
        return isConnect;
    }

    private void retryConnect() {
        Retryer<Boolean> retryer = RetryerBuilder.<Boolean>newBuilder()
                //无论出现什么异常，都进行重试
                .retryIfExceptionOfType(IOException.class)
                // 如果返回true，则重试
                .retryIfResult(input -> {
                    // 没有连接成功则重试
                    return !isConnected();
                })
                //重试等待策略：等待 3s 后再进行重试
                .withWaitStrategy(WaitStrategies.fixedWait(3, TimeUnit.SECONDS))
                //重试停止策略：重试达到 3 次
                .withStopStrategy(StopStrategies.stopAfterAttempt(3))
                .withRetryListener(new RetryListener() {
                    @Override
                    public <V> void onRetry(Attempt<V> attempt) {
                        log.info("RetryListener: 第{}次调用 source:Polygon", attempt.getAttemptNumber());
                    }
                })
                .build();
        try {
            retryer.call(retryConnectCallable());
        } catch (Exception e) {
            log.error("数据源:Polygon 多次重新连接失败: {}", e.getMessage());
        }
    }
}
