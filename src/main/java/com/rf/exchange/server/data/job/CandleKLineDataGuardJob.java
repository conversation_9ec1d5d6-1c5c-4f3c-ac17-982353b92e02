package com.rf.exchange.server.data.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.rf.exchange.framework.common.enums.CommonStatusEnum;
import com.rf.exchange.framework.common.util.json.JsonUtils;
import com.rf.exchange.framework.xxljob.core.log.XJLog;
import com.rf.exchange.module.candle.enums.CandleTimeRangeEnum;
import com.rf.exchange.module.exc.api.tradepair.TradePairApi;
import com.rf.exchange.module.exc.api.tradepair.dto.TradePairRespDTO;
import com.rf.exchange.module.exc.enums.TradeAssetTypeEnum;
import com.rf.exchange.server.data.config.CandleDataSyncProperties;
import com.rf.exchange.server.data.core.http.executor.CandleSyncGuardTaskExecutor;
import com.rf.exchange.server.data.dal.dataobject.CandleLastSyncDO;
import com.rf.exchange.server.data.dto.DataSyncRequestDTO;
import com.rf.exchange.server.data.dto.DataSyncTaskDTO;
import com.rf.exchange.server.data.enums.CandleDataSource;
import com.rf.exchange.server.data.service.CandleLastSyncService;
import com.rf.exchange.server.data.utils.RateLimiterSimpleWindow;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.support.PeriodicTrigger;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * k线历史数据的守护任务
 *
 * <AUTHOR>
 * @since 2024-07-20
 */
@Component
public class CandleKLineDataGuardJob {

    // 历史k线请求的任务队列
    private final LinkedBlockingQueue<DataSyncRequestDTO> historyRequestQueue = new LinkedBlockingQueue<>();
    // 1分钟一次的k线请求队列
    private final LinkedBlockingQueue<DataSyncRequestDTO> realTimeRequestQueue = new LinkedBlockingQueue<>();
    // Polygon的实时1分钟一次的k线请求队列
    private final LinkedBlockingQueue<DataSyncRequestDTO> polygonRealTimeRequestQueue = new LinkedBlockingQueue<>();
    // Polygon的历史k线请求队列
    private final LinkedBlockingQueue<DataSyncRequestDTO> polygonHistoryRequestQueue = new LinkedBlockingQueue<>();

    private static final DateTimeFormatter UTC_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'").withZone(ZoneId.of("UTC"));

    @Value("${exchange.alltick-qps:50}")
    private int allTickQps;

    @Resource(name = "historyCandleSyncTaskScheduler")
    private ThreadPoolTaskScheduler historyTaskScheduler;

    @Resource(name = "realTimeCandleSyncTaskScheduler")
    private ThreadPoolTaskScheduler realTimeTaskScheduler;

    @Resource(name = "polygonRealTimeCandleSyncScheduler")
    private ThreadPoolTaskScheduler polygonRealTimeTaskScheduler;

    @Resource(name = "polygonHistoryCandleSyncScheduler")
    private ThreadPoolTaskScheduler polygonHistoryTaskScheduler;

    @Resource
    private CandleLastSyncService lastSyncService;
    @Resource
    private TradePairApi tradePairApi;
    @Resource
    private CandleDataSyncProperties syncProperties;
    @Resource
    private CandleSyncGuardTaskExecutor taskExecutor;

    @PostConstruct
    public void init() {
        // 设置限流器的QPS
        RateLimiterSimpleWindow.setupQPS(allTickQps);
        historyTaskScheduler.schedule(this::pollHistoryQueue, new PeriodicTrigger(Duration.ofMillis(200))); // 1秒5个历史请求
        realTimeTaskScheduler.schedule(this::pollRealTimeQueue, new PeriodicTrigger(Duration.ofMillis(100))); // 1秒10个实时请求
        polygonRealTimeTaskScheduler.schedule(this::pollPolygonRealTimeQueue, new PeriodicTrigger(Duration.ofMillis(100)));
        polygonHistoryTaskScheduler.schedule(this::pollPolygonHistoryQueue, new PeriodicTrigger(Duration.ofMillis(100)));
    }

    /**
     * k线实时数据的获取的拉取任务
     * 最慢执行频率1分钟1次
     */
    @XxlJob("candleRealTimeDataGuardJob")
    public void realTimeDataGuardJob() {
        XJLog.info("执行k线实时数据守护任务 开始");

        final List<TradePairRespDTO> needSyncTradePairList = getSyncEnableTradePairList(false);
        // 起始时间往后延迟1分钟，避免漏数据
        final LocalDateTime now = LocalDateTime.now().plusMinutes(1);
        List<DataSyncTaskDTO> taskList = new ArrayList<>();
        for (TradePairRespDTO tradePairDTO : needSyncTradePairList) {
            // 如果是自发币或者复制币则不拉取历史数据
            if (tradePairDTO.getIsCustom() || tradePairDTO.getIsCopy()) {
                continue;
            }
            XJLog.info("系统交易对 {} 创建实时拉取k线的任务 {}", tradePairDTO.getCode(), now);
            DataSyncTaskDTO task = createSyncTask(tradePairDTO, LocalDateTimeUtil.beginOfDay(now), now, false, true);
            taskList.add(task);
        }
        // 添加到队列中
        queueQueueTasks(taskList);
        XJLog.info("执行k线实时数据守护任务 完成");
    }

    /**
     * 检查历史数据的完整性
     * <p>
     * 最快10分钟执行一次
     * <p>
     * 根据数据源的接口请求频率限制需要调整
     * 计算方式为 一个系统交易对总共有10个级别的k线，其中1分钟线超过了1000条的接口请求限制，所以需要占用两个请求
     * 1个交易对请求1天时间内所有级别的k线数据需要发送，
     * 1. 包括1分钟级别k线的情况下，需要发送11个请求
     * 2. 不包括1分钟级别k线的情况下，需要发送9个请求
     * <p>
     * AllTick数据源1分钟目前是600次请求，如果是所以需要200个系统交易对，则需要1个交易对1分钟最多只能发送3次请求，所以最多需要3.66分钟才能发送完11条请求
     * <p>
     * 所以假设200个交易对的话的，则最小需要间隔4分钟执行定时任务
     */
    @XxlJob("candleHistoryDataGuardJob")
    public void historyDataGuardJob() {
        LocalDateTime start = LocalDateTime.now();
        XJLog.info("执行k线历史数据守护任务 开始... {}", start);

        // xxl-job的任务参数，用于同步特定时间范围的k线记录
        LocalDateTime paramEndTime = null;
        LocalDateTime paramStartTime = null;
        List<String> paramCodeList = new ArrayList<>();

        // 获取xxl-job的参数，有可能需要使用定时任务拉取特定时间范围的k线数据
        final String jobParam = XxlJobHelper.getJobParam();
        if (StrUtil.isNotEmpty(jobParam)) {
            XxlJobHelper.log("任务参数: {}", jobParam);
            try {
                @SuppressWarnings("unchecked")
                Map<String, Object> jobParamsMap = JsonUtils.parseObject(jobParam, Map.class);
                if (jobParamsMap != null && jobParamsMap.get("endTime") != null) {
                    String endTimeStr = (String) jobParamsMap.get("endTime");
                    paramEndTime = LocalDateTimeUtil.parse(endTimeStr, "yyyy-MM-dd");
                    if (jobParamsMap.get("startTime") != null) {
                        String startTimeStr = (String) jobParamsMap.get("startTime");
                        paramStartTime = LocalDateTimeUtil.parse(startTimeStr, "yyyy-MM-dd");
                    }
                    if (jobParamsMap.get("codeList") != null) {
                        paramCodeList = (List<String>) jobParamsMap.get("codeList");
                    }
                }
            } catch (RuntimeException e) {
                XJLog.error("发生异常 {}", e.getMessage());
                XxlJobHelper.log(e);
            }
        }

        final List<TradePairRespDTO> needSyncTradePairList = getSyncEnableTradePairList(true);
        final Map<String, CandleLastSyncDO> lastSyncRecordListMap = getTradePairLastSyncRecords();

        // 为所有需要同步数据的系统交易对创建数据同步任务
        for (TradePairRespDTO tradePairDTO : needSyncTradePairList) {
            // 如果是自发币或者复制币的话则这里不处理
            if (tradePairDTO.getIsCustom() || tradePairDTO.getIsCopy()) {
                continue;
            }
            if (paramEndTime != null) {
                // 如果xxl-job的参数中传了codeList则只获取指定code的数据
                if (CollUtil.isNotEmpty(paramCodeList) && !paramCodeList.contains(tradePairDTO.getCode())) {
                    continue;
                }
                XJLog.info("系统交易对 {} 使用xxl-job参数创建同步任务 截止时间参数:{} 开始时间参数:{}", tradePairDTO.getCode(), paramEndTime, paramStartTime);
                final List<DataSyncTaskDTO> tasks = createHistorySyncTasks(tradePairDTO, paramEndTime, paramStartTime);
                // 因为是xxl-job指定了参数的特定历史时间段的数据拉取所以这里不更新最后的历史记录时间
                tasks.forEach(dataSyncTaskDTO -> {
                    dataSyncTaskDTO.setNeedUpdateLastSyncDate(false);
                });
                queueQueueTasks(tasks);

            } else {
                CandleLastSyncDO candleLastSyncDO = lastSyncRecordListMap.get(tradePairDTO.getCode());
                if (candleLastSyncDO == null) {
                    // 没有同步过任何历史数据
                    XJLog.info("系统交易对 {} 没有任何数据同步的历史记录", tradePairDTO.getCode());
                    // 默认截止时间为今日的23:59:59,开始时间为今天的00:00:00
                    final LocalDateTime now = LocalDateTime.now();
                    LocalDateTime endTime = LocalDateTimeUtil.endOfDay(now);
                    LocalDateTime startTime = LocalDateTimeUtil.beginOfDay(now);
                    queueQueueTasks(createHistorySyncTasks(tradePairDTO, endTime, startTime));
                } else {
                    XJLog.info("系统交易对 {} 最后的同步记录时间:{} ", candleLastSyncDO.getCode(), candleLastSyncDO.getLastSyncDate());
                    // 存在历史同步记录，则以上次同步记录的时间点作为endTime 其中同步记录中记录的时间是最后同步日期的当天起始时间
                    final LocalDateTime lastSyncDate = candleLastSyncDO.getLastSyncDate();
                    // 因为最后的同步时间是保存的startTime，所以这里需要减1分才能拉取前一天的数据
                    LocalDateTime endTime = LocalDateTimeUtil.endOfDay(lastSyncDate.minusMinutes(1));
                    LocalDateTime startTime = LocalDateTimeUtil.beginOfDay(endTime);
                    queueQueueTasks(createHistorySyncTasks(tradePairDTO, endTime, startTime));
                }
            }
        }
        LocalDateTime end = LocalDateTime.now();
        XJLog.info("执行k线历史数据守护任务 请求任务创建完成... {} 耗时:{}s", end, LocalDateTimeUtil.between(start, end).getSeconds());
    }


    private List<DataSyncTaskDTO> createHistorySyncTasks(TradePairRespDTO tradePairDTO,
                                                         LocalDateTime endTime,
                                                         LocalDateTime startTime) {
        XJLog.info("开始创建历史任务 时间范围【{}】--【{}】", startTime, endTime);

        List<DataSyncTaskDTO> taskList = new ArrayList<>();
        final LocalDateTime startTimeInRange = startTimeInConfigureRange(startTime);
        final Duration between = LocalDateTimeUtil.between(startTimeInRange, endTime);
        // 如果endTime比startTimeInRange还早则无须创建任务
        if (between.isNegative()) {
            return Collections.emptyList();
        }
        // 如果startTimeInRange和endTime相等也无须创建任务
        if (between.isZero()) {
            return Collections.emptyList();
        }
        // 如果startTime和endTime两个时间相差小于60秒，则无须发送请求，因为连最小的一分钟间隔都没有超过
        if (between.getSeconds() <= 60) {
            return Collections.emptyList();
        }
        // startTime和endTime两个时间之间相差的天数
        final long days = between.toDays() + 1;
        for (int i = 0; i < days; i++) {
            // 计算每个task的endTime

            // 截止时间为endTime的前一天的最后1分钟
            LocalDateTime taskEndTime = LocalDateTimeUtil.endOfDay(endTime.minusDays(i));
            LocalDateTime taskStartTime = LocalDateTimeUtil.beginOfDay(taskEndTime);

            boolean needSyncMinKline = needSyncMinCandleData(taskEndTime);
            XJLog.info("创建任务 endTime:{} 是否拉取1分钟级别k线:{}", taskEndTime, needSyncMinKline);
            taskList.add(createSyncTask(tradePairDTO, taskStartTime, taskEndTime, true, needSyncMinKline));
        }
        return taskList;
    }

    /**
     * 创建数据同步的任务
     *
     * @param tradePairDTO       交易对信息
     * @param startTime          开始时间
     * @param endTime            截止时间
     * @param isHistoryTask      是否是历史数据任务 true:是
     * @param isNeedSyncMinKline 是否需要同步分钟级别k线 true: 需要
     * @return 任务信息
     */
    private DataSyncTaskDTO createSyncTask(TradePairRespDTO tradePairDTO,
                                           LocalDateTime startTime,
                                           LocalDateTime endTime,
                                           boolean isHistoryTask,
                                           boolean isNeedSyncMinKline) {
        DataSyncTaskDTO task = new DataSyncTaskDTO();
        // 设置任务的数据源
        task.setDataSource(tradePairDTO.getSource());
        // 是否是股票资产
        boolean isStock = TradeAssetTypeEnum.STOCKS.getType().equals(tradePairDTO.getAssetType());
        task.setStock(isStock);
        task.setTradePairCode(tradePairDTO.getCode());
        task.setStartTime(startTime);
        task.setEndTime(endTime);
        task.setHistorySync(isHistoryTask);
        task.setNeedRequestMinKline(isNeedSyncMinKline);
        task.setTradeAssetType(tradePairDTO.getAssetType());
        task.setBaseCode(tradePairDTO.getBaseAsset());
        task.setQuoteCode(tradePairDTO.getQuoteAsset());
        return task;
    }

    /**
     * 获取在配置的同步数据的时间范围内的最晚startTime
     *
     * @param startTime 开始时间
     * @return 在配置范围内的开始时间
     */
    private LocalDateTime startTimeInConfigureRange(LocalDateTime startTime) {
        String beginDateStr = syncProperties.getBeginYear() + "-01-01T00:00:00Z";
        LocalDateTime rangeStartTime = LocalDateTimeUtil.parse(beginDateStr, UTC_DATE_FORMATTER);
        if (startTime.isBefore(rangeStartTime)) {
            return rangeStartTime;
        }
        return startTime;
    }

    /**
     * 是否需要同步分钟的k线数据
     *
     * @return true:需要 false:不需要
     */
    private boolean needSyncMinCandleData(LocalDateTime endTime) {
        // 自然月超过了配置的1分钟k线最长同步月份则返false
        final long between = ChronoUnit.MONTHS.between(endTime.withDayOfMonth(1), LocalDateTime.now().withDayOfMonth(1)) + 1;
        return between < syncProperties.getMaxMinKlineMonthCount();
    }

    /**
     * 获取所有启用数据同步的交易对列表
     *
     * @param isHistoryTask 是否是历史数据的同步任务
     * @return 交易对列表
     */
    private List<TradePairRespDTO> getSyncEnableTradePairList(boolean isHistoryTask) {
        // 获取系统所有交易对数据, 包括不可用的
        List<TradePairRespDTO> tradePairList = tradePairApi.getTradePairEnableListCached();
        // 非历史数据同步任务则直接返回所有可用的交易对
        if (!isHistoryTask) {
            return tradePairList;
        }
        // 过滤出同步状态为可用的交易对
        List<TradePairRespDTO> needSyncDataTradePairList = tradePairList.stream().filter(tradePairRespDTO -> CommonStatusEnum.isEnable(tradePairRespDTO.getSyncStatus())).toList();
        if (needSyncDataTradePairList.isEmpty()) {
            XJLog.info("没有任何需要同步历史数据的交易对");
            XxlJobHelper.handleSuccess();
            return Collections.emptyList();
        }
        return needSyncDataTradePairList;
    }

    /**
     * 获取所有交易对最后的同步记录
     * <p>
     * key: 交易对代码
     *
     * @return 同步记录map
     */
    private Map<String, CandleLastSyncDO> getTradePairLastSyncRecords() {
        Collection<CandleLastSyncDO> lastSyncRecords = lastSyncService.getLastSyncRecords();
        if (CollUtil.isEmpty(lastSyncRecords)) {
            return Collections.emptyMap();
        }
        HashMap<String, CandleLastSyncDO> map = new HashMap<>(lastSyncRecords.size());
        for (CandleLastSyncDO lastSyncRecord : lastSyncRecords) {
            map.put(lastSyncRecord.getCode(), lastSyncRecord);
        }
        return map;
    }

    /**
     * 创建同步数据的http请求任务
     *
     * @param task 同步任务
     * @return 请求列表
     */
    private Collection<DataSyncRequestDTO> createHistorySyncRequests(DataSyncTaskDTO task) {
        List<DataSyncRequestDTO> requests = new ArrayList<>();
        for (CandleTimeRangeEnum timeRange : CandleTimeRangeEnum.values()) {
            // 如果不需要同步分钟级别的数据则跳过
            if (!task.isNeedRequestMinKline() && timeRange.equals(CandleTimeRangeEnum.MIN_ONE)) {
                continue;
            }

            // 截止时间，单位秒
            long endTime = LocalDateTimeUtil.toEpochMilli(task.getEndTime()) / 1000;

            Integer minBarCount = task.getBarCount();
            //如果task中没有设置请求多少条k线则按照k线的时间级别获取请求k线的条数，最多一次拉取1000条k线
            if (minBarCount == null) {
                minBarCount = Math.min(timeRange.getCandleCountMax(), 1000);
            }
            requests.add(createHistoryRequest(task, timeRange, task.getStartTime(), endTime, minBarCount));

            //如果是同步的历史数据则需要创建分钟级别k线的第二个请求，否则无法将一天的数据同步完整，因为一个请求最多拉取1000条数据
            if (task.isHistorySync() && timeRange.equals(CandleTimeRangeEnum.MIN_ONE)) {
                // 创建第二个1分钟线的请求时把endTime往前移动440分钟
                endTime = endTime - 60000;
                minBarCount = 440;
                requests.add(createHistoryRequest(task, timeRange, task.getStartTime(), endTime, minBarCount));
            }
        }
        return requests;
    }

    /**
     * 创建单个请求
     *
     * @param task      任务
     * @param timeRange 时间范围枚举
     * @param startTime 开始时间
     * @param endTimeTS 截止时间
     * @param barCount  k线条数
     * @return 请求信息
     */
    private DataSyncRequestDTO createHistoryRequest(DataSyncTaskDTO task,
                                                    CandleTimeRangeEnum timeRange,
                                                    LocalDateTime startTime,
                                                    long endTimeTS,
                                                    int barCount) {
        DataSyncRequestDTO requestDTO = new DataSyncRequestDTO();
        requestDTO.setNeedUpdateLastSyncDate(task.isNeedUpdateLastSyncDate());
        requestDTO.setStock(task.isStock());
        requestDTO.setDataSource(task.getDataSource());
        requestDTO.setTradePairCode(task.getTradePairCode());
        requestDTO.setBaseCode(task.getBaseCode());
        requestDTO.setQuoteCode(task.getQuoteCode());
        requestDTO.setTradeAssetType(task.getTradeAssetType());
        requestDTO.setTimeRangeEnum(timeRange);
        requestDTO.setStartTime(startTime);
        requestDTO.setEndTS(endTimeTS);
        requestDTO.setBarCount(barCount);
        requestDTO.setNeedRequestMinKline(task.isNeedRequestMinKline());
        if (task.isHistorySync() && CandleDataSource.POLYGON.getValue() == task.getDataSource()) {
            requestDTO.setPolygonHistorySync(true);
        }
        return requestDTO;
    }

    /**
     * 创建实时k线
     *
     * @param task 同步任务
     * @return 请求列表
     */
    private DataSyncRequestDTO createRealTimeRequest(DataSyncTaskDTO task) {
        DataSyncRequestDTO requestDTO = new DataSyncRequestDTO();
        requestDTO.setNeedUpdateLastSyncDate(false);
        // 需要发送批量请求
        requestDTO.setBatch(true);
        requestDTO.setStock(task.isStock());
        requestDTO.setDataSource(task.getDataSource());
        requestDTO.setTradePairCode(task.getTradePairCode());
        requestDTO.setBaseCode(task.getBaseCode());
        requestDTO.setQuoteCode(task.getQuoteCode());
        requestDTO.setTradeAssetType(task.getTradeAssetType());
        // 这个地方需要创建所有k线级别
        final List<CandleTimeRangeEnum> timeRangeEnums = Arrays.asList(CandleTimeRangeEnum.MIN_ONE,
                CandleTimeRangeEnum.MIN_FIVE, CandleTimeRangeEnum.MIN_FIFTEEN, CandleTimeRangeEnum.MIN_THIRTY,
                CandleTimeRangeEnum.HOUR_ONE, CandleTimeRangeEnum.HOUR_TWO, CandleTimeRangeEnum.HOUR_FOUR,
                CandleTimeRangeEnum.DAY_ONE, CandleTimeRangeEnum.WEEK_ONE, CandleTimeRangeEnum.MONTH_ONE);
        requestDTO.setTimeRangeEnums(timeRangeEnums);
        requestDTO.setStartTime(task.getStartTime());
        // 如果是实时的请求传0表示从当前时间往前获取数据
        requestDTO.setEndTS(0L);
        return requestDTO;
    }

    /**
     * 创建实时请求列表
     *
     * @param task 同步任务
     * @return 请求列表
     */
    private List<DataSyncRequestDTO> createPolygonRealTimeRequests(DataSyncTaskDTO task) {
        final List<CandleTimeRangeEnum> timeRangeEnums = Arrays.asList(CandleTimeRangeEnum.MIN_ONE,
                CandleTimeRangeEnum.MIN_FIVE, CandleTimeRangeEnum.MIN_FIFTEEN, CandleTimeRangeEnum.MIN_THIRTY,
                CandleTimeRangeEnum.HOUR_ONE, CandleTimeRangeEnum.HOUR_TWO, CandleTimeRangeEnum.HOUR_FOUR,
                CandleTimeRangeEnum.DAY_ONE, CandleTimeRangeEnum.WEEK_ONE, CandleTimeRangeEnum.MONTH_ONE);

        List<DataSyncRequestDTO> requests = new ArrayList<>();
        for (CandleTimeRangeEnum timeRangeEnum : timeRangeEnums) {
            DataSyncRequestDTO requestDTO = new DataSyncRequestDTO();
            requestDTO.setNeedUpdateLastSyncDate(false);
            requestDTO.setPolygonRealTimeSync(true);
            requestDTO.setBatch(false);
            requestDTO.setStock(task.isStock());
            requestDTO.setDataSource(task.getDataSource());
            requestDTO.setTradePairCode(task.getTradePairCode());
            requestDTO.setBaseCode(task.getBaseCode());
            requestDTO.setQuoteCode(task.getQuoteCode());
            // 这个地方需要创建所有k线级别
            requestDTO.setTimeRangeEnum(timeRangeEnum);
            requestDTO.setStartTime(task.getStartTime());
            long startTS = task.getStartTime().toInstant(ZoneOffset.UTC).toEpochMilli();
            requestDTO.setStartTS(startTS);
            requestDTO.setEndTS(System.currentTimeMillis());
            requests.add(requestDTO);
        }
        return requests;
    }

    /**
     * 添加单个任务到队列中
     *
     * @param taskList 任务列表
     */
    public void queueQueueTasks(Collection<DataSyncTaskDTO> taskList) {
        for (DataSyncTaskDTO task : taskList) {
            if (CandleDataSource.POLYGON.getValue() == task.getDataSource()) {
                if (task.isHistorySync()) {
                    final Collection<DataSyncRequestDTO> syncRequests = createHistorySyncRequests(task);
                    polygonHistoryRequestQueue.addAll(syncRequests);
                } else {
                    final List<DataSyncRequestDTO> requests = createPolygonRealTimeRequests(task);
                    polygonRealTimeRequestQueue.addAll(requests);
                }
            } else {
                if (task.isHistorySync()) {
                    final Collection<DataSyncRequestDTO> syncRequests = createHistorySyncRequests(task);
                    historyRequestQueue.addAll(syncRequests);
                } else if (CandleDataSource.ALLTICK.getValue() == task.getDataSource()) {
                    final DataSyncRequestDTO syncRequest = createRealTimeRequest(task);
                    realTimeRequestQueue.add(syncRequest);
                }
            }
        }
    }

    private void pollRealTimeQueue() {
        final boolean allow = RateLimiterSimpleWindow.tryAcquire();
        if (!allow) {
            return;
        }
        if (!realTimeRequestQueue.isEmpty()) {
            XJLog.log("实时请求队列中的请求数量:[{}] 历史请求队列中的任务数量:[{}] polygon实时任务队列数量:[{}]",
                    realTimeRequestQueue.size(),
                    historyRequestQueue.size(),
                    polygonRealTimeRequestQueue.size());
            DataSyncRequestDTO request = realTimeRequestQueue.poll();
            if (request != null) {
                taskExecutor.executeRequest(request);
            }
        }
    }

    private void pollPolygonRealTimeQueue() {
        if (!polygonRealTimeRequestQueue.isEmpty()) {
            XJLog.log("实时请求队列中的请求数量:[{}] 历史请求队列中的任务数量:[{}] polygon实时任务队列数量:[{}] polygon历史任务队列数量:[{}]",
                    realTimeRequestQueue.size(), historyRequestQueue.size(),
                    polygonRealTimeRequestQueue.size(), polygonHistoryRequestQueue.size());
            DataSyncRequestDTO request = polygonRealTimeRequestQueue.poll();
            if (null != request) {
                taskExecutor.executeRequest(request);
            }
        }
    }

    private void pollPolygonHistoryQueue() {
        if (!polygonHistoryRequestQueue.isEmpty()) {
            XJLog.log("实时请求队列中的请求数量:[{}] 历史请求队列中的任务数量:[{}] polygon实时任务队列数量:[{}] polygon历史任务队列数量:[{}]",
                    realTimeRequestQueue.size(), historyRequestQueue.size(),
                    polygonRealTimeRequestQueue.size(), polygonHistoryRequestQueue.size());
            DataSyncRequestDTO request = polygonHistoryRequestQueue.poll();
            if (null != request) {
                taskExecutor.executeRequest(request);
            }
        }
    }

    /**
     * 拉取队列中的请求任务
     */
    private void pollHistoryQueue() {
        final boolean allow = RateLimiterSimpleWindow.tryAcquire();
        if (!allow) {
            return;
        }
        // 如果实时请求队列中任务就先临时停止历史请求
        if (!realTimeRequestQueue.isEmpty()) {
            return;
        }
        if (!historyRequestQueue.isEmpty()) {
            XJLog.log("实时请求队列中的请求数量:{} 历史请求队列中的任务数量:{}", realTimeRequestQueue.size(), historyRequestQueue.size());
            DataSyncRequestDTO request = historyRequestQueue.poll();
            if (request != null) {
                taskExecutor.executeRequest(request);
            }
        }
    }
}
