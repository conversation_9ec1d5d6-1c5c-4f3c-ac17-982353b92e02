package com.rf.exchange.server.data.datasource.alltick.websocket;

import com.rf.exchange.server.data.core.websocket.handler.CandleDataWebSocketHandler;
import com.rf.exchange.server.data.enums.CandleDataSource;
import com.rf.exchange.server.data.service.CandleDataWebSocketService;
import jakarta.annotation.Resource;
import org.springframework.context.event.ApplicationEventMulticaster;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;


/**
 * <AUTHOR>
 * @since 2024-06-27
 */
public class AllTickWebSocketHandler extends CandleDataWebSocketHandler {

    @Resource
    private ApplicationEventMulticaster eventMulticaster;

    @Resource
    private AllTickMessageQueue allTickMessageQueue;

    @Resource
    private CandleDataWebSocketService dataWebSocketService;

    @Resource
    private AllTickMessageProcessor messageProcessor;

    public AllTickWebSocketHandler(CandleDataSource dataSource) {
        super(dataSource);
    }

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        super.afterConnectionEstablished(session);
        allTickMessageQueue.setAllTickWebSocketSession(session);
        // 发送订阅消息
        dataWebSocketService.sendSubscribeMessage(CandleDataSource.ALLTICK);
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        super.handleTextMessage(session, message);
        messageProcessor.process(message.getPayload());
    }
}