package com.rf.exchange.server.data.datasource.polygon.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-10-08
 */
@Data
public class PolygonKLinePathArgus {
    /**
     * Polygon的交易代码
     */
    private String ticker;
    /**
     * 时间窗口尺度的
     */
    private String multiplier;
    /**
     * 时间窗口尺度
     */
    private String timespan;
    /**
     * 起始时间(时间戳)
     * 格式：YYYY-MM-DD或者毫秒时间戳
     */
    private String from;
    /**
     * 结束时间(时间戳)
     * 格式：YYYY-MM-DD或者毫秒时间戳
     */
    private String to;
}
