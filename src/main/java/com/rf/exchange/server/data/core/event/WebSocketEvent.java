package com.rf.exchange.server.data.core.event;

import lombok.*;
import org.springframework.context.ApplicationEvent;
import org.springframework.core.ResolvableType;
import org.springframework.core.ResolvableTypeProvider;

/**
 * <AUTHOR>
 * @since 2024-06-26
 */
@Getter
@Setter
public class WebSocketEvent<T> extends ApplicationEvent implements ResolvableTypeProvider {

    private Object source;
    private String message;
    private T payload;

    public WebSocketEvent(Object source, String message, T payload) {
        super(source);
        this.source = source;
        this.message = message;
        this.payload = payload;
    }

    @Override
    public ResolvableType getResolvableType() {
        return ResolvableType.forClassWithGenerics(getClass(), ResolvableType.forInstance(getSource()));
    }
}
