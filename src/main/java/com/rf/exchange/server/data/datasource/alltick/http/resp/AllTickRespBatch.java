package com.rf.exchange.server.data.datasource.alltick.http.resp;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-07-25
 */
@Data
public class AllTickRespBatch {
    private int ret;
    private String msg;
    private String trace;
    private DataPojo data;

    @Data
    public static class DataPojo {
        List<KlineListItem> klineList;
    }

    @Data
    public static class KlineListItem {
        private String code;
        private Integer klineType;
        private List<AllTickKLine> klineData;
    }
}
