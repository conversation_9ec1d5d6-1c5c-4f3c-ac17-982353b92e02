package com.rf.exchange.server.data.job;

import java.util.Collections;
import java.util.List;

import org.springframework.stereotype.Component;

import com.rf.exchange.framework.common.enums.CommonStatusEnum;
import com.rf.exchange.framework.xxljob.core.log.XJLog;
import com.rf.exchange.module.exc.api.tradepair.TradePairApi;
import com.rf.exchange.module.exc.api.tradepair.dto.TradePairRespDTO;
import com.rf.exchange.server.data.datasource.jiangshan.http.JiangShanHttpClient;
import com.rf.exchange.server.data.enums.CandleDataSource;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * @description:
 * @author: Petter
 * @create: 2024-10-10
 **/
@Slf4j
@Component
public class JiangShanKineDataGuardJob {

    @Resource
    private TradePairApi tradePairApi;
    @Resource
    private JiangShanHttpClient jiangShanHttpClient;

    /**
     * 获取所有启用数据同步的交易对列表
     *
     * @return 交易对列表
     */
    private List<TradePairRespDTO> getJiangShanEnableTradePairList() {
        // 获取系统所有交易对数据, 包括不可用的
        List<TradePairRespDTO> tradePairList = tradePairApi.getTradePairEnableListCached();
        // 过滤出同步状态为可用的交易对
        List<TradePairRespDTO> needSyncDataTradePairList = tradePairList.stream()
            .filter(tradePairRespDTO -> CommonStatusEnum.isEnable(tradePairRespDTO.getSyncStatus())
                && tradePairRespDTO.getSource() == CandleDataSource.JIANG_SHAN.getValue())
            .toList();
        if (needSyncDataTradePairList.isEmpty()) {
            XJLog.info("没有任何需要同步历史数据的交易对");
            XxlJobHelper.handleSuccess();
            return Collections.emptyList();
        }
        return needSyncDataTradePairList;
    }

    @XxlJob("jiangShanRealTimeDataGuardJob")
    public void jiangShanRealTimeDataGuardJob() {
        List<TradePairRespDTO> tradePairRespDTOList = this.getJiangShanEnableTradePairList();
        log.info("匠山k线定时任务启动，需要请求的的交易对数据量：{}", tradePairRespDTOList.size());
        for (TradePairRespDTO tradePairRespDTO : tradePairRespDTOList) {
            jiangShanHttpClient.sendGetRequest(tradePairRespDTO);
        }

    }
}
