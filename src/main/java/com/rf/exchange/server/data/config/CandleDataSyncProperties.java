package com.rf.exchange.server.data.config;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 * @since 2024-07-23
 */
@Data
@Validated
@ConfigurationProperties(prefix = "exchange.candle-sync")
public class CandleDataSyncProperties {

    /**
     * 数据同步是否开启
     */
    @NotNull(message = "enable不能为空")
    private Boolean enable;
    /**
     * 历史数据最早同步到哪一年
     */
    @NotNull(message = "beginYear不能为空")
    private Integer beginYear;
    /**
     * 分钟k线最多同步几个月份
     * 包扩1分钟，3分钟 5分钟 所有分钟级别的k线
     */
    @NotNull(message = "maxMinKlineMonthCount不能为空")
    private Integer maxMinKlineMonthCount;
}
