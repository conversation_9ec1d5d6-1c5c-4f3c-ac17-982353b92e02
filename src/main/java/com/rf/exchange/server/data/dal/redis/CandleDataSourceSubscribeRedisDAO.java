package com.rf.exchange.server.data.dal.redis;

import cn.hutool.core.util.StrUtil;
import com.rf.exchange.framework.common.util.json.JsonUtils;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

import static com.rf.exchange.server.data.dal.redis.RedisKeyConstants.CANDLE_DATASOURCE_SUBSCRIBE;

/**
 * <AUTHOR>
 * @since 2024-06-26
 */
@Repository
public class CandleDataSourceSubscribeRedisDAO {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 批量缓存订阅关系
     *
     * @param name 数据源编号
     * @param codes 订阅的交易对代码
     */
    public void set(String name, List<String> codes) {
        String redisKey = formatKey(name);
        String jsonString = JsonUtils.toJsonString(codes);
        stringRedisTemplate.opsForValue().set(redisKey, jsonString);
    }

    /**
     * 获取已经存在的订阅关系
     * @param name 数据源名称
     * @return 订阅的交易对列表
     */
    public List<String> get(String name) {
        String redisKey = formatKey(name);
        String jsonStr = stringRedisTemplate.opsForValue().get(redisKey);
        if (StrUtil.isEmpty(jsonStr)) {
            return Collections.emptyList();
        }
        return JsonUtils.parseArray(jsonStr, String.class);
    }

    /**
     * 删除订阅关系
     *
     * @param name 数据源名称
     */
    public void delete(String name) {
        String redisKey = formatKey(name);
        stringRedisTemplate.delete(redisKey);
    }

    private String formatKey(String name) {
        return CANDLE_DATASOURCE_SUBSCRIBE + ":" + name;
    }
}
