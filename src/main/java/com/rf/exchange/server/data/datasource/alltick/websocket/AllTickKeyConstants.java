package com.rf.exchange.server.data.datasource.alltick.websocket;

/**
 * <AUTHOR>
 * @since 2024-07-03
 */
public interface AllTickKeyConstants {

    String KEY_RET = "ret";
    String KEY_CMD_ID = "cmd_id";
    String KEY_SEQ_ID = "seq_id";
    String KEY_CODE = "code";
    String KEY_TRACE = "trace";
    String KEY_VOLUME = "volume";
    String KEY_TURNOVER = "turnover";
    String KEY_TRADE_DIRECTION = "trade_direction";
    String KEY_DATA = "data";
    String KEY_TICK_TIMe = "tick_time";
    String KEY_PRICE = "price";
    String KEY_DEPTH = "depth_level";
    String KEY_SYMBOL_LIST = "symbol_list";
    String KEY_CANCEL_TYPE = "cancel_type";
    String KEY_BIDS = "bids";
    String KEY_ASKS = "asks";
}
