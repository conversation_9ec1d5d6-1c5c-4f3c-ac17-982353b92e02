package com.rf.exchange.server.data.service;

import com.rf.exchange.module.candle.enums.CandleTimeRangeEnum;
import com.rf.exchange.server.data.datasource.alltick.http.resp.AllTickKLine;

import java.util.List;

/**
 * k线数据同步服务
 *
 * <AUTHOR>
 * @since 2024-07-06
 */
public interface CandleDataSyncService {

    /**
     * 初始化历史数据表
     */
    void createCandleDataTable();

    /**
     * 插入k线到数据库中
     *
     * @param tradePairCode 系统交易对代码
     * @param timeRangeEnum 时间范围类型枚举
     * @param kLineList     k线了列表
     */
    void insertCandleRecords(String tradePairCode, CandleTimeRangeEnum timeRangeEnum, List<AllTickKLine> kLineList);

}
