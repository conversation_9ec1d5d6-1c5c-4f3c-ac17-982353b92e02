package com.rf.exchange.server.data.datasource.alltick.websocket;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024-06-28
 */
public interface MessageBuilder {

    /**
     * 创建ws的订阅消息
     *
     * @param tradePairCodes 交易对代码集合
     * @param cmdId          ws订阅命令
     * @param extraInfo      附加信息
     * @return 发送到ws的消息
     */
    AllTickCandleIdMessage buildSubscribeMessage(Set<String> tradePairCodes, Integer cmdId, Map<String, Object> extraInfo);

    /**
     * 创建ws的取消订阅消息
     *
     * @return 发送到ws的消息
     */
    AllTickCandleIdMessage buildUnsubscribeMessage(Set<String> tradePairCodes, Integer cmdId);

    /**
     * 创建ws的心跳消息
     *
     * @return 发送到ws的消息
     */
    AllTickCandleIdMessage buildHeartbeatMessage();

    /**
     * 根据已有消息重新构建消息
     *
     * @param message 已有的消息
     * @return 发送到ws的消息
     */
    AllTickCandleIdMessage rebuildMessage(Object message);
}
