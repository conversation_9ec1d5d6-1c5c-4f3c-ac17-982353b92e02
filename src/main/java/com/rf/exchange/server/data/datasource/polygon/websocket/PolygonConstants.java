package com.rf.exchange.server.data.datasource.polygon.websocket;

/**
 * <AUTHOR>
 * @since 2024-10-19
 */
public interface PolygonConstants {
    String REQ_KEY_ACTION = "action";
    String REQ_KEY_PARAMS = "params";
    String REQ_ACTION_SUB = "subscribe";
    String REQ_ACTION_AUTH = "auth";

    String RESP_KEY_EV = "ev";
    String RESP_KEY_PAIR_CRYPTO = "pair";
    String RESP_KEY_PAIR_FOREX = "p";
    String RESP_KEY_STATUS = "status";
    String RESP_KEY_SYM = "sym";
    String RESP_KEY_MESSAGE = "message";

    String RESP_VAL_EV_XAS = "XAS";
    String RESP_VAL_EV_XQ = "XQ";
    String RESP_VAL_EV_XT = "XT";
    String RESP_VAL_EV_CAS = "CAS";
    String RESP_VAL_EV_C = "C";
}
