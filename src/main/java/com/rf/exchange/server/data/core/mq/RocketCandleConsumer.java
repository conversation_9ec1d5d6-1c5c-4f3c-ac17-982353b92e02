package com.rf.exchange.server.data.core.mq;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.google.common.collect.Lists;
import com.rf.exchange.module.candle.api.ControlPlanApi;
import com.rf.exchange.module.candle.api.dto.ControlPlanDTO;
import com.rf.exchange.module.candle.api.dto.ControlPricePointDTO;
import com.rf.exchange.module.candle.api.dto.CurrentPriceRespDTO;
import com.rf.exchange.module.candle.api.dto.TodayKlinePriceDTO;
import com.rf.exchange.module.candle.dal.redis.CandleCurrentOrderBookRedisDAO;
import com.rf.exchange.module.candle.dal.redis.CandleCurrentPriceRedisDAO;
import com.rf.exchange.module.candle.dal.redis.CandleCustomTradePairRedisDAO;
import com.rf.exchange.module.candle.dal.redis.dto.CandleOrderBookDTO;
import com.rf.exchange.module.candle.dal.redis.dto.CandleOrderBookListDTO;
import com.rf.exchange.module.exc.api.tradepair.TradePairApi;
import com.rf.exchange.module.exc.api.tradepair.dto.TradePairRespDTO;
import com.rf.exchange.module.exc.dal.redis.TradePairControlRedisDAO;
import com.rf.exchange.server.data.context.TodayKlineHolder;
import com.rf.exchange.server.data.core.marketclose.CandleMarketCloseDataGenerator;
import com.rf.exchange.server.data.core.mq.dto.UpdateDto;
import com.rf.exchange.server.data.core.mq.message.CandlePriceMessage;
import com.rf.exchange.server.data.datasource.alltick.convert.AllTickOrderBookConvert;
import com.rf.exchange.server.data.datasource.alltick.dto.AllTickDataDTO;
import com.rf.exchange.server.data.datasource.alltick.dto.AllTickDealQuoteDTO;
import com.rf.exchange.server.data.datasource.alltick.dto.AllTickPKQuoteDTO;
import com.rf.exchange.server.data.datasource.alltick.util.AllTickUtil;
import com.rf.exchange.server.data.datasource.alltick.websocket.AllTickCommand;
import com.rf.exchange.server.data.datasource.jiangshan.dto.JiangShanPriceDTO;
import com.rf.exchange.server.data.datasource.jiangshan.util.JiangShanTradePairStatusHolder;
import com.rf.exchange.server.data.datasource.jiangshan.util.JiangShanUtil;
import com.rf.exchange.server.data.datasource.polygon.util.PolygonUtil;
import com.rf.exchange.server.data.datasource.polygon.websocket.PolygonConstants;
import com.rf.exchange.server.data.enums.CandleDataSource;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.rf.exchange.server.data.datasource.polygon.websocket.PolygonConstants.*;

@FunctionalInterface
interface QuoteHandleFunction<String, Q, U> {
    void handle(String name, Q quote, U isMarketClose);
}

/**
 * <AUTHOR>
 * @since 2024-07-01
 */
@Slf4j
@Service
@RocketMQMessageListener(topic = "${exchange.candle-rocketmq.topic}",
        consumerGroup = "${exchange.candle-rocketmq.consumer-group}",
        selectorExpression = "${exchange.candle-rocketmq.topic-tag-price} || ${exchange.candle-rocketmq.topic-tag-orderbook}")
public class RocketCandleConsumer implements RocketMQListener<RocketCandleMessage> {

    private static final ObjectMapper MAPPER =
            new ObjectMapper().setPropertyNamingStrategy(new PropertyNamingStrategies.SnakeCaseStrategy());

    /**
     * 交易对的更新信息map 格式：<交易对代码: 更新信息>
     */
    private static final ConcurrentHashMap<String, UpdateDto> CODE_LAST_TICKTIME_MAP = new ConcurrentHashMap<>(100);

    @Resource
    private CandleCurrentPriceRedisDAO priceRedisDAO;
    @Resource
    private CandleCurrentOrderBookRedisDAO orderBookRedisDAO;
    @Resource
    private AppCandlePriceProducer appPriceProducer;
    @Resource
    private TradePairApi tradePairApi;
    @Resource
    private TradePairControlRedisDAO tradePairControlRedisDAO;
    @Resource
    private CandleCustomTradePairRedisDAO candleCustomTradePairRedisDAO;
    @Resource
    private CandleMarketCloseDataGenerator marketCloseDataGenerator;
    @Resource
    private ControlPlanApi controlPlanApi;

    @Override
    public void onMessage(RocketCandleMessage message) {
        log.debug("--- consumer 收到消息: dataSource:{} payload:{}", message.getDataSource(), message.getPayload());
        if (message.getDataSource() == CandleDataSource.ALLTICK.getValue()) {
            String payload = message.getPayload();
            if (StrUtil.isNotEmpty(payload)) {
                AllTickDataDTO<Map<String, Object>> dataDto = null;
                try {
                    dataDto = MAPPER.readValue(payload, new TypeReference<>() {
                    });
                } catch (JsonProcessingException | NumberFormatException e) {
                    log.error("Rocket消费candle消息异常 消息转DTO失败: {}", e.getMessage());
                }
                if (dataDto != null) {
                    if (Integer.parseInt(dataDto.getCmdId()) == AllTickCommand.DEAL_PRICE_MSG.getCmdCode()) {
                        try {
                            AllTickDealQuoteDTO quoteDTO = BeanUtil.toBean(dataDto.getData(), AllTickDealQuoteDTO.class,
                                    CopyOptions.create().setAutoTransCamelCase(true));
                            String tradePairCode = AllTickUtil.getTradePairCode(quoteDTO.getCode());

                            boolean isMarketClose = StrUtil.isEmpty(quoteDTO.getSeq());
                            CurrentPriceRespDTO currentPriceRespDTO =
                                    covertToPriceResp(tradePairCode, quoteDTO, isMarketClose);
                            // 优先处理成交消息
                            process(CandleDataSource.ALLTICK.getValue(), tradePairCode, quoteDTO.getTickTime(),
                                    currentPriceRespDTO, isMarketClose, this::handleDealQuote);

                            // 转发交易对价格信息到APP服务的rocketMQ主题
                            appPriceProducer.sendAllTickPriceMessage(tradePairCode, quoteDTO);
                        } catch (IllegalArgumentException e) {
                            log.error("Rocket消费candle消息异常 读取成交报价的data失败: {}", e.getMessage());
                        }
                    } else if (Integer.parseInt(dataDto.getCmdId()) == AllTickCommand.PK_PRICE_MSG.getCmdCode()) {
                        try {
                            AllTickPKQuoteDTO quoteDTO = BeanUtil.toBean(dataDto.getData(), AllTickPKQuoteDTO.class,
                                    CopyOptions.create().setAutoTransCamelCase(true));
                            String tradePairCode = AllTickUtil.getTradePairCode(quoteDTO.getCode());
                            boolean isMarketClose = StrUtil.isEmpty(quoteDTO.getSeq());
                            CandleOrderBookListDTO candleOrderBookListDTO =
                                    AllTickOrderBookConvert.INSTANCE.convert2(quoteDTO);
                            process(CandleDataSource.ALLTICK.getValue(), tradePairCode, quoteDTO.getTickTime(),
                                    candleOrderBookListDTO, isMarketClose, this::handlePKQuote);
                        } catch (IllegalArgumentException e) {
                            log.error("Rocket消费candle消息异常 读取盘口报价的data失败: {}", e.getMessage());
                        }
                    }
                }
            } else {
                log.error("consumer 消息逻辑异常 payload为空 {}", message);
            }
        } else if (message.getDataSource() == CandleDataSource.POLYGON.getValue()) {
            final JSONObject jsonObj = JSONUtil.parseObj(message.getPayload());
            final String evVal = jsonObj.getStr(RESP_KEY_EV);
            String pairKey = null;
            if (RESP_VAL_EV_C.equals(evVal) || RESP_VAL_EV_CAS.equals(evVal)) {
                pairKey = RESP_KEY_PAIR_FOREX;
            } else if (RESP_VAL_EV_XT.equals(evVal) || RESP_VAL_EV_XQ.equals(evVal) || RESP_VAL_EV_XAS.equals(evVal)) {
                pairKey = RESP_KEY_PAIR_CRYPTO;
            }
            if (StrUtil.isNotEmpty(pairKey)) {
                final String pair = jsonObj.getStr(pairKey);
                final String tradePairCode = PolygonUtil.tradeCode(pair, true);
                if (StrUtil.isNotEmpty(tradePairCode)) {
                    CurrentPriceRespDTO priceRespDTO = convertToPriceResp(tradePairCode, jsonObj, false);
                    process(CandleDataSource.POLYGON.getValue(), tradePairCode, String.valueOf(priceRespDTO.getTickTime()), priceRespDTO, false, this::handleDealQuote);

                    // 转发交易对价格信息到APP服务的rocketMQ主题
                    CandlePriceMessage candlePriceMessage = CandlePriceMessage.builder()
                            .code(tradePairCode)
                            .price(String.valueOf(priceRespDTO.getCurrentPrice()))
                            .volume(String.valueOf(priceRespDTO.getVolume()))
                            .turnover(String.valueOf(priceRespDTO.getTurnover()))
                            .timestamp(String.valueOf(priceRespDTO.getTimestamp()))
                            .build();
                    appPriceProducer.sendPriceMessage(candlePriceMessage);

                    // 生成委托订单
                    //if (DateUtil.currentSeconds() % 2 == 0) {
                    CandleOrderBookListDTO candleOrderBookListDTO =
                            generateCandleOrderBookListDTO(tradePairCode,
                                    priceRespDTO.getCurrentPrice(),
                                    Optional.ofNullable(priceRespDTO.getVolume()).orElse(new BigDecimal(1)));
                    process(CandleDataSource.POLYGON.getValue(), tradePairCode, String.valueOf(priceRespDTO.getTimestamp()), candleOrderBookListDTO, false, this::handlePKQuote);
                    //}
                } else {
                    log.error("[{}] 无法正确的转换成系统交易对代码", pair);
                }
            } else {
                log.error("无法解析消息中的ticker名称 消息内容:[{}]", jsonObj);
            }

        } else if (message.getDataSource() == CandleDataSource.JIANG_SHAN.getValue()) {
            JiangShanPriceDTO jiangShanPriceDTO = JSONUtil.toBean(message.getPayload(), JiangShanPriceDTO.class);
            if (Objects.nonNull(jiangShanPriceDTO)) {
                // 根据三方代码获取系统交易对代码
                String tradePairCode = JiangShanUtil.getTradePairCode(jiangShanPriceDTO.getSymbol());
                CurrentPriceRespDTO priceRespDTO = covertToPriceResp(tradePairCode, jiangShanPriceDTO, false);
                CandlePriceMessage candlePriceMessage = CandlePriceMessage.builder()
                        .code(priceRespDTO.getTradePairCode()).price(String.valueOf(priceRespDTO.getCurrentPrice()))
                        .volume(String.valueOf(priceRespDTO.getVolume()))
                        .turnover(String.valueOf(priceRespDTO.getCurrentPrice().multiply(priceRespDTO.getVolume())))
                        .timestamp(String.valueOf(priceRespDTO.getTimestamp())).build();
                // 处理成交消息
                process(CandleDataSource.JIANG_SHAN.getValue(), tradePairCode, jiangShanPriceDTO.getTimestamp(),
                        priceRespDTO, JiangShanTradePairStatusHolder.isMarketClosed(tradePairCode), this::handleDealQuote);
                // 转发交易对价格信息到APP服务的rocketMQ主题
                appPriceProducer.sendPriceMessage(candlePriceMessage);

                // 根据当前价格生成委托单信息 -- 3秒生成一次
                if (DateUtil.currentSeconds() % 3 == 0) {
                    CandleOrderBookListDTO candleOrderBookListDTO =
                            generateCandleOrderBookListDTO(tradePairCode, new BigDecimal(jiangShanPriceDTO.getBid()),
                                    new BigDecimal(Optional.ofNullable(jiangShanPriceDTO.getVolume()).orElse("1")));
                    // 处理委托单
                    long tickTime = Long.parseLong(jiangShanPriceDTO.getTimestamp()) + 1L;
                    process(CandleDataSource.JIANG_SHAN.getValue(), tradePairCode, String.valueOf(tickTime),
                            candleOrderBookListDTO, JiangShanTradePairStatusHolder.isMarketClosed(tradePairCode),
                            this::handlePKQuote);
                }

            }

        }
    }

    /**
     * 是否可以有效的tickTime避免重复解析
     *
     * @param code       交易对代码
     * @param tickTime   tick的时间戳
     * @param datasource 数据源
     * @return true: 有效的消息
     */
    private boolean isValidTickTime(String code, long tickTime, int datasource) {
        if (CODE_LAST_TICKTIME_MAP.containsKey(code)) {
            Long lastTickTime = CODE_LAST_TICKTIME_MAP.get(code).getTickTime();
            if (lastTickTime >= tickTime) {
                return false;
            }
        }
        UpdateDto updateDto = new UpdateDto();
        updateDto.setTickTime(tickTime);
        updateDto.setCode(code);
        updateDto.setSource(datasource);
        CODE_LAST_TICKTIME_MAP.put(code, updateDto);
        return true;
    }

    // 通用处理方法
    private <Q> void process(int dataSource, String tradePairCode, String tickTime, Q quoteDTO, Boolean isMarketClose,
                             QuoteHandleFunction<String, Q, Boolean> handler) {
        if (quoteDTO == null) {
            return;
        }
        try {
            long tick = Long.parseLong(tickTime);
            if (isValidTickTime(tradePairCode, tick, dataSource) || isMarketClose) {
                handler.handle(tradePairCode, quoteDTO, isMarketClose);
            }
        } catch (NumberFormatException e) {
            log.error("tickTime的格式错误 {}", tickTime);
        }
    }

    /**
     * 处理成交报价消息对象
     *
     * @param priceDto 消息对象
     */
    private void handleDealQuote(String tradePairCode, CurrentPriceRespDTO priceDto, Boolean isMarketClose) {
        log.debug("更新tradePair:[{}], price:[{}]", tradePairCode, priceDto.getCurrentPrice());
        priceRedisDAO.set(tradePairCode, priceDto);

        // 如果是休市时则不去更新这个价格因为这个价格是系统人工生成的假数据
        if (!isMarketClose) {
            priceRedisDAO.setCloseBackupPrice(tradePairCode, priceDto);
            // 更新休市价格生成器中的基准价格
            marketCloseDataGenerator.updateCloseBasePrice(tradePairCode, priceDto.getCurrentPrice());
        }

        long now = DateUtil.currentSeconds();

        // 先获取tradePairCode的所有复制币
        final List<TradePairRespDTO> copyTrades = tradePairApi.getCopyTradeOfReference(tradePairCode);
        final List<ControlPlanDTO> runningPlanList = controlPlanApi.getRunningPlanList();
        final Map<String, ControlPlanDTO> runningPlanMap = runningPlanList.stream().collect(Collectors.toMap(ControlPlanDTO::getTradePairCode, controlPlanDTO -> controlPlanDTO));

        for (TradePairRespDTO copyTrade : copyTrades) {
            String copyCode = copyTrade.getCode();
            // 判断复制币此时此刻是否有在控盘
            final ControlPlanDTO runningPlan = runningPlanMap.get(copyCode);
            boolean isRunning = runningPlan.getStartTime() <= now && runningPlan.getEndTime() > now;
            if (isRunning) {
                final ControlPricePointDTO pricePoint = controlPlanApi.getPricePoint(runningPlan.getId(), now);
                if (pricePoint != null) {
                    // 当前价格和引用价格的差值
                    final BigDecimal diffPrice = runningPlan.getExecPrice().subtract(pricePoint.getReferPrice());
                    BigDecimal calculatePrice = pricePoint.getReferPrice().add(diffPrice).add(pricePoint.getPriceDiff());

                    CurrentPriceRespDTO priceCopied = new CurrentPriceRespDTO();
                    priceCopied.setCurrentPrice(calculatePrice);
                    priceCopied.setVolume(pricePoint.getVolume());
                    priceCopied.setTimestamp(pricePoint.getTimestamp());
                    priceCopied.setTurnover(BigDecimal.ZERO);

                    // 这里需要使用引用交易对的代码，因为这里需要获取今日开盘价格
                    final BigDecimal percentage = calculateChangePercentage(tradePairCode, calculatePrice);
                    priceCopied.setPercentage(percentage);

                    priceRedisDAO.set(copyTrade.getCode(), priceCopied);
                }
            } else {
                // 如果没有运行中的控盘计划则直接将引用交易对的价格作为复制币的价格
                priceRedisDAO.set(copyTrade.getCode(), priceDto);
            }
        }
    }

    /**
     * 根据实时价格计算交易对的涨跌幅并更新redis中的涨跌幅
     *
     * @param tradePairCode 交易对代码
     * @param currentPrice  实时价格
     */
    private BigDecimal calculateChangePercentage(String tradePairCode, BigDecimal currentPrice) {
        final TodayKlinePriceDTO todayKlinePriceDTO = TodayKlineHolder.get(tradePairCode);
        if (currentPrice == null || todayKlinePriceDTO == null || todayKlinePriceDTO.getOpenPrice() == null) {
            log.error("涨跌幅计算失败 返回0.00 close:{} current:{}", todayKlinePriceDTO, currentPrice);
            return BigDecimal.valueOf(0, 2);
        }
        try {
            final BigDecimal openPrice = new BigDecimal(todayKlinePriceDTO.getOpenPrice());
            final BigDecimal change = currentPrice.subtract(openPrice);
            // final MathContext mc = new MathContext(3, RoundingMode.HALF_UP);
            // return change.divide(closePrice, 8, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100), mc);
            // return change.divide(openPrice, 8, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
            return change.divide(openPrice, 4, RoundingMode.HALF_UP);
        } catch (NumberFormatException | ArithmeticException ignored) {
        }
        log.error("涨跌幅计算异常 返回0.00");
        return BigDecimal.valueOf(0, 2);
    }

    /**
     * 处理盘口报价消息对象
     *
     * @param candleOrderBookListDTO 消息对象
     */
    private void handlePKQuote(String tradePairCode, CandleOrderBookListDTO candleOrderBookListDTO,
                               Boolean isMarketClose) {
        // 更新redis中的对应交易对的订单薄信息
        candleOrderBookListDTO.setTradePairCode(tradePairCode);
        orderBookRedisDAO.set(tradePairCode, candleOrderBookListDTO);

        // 设置复制币
        List<TradePairRespDTO> customTradePair = tradePairApi.getCustomTradePair();
        customTradePair = customTradePair.stream()
                .filter(c -> c.getIsCopy() && c.getReferenceCode().equalsIgnoreCase(tradePairCode)).toList();
        for (TradePairRespDTO item : customTradePair) {
            candleOrderBookListDTO.setTradePairCode(item.getCode());
            orderBookRedisDAO.set(item.getCode(), candleOrderBookListDTO);
        }
    }

    /**
     * 获取交易对的最后更新时间
     *
     * @return 更新信息的map
     */
    public static Map<String, UpdateDto> getCodeLastTickTimeMap() {
        return CODE_LAST_TICKTIME_MAP;
    }

    /**
     * 根据当前价格生成委托单数据
     *
     * @param tradePairCode 交易对代码
     * @param currentPrice  当前价格
     * @return 订单薄
     */
    private CandleOrderBookListDTO generateCandleOrderBookListDTO(String tradePairCode, BigDecimal currentPrice,
                                                                  BigDecimal currentVolume) {
        CandleOrderBookListDTO dto = new CandleOrderBookListDTO();
        dto.setTradePairCode(tradePairCode);
        List<CandleOrderBookDTO> bids = Lists.newArrayList();
        List<CandleOrderBookDTO> asks = Lists.newArrayList();
        // 生成
        for (int i = 1; i <= 6; i++) {
            Random random = new Random();
            BigDecimal sellPrice = currentPrice.subtract(
                    (BigDecimal.valueOf(i).divide(BigDecimal.valueOf(random.nextInt(1, 200)), 2, RoundingMode.HALF_UP)));
            BigDecimal buyPrice = currentPrice.add(
                    (BigDecimal.valueOf(i).divide(BigDecimal.valueOf(random.nextInt(1, 200)), 2, RoundingMode.HALF_UP)));
            BigDecimal sellVolume = BigDecimal.ONE;
            BigDecimal buyVolume = BigDecimal.ONE;

            if (currentPrice.compareTo(BigDecimal.ONE) < 0) {
                sellVolume = BigDecimal.valueOf(random.nextInt(1000, 100000));
                buyVolume = BigDecimal.valueOf(random.nextInt(1000, 100000));
            } else if (currentPrice.compareTo(BigDecimal.valueOf(100)) < 0) {
                sellVolume = BigDecimal.valueOf(random.nextInt(100, 10000));
                buyVolume = BigDecimal.valueOf(random.nextInt(100, 10000));
            } else if (currentPrice.compareTo(BigDecimal.valueOf(1000)) < 0) {
                sellVolume = BigDecimal.valueOf(random.nextDouble(1, 1000));
                buyVolume = BigDecimal.valueOf(random.nextDouble(1, 1000));
            } else if (currentPrice.compareTo(BigDecimal.valueOf(2000)) < 0) {
                sellVolume = BigDecimal.valueOf(random.nextDouble(0.01, 100));
                buyVolume = BigDecimal.valueOf(random.nextDouble(0.01, 100));
            } else if (currentPrice.compareTo(BigDecimal.valueOf(5000)) < 0) {
                sellVolume = BigDecimal.valueOf(random.nextDouble(0.01, 50));
                buyVolume = BigDecimal.valueOf(random.nextDouble(0.01, 50));
            } else {
                sellVolume = BigDecimal.valueOf(random.nextDouble(0.01, 10.99));
                buyVolume = BigDecimal.valueOf(random.nextDouble(0.01, 10.99));
            }

            if (sellVolume.compareTo(currentVolume) > 0) {
                sellVolume = sellVolume.divide(BigDecimal.TEN).setScale(2, RoundingMode.HALF_UP);
            }
            if (buyVolume.compareTo(currentVolume) > 0) {
                buyVolume = buyVolume.divide(BigDecimal.TEN).setScale(2, RoundingMode.HALF_UP);
            }
            // sellVolume = BigDecimal.valueOf(random.nextDouble(0.01, currentVolume.doubleValue()));
            // buyVolume = BigDecimal.valueOf(random.nextDouble(0.01, currentVolume.doubleValue()));

            sellVolume = sellVolume.setScale(2, RoundingMode.HALF_UP);
            buyVolume = buyVolume.setScale(2, RoundingMode.HALF_UP);
            CandleOrderBookDTO sellOrderBookDTO = new CandleOrderBookDTO();
            CandleOrderBookDTO buyOrderBookDTO = new CandleOrderBookDTO();
            sellOrderBookDTO.setPrice(String.valueOf(sellPrice));
            sellOrderBookDTO.setVolume(String.valueOf(sellVolume));
            buyOrderBookDTO.setPrice(String.valueOf(buyPrice));
            buyOrderBookDTO.setVolume(String.valueOf(buyVolume));
            bids.add(buyOrderBookDTO);
            asks.add(sellOrderBookDTO);
        }

        dto.setBids(bids);
        dto.setAsks(asks);
        return dto;
    }

    private CurrentPriceRespDTO covertToPriceResp(String tradePairCode, AllTickDealQuoteDTO quoteDTO,
                                                  boolean isMarketClose) {
        long tickTime = Long.parseLong(quoteDTO.getTickTime());
        BigDecimal currentPrice = new BigDecimal(quoteDTO.getPrice());
        // 更新redis中的对应交易对的价格信息
        CurrentPriceRespDTO priceDto = new CurrentPriceRespDTO();
        priceDto.setIsMarketClose(isMarketClose);
        priceDto.setTradePairCode(tradePairCode);
        priceDto.setCurrentPrice(currentPrice);
        priceDto.setVolume(new BigDecimal(quoteDTO.getVolume()));
        priceDto.setTickTime(tickTime);
        priceDto.setTimestamp(System.currentTimeMillis());
        // 计算涨跌幅
        final BigDecimal changePercentage = calculateChangePercentage(tradePairCode, currentPrice);
        priceDto.setPercentage(changePercentage);
        return priceDto;
    }

    private CurrentPriceRespDTO covertToPriceResp(String tradePairCode, JiangShanPriceDTO jiangShanPriceDTO,
                                                  boolean isMarketClose) {
        long tickTime = Long.parseLong(jiangShanPriceDTO.getTimestamp());
        CurrentPriceRespDTO priceDto = new CurrentPriceRespDTO();
        priceDto.setTradePairCode(tradePairCode);
        priceDto.setCurrentPrice(new BigDecimal(jiangShanPriceDTO.getBid()));
        priceDto.setVolume(new BigDecimal(jiangShanPriceDTO.getVolume()));
        priceDto.setTickTime(tickTime);
        priceDto.setTimestamp(System.currentTimeMillis());
        priceDto.setPercentage(
                new BigDecimal(jiangShanPriceDTO.getPercent()).divide(new BigDecimal(100), 4, RoundingMode.HALF_UP));
        priceDto.setIsMarketClose(isMarketClose);
        return priceDto;
    }

    private CurrentPriceRespDTO convertToPriceResp(String tradePairCode, JSONObject jsonObj, boolean isMarketClose) {
        CurrentPriceRespDTO priceDto = new CurrentPriceRespDTO();
        final String evVal = jsonObj.getStr(PolygonConstants.RESP_KEY_EV);
        String priceKey = "b";
        String volumeKey = "s";
        if (RESP_VAL_EV_XT.equals(evVal)) {
            priceKey = "p";
            volumeKey = "s";
        }
        final long ts = jsonObj.getLong("t");
        priceDto.setTradePairCode(tradePairCode);
        priceDto.setCurrentPrice(jsonObj.getBigDecimal(priceKey));
        priceDto.setVolume(Objects.requireNonNullElse(jsonObj.getBigDecimal(volumeKey), BigDecimal.ZERO));
        priceDto.setTurnover(BigDecimal.ZERO);
        priceDto.setTimestamp(ts);
        priceDto.setTickTime(ts);
        priceDto.setIsMarketClose(isMarketClose);
        BigDecimal changePercentage = calculateChangePercentage(tradePairCode, priceDto.getCurrentPrice());
        priceDto.setPercentage(changePercentage);
        log.debug("Polygon 价格信息:[{}]", priceDto);
        return priceDto;
    }
}