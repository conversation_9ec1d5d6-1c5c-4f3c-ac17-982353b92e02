package com.rf.exchange.server.data.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.github.rholder.retry.*;
import com.rf.exchange.module.exc.api.tradepair.TradePairApi;
import com.rf.exchange.module.exc.api.tradepair.dto.TradePairRespDTO;
import com.rf.exchange.server.data.core.websocket.container.CandleWebSocketManagerContainer;
import com.rf.exchange.server.data.dal.redis.CandleDataSourceSubscribeRedisDAO;
import com.rf.exchange.server.data.datasource.alltick.util.AllTickUtil;
import com.rf.exchange.server.data.datasource.alltick.websocket.*;
import com.rf.exchange.server.data.enums.CandleDataSource;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.client.WebSocketConnectionManager;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2024-06-27
 */
@Slf4j
@Service
public class CandleDataWebSocketServiceImpl implements CandleDataWebSocketService {

    @Resource
    private CandleWebSocketManagerContainer managerContainer;
    @Resource
    private CandleDataSourceSubscribeRedisDAO sourceSubscribeRedisDAO;
    @Resource
    private AllTickProductSubscriber allTickProductSubscriber;

    @Value("${exchange.candle-datasource.alltick.deep:5}")
    private Integer allTickDeep;

    @Resource
    @Lazy
    private TradePairApi tradePairApi;

    @Override
    public void connect(CandleDataSource dataSource) {
        if (!managerContainer.isManagerConnected(dataSource)) {
            retryConnect(dataSource);
        }
    }

    @Override
    public void disconnect(CandleDataSource dataSource) {
        WebSocketConnectionManager manager = managerContainer.getManager(dataSource);
        if (manager != null) {
            manager.stop();
        }
    }

    @Override
    public void sendUpdateSubscribeMessage(CandleDataSource dataSource, Set<String> codeList) {
        if (CollectionUtil.isEmpty(codeList)) {
            return;
        }
        // 如果数据源对应的websocket没有连接则连接
        if (!managerContainer.isManagerConnected(dataSource)) {
            connect(dataSource);
        }
        //
        AllTickWSMessageBuilder messageBuilder = new AllTickWSMessageBuilder();
        // 先发送AllTick的成交报价订阅消息
        AllTickCandleIdMessage message = messageBuilder.buildSubscribeMessage(codeList, AllTickCommand.DEAL_PRICE.getCmdCode(), getSubscribeExtraInfo(dataSource));
        managerContainer.sendMessage(dataSource, message);
        log.info("订阅 {} 的产品列表-更新: {}", dataSource.getName(), codeList);
    }

    @Override
    public void sendUnsubscribeMessage(CandleDataSource dataSource, Set<String> codeList) {
        if (CollUtil.isEmpty(codeList)) {
            return;
        }
        if (!managerContainer.isManagerConnected(dataSource)) {
            connect(dataSource);
        }
        AllTickWSMessageBuilder messageBuilder = new AllTickWSMessageBuilder();
        AllTickCandleIdMessage message = messageBuilder.buildUnsubscribeMessage(codeList, AllTickCommand.CANCEL_SUB.getCmdCode());
        managerContainer.sendMessage(dataSource, message);
    }

    @Override
    public void sendSubscribeMessage(CandleDataSource dataSource) {
        // 获取需要订阅的交易对
        List<TradePairRespDTO> listCached = tradePairApi.getTradePairEnableListCached();
        if (CollectionUtil.isEmpty(listCached)) {
            return;
        }

        // 只订阅AllTick支持的产品code
        Set<String> willSubscribeCodeSet = new HashSet<>(listCached.size());
        for (TradePairRespDTO tradePair : listCached) {
            if (tradePair.getSource().equals(dataSource.getValue())) {
                if (AllTickUtil.isProductContain(tradePair.getCode())) {
                    willSubscribeCodeSet.add(tradePair.getCode());
                } else {
                    log.error("无法正确获取需要订阅的AllTick产品code 系统code:{}", tradePair.getCode());
                }
            }
        }

        if (CollectionUtil.isNotEmpty(willSubscribeCodeSet)) {
            AllTickWSMessageBuilder builder = new AllTickWSMessageBuilder();
            AllTickCandleIdMessage message = builder.buildSubscribeMessage(willSubscribeCodeSet, AllTickCommand.DEAL_PRICE.getCmdCode(), null);
            managerContainer.sendMessage(dataSource, message);
            log.info("订阅 {} 的产品列表: {}", dataSource.getName(), willSubscribeCodeSet);
        } else {
            // 如果没有任何需要订阅的产品，则清除redis中的订阅关系
            allTickProductSubscriber.cleanAllSubscribedCodes();
        }
    }

    /**
     * 获取订阅消息的附加信息
     *
     * @param dataSource 数据源
     * @return 附加信息
     */
    private Map<String, Object> getSubscribeExtraInfo(CandleDataSource dataSource) {
        if (!CandleDataSource.ALLTICK.equals(dataSource)) {
            return Collections.emptyMap();
        }
        return Map.of(AllTickKeyConstants.KEY_DEPTH, allTickDeep);
    }

    @Override
    public void sendHeartbeatMessage(CandleDataSource dataSource) {
        AllTickWSMessageBuilder builder = new AllTickWSMessageBuilder();
        AllTickCandleIdMessage message = builder.buildHeartbeatMessage();
        managerContainer.sendMessage(dataSource, message);
    }

    private Callable<Boolean> retryConnectCallable(CandleDataSource dataSource) {
        return () -> {
            WebSocketConnectionManager manager = managerContainer.getManager(dataSource);
            if (manager != null) {
                if (manager.isConnected()) {
                    return true;
                }
                manager.start();
            }
            return true;
        };
    }

    private Boolean isConnected(CandleDataSource dataSource) {
        boolean isConnect = managerContainer.isManagerConnected(dataSource);
        log.info("数据源:[{}] isConnect:[{}]", dataSource.getName(), isConnect);
        return isConnect;
    }

    private void retryConnect(CandleDataSource dataSource) {
        Retryer<Boolean> retryer = RetryerBuilder.<Boolean>newBuilder()
                //无论出现什么异常，都进行重试
                .retryIfExceptionOfType(IOException.class)
                // 如果返回true，则重试
                .retryIfResult(input -> {
                    // 如果manager为空则不重试
                    if (managerContainer.getManager(dataSource) == null) {
                        log.error("没有数据源:[{}]的ws manager", dataSource.getName());
                        return false;
                    }
                    // 没有连接成功则重试
                    return !isConnected(dataSource);
                })
                //重试等待策略：等待 3s 后再进行重试
                .withWaitStrategy(WaitStrategies.fixedWait(3, TimeUnit.SECONDS))
                //重试停止策略：重试达到 3 次
                .withStopStrategy(StopStrategies.stopAfterAttempt(3))
                .withRetryListener(new RetryListener() {
                    @Override
                    public <V> void onRetry(Attempt<V> attempt) {
                        log.info("RetryListener: 第{}次调用 source:{}", attempt.getAttemptNumber(), dataSource.getName());
                    }
                })
                .build();
        try {
            retryer.call(retryConnectCallable(dataSource));
            retryConnectCallable(dataSource).call();
        } catch (Exception e) {
            log.error("数据源:{} 多次重新连接失败: {}", dataSource.getName(), e.getMessage());
        }
    }

}
