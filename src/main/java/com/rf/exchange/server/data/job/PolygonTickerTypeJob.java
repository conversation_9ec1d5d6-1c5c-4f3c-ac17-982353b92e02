package com.rf.exchange.server.data.job;

import com.rf.exchange.framework.idempotent.core.annotation.Idempotent;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * Polygon API 信息查询定时任务
 *
 * <AUTHOR>
 * @since 2024-06-27
 */
@Component
public class PolygonTickerTypeJob {

    //@Idempotent
    //@Scheduled(cron = "0/10 * * * * ?")
    //public void requestTickerType() {
    //
    //}
}
