package com.rf.exchange.server.data.core.mq;

import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2024-07-01
 */
@Slf4j
@Service
public class RocketCandleProducer {

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    @Value("${exchange.candle-rocketmq.topic}")
    private String topic;

    @Value("${exchange.candle-rocketmq.topic-tag-price}")
    private String tagPrice;

    @Value("${exchange.candle-rocketmq.topic-tag-orderbook}")
    private String tagOrderBook;

    public void sendPriceMessage(Integer dataSource, String message) {
        internalSendMessage(topic + ":" + tagPrice, dataSource, message);
    }

    public void sendOrderBookMessage(Integer dataSource, String message) {
        internalSendMessage(topic + ":" + tagOrderBook, dataSource, message);
    }

    private void internalSendMessage(String dest, Integer dataSource, String message) {
        RocketCandleMessage mqMsg = new RocketCandleMessage();
        mqMsg.setDataSource(dataSource);
        mqMsg.setPayload(message);
        SendResult sendResult = rocketMQTemplate.syncSend(dest, mqMsg);
        if (sendResult.getSendStatus() != SendStatus.SEND_OK) {
            log.error("RocketMQ发送Candle消息失败 {}", sendResult.getSendStatus());
        }
    }
}
