package com.rf.exchange.server.data.datasource.polygon.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Polygon的k线
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@Data
public class PolygonKLine {
    /**
     * 系统交易对代码
     */
    private String code;
    /**
     * Polygon的交易对代码
     */
    private String ticker;
    /**
     * 交易时间戳
     */
    @JsonProperty("t")
    private Long timestamp;
    /**
     * 开盘价
     */
    @JsonProperty("o")
    private BigDecimal openPrice;
    /**
     * 收盘价
     */
    @JsonProperty("c")
    private BigDecimal closePrice;
    /**
     * 最高价
     */
    @JsonProperty("h")
    private BigDecimal highPrice;
    /**
     * 最低价
     */
    @JsonProperty("l")
    private BigDecimal lowPrice;
    /**
     * 成交量
     */
    @JsonProperty("v")
    private BigDecimal volume;
    /**
     * 加权的成交量
     */
    @JsonProperty("vw")
    private BigDecimal volumeWeighted;

    @JsonProperty("n")
    private Integer transactionNum;
}
