package com.rf.exchange.server.data.job;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.date.Week;
import cn.hutool.core.util.NumberUtil;
import com.rf.exchange.framework.common.util.json.JsonUtils;
import com.rf.exchange.module.exc.api.tradepair.TradePairApi;
import com.rf.exchange.module.exc.api.tradepair.dto.TradePairRespDTO;
import com.rf.exchange.module.exc.api.tradetransactionhour.TradeTransactionTimeApi;
import com.rf.exchange.module.exc.api.tradetransactionhour.dto.TradeTransactionTimeDTO;
import com.rf.exchange.server.data.core.marketclose.CandleMarketCloseDataGenerator;
import com.rf.exchange.server.data.core.mq.RocketCandleConsumer;
import com.rf.exchange.server.data.core.mq.RocketCandleProducer;
import com.rf.exchange.server.data.core.mq.dto.UpdateDto;
import com.rf.exchange.server.data.dal.redis.CandleDataSourceMonitorRedisDAO;
import com.rf.exchange.server.data.datasource.alltick.util.AllTickUtil;
import com.rf.exchange.server.data.datasource.alltick.websocket.AllTickCommand;
import com.rf.exchange.server.data.datasource.alltick.websocket.AllTickKeyConstants;
import com.rf.exchange.server.data.enums.CandleDataSource;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.support.PeriodicTrigger;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 休市交易对价格的
 *
 * <AUTHOR>
 * @since 2024-10-02
 */
@Slf4j
@Component
public class CandleMarketCloseGeneratorJob {

    @Resource
    private RocketCandleProducer mqProducer;
    @Resource
    private TradePairApi tradePairApi;
    @Resource
    private TradeTransactionTimeApi transactionHourApi;
    @Resource
    private CandleMarketCloseDataGenerator candleGenerator;
    @Resource
    private CandleDataSourceMonitorRedisDAO sourceMonitorRedisDAO;

    @Resource(name = "marketCloseGeneratorTaskScheduler")
    private ThreadPoolTaskScheduler genTaskScheduler;

    // 服务器默认时区id
    private final ZoneId serverTimeZoneId = TimeZone.getDefault().toZoneId();

    // 三方数据源的价格最后的更新时间
    private final ConcurrentHashMap<String, Long> CACHED_LAST_TICK_TIME = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        genTaskScheduler.schedule(this::generateMarkCloseCandle, new PeriodicTrigger(Duration.ofMillis(1000)));
        candleGenerator.initGenerator();

        // 交易对的最后更新数据的时间
        final Map<String, String> allTradePairLastUpdateTimeMap = sourceMonitorRedisDAO.getAll();
        for (Map.Entry<String, String> entry : allTradePairLastUpdateTimeMap.entrySet()) {
            final String value = entry.getValue();
            final String[] parts = value.split(":");
            if (parts.length >= 2) {
                String ts = parts[1];
                CACHED_LAST_TICK_TIME.put(entry.getKey(), Long.valueOf(ts));
            }
        }
    }

    /**
     * 生成休市k线数据
     */
    public void generateMarkCloseCandle() {
        // 判断显示是夏令时还是冬令时
        boolean isDST = isDSTTimeNow();
        // 市场时间
        ZonedDateTime serverTime = ZonedDateTime.now(serverTimeZoneId);
        // 获取非24小时交易的所有交易对
        final List<TradePairRespDTO> not24HourTradePairs = tradePairApi.getNot24HourTradePairs();

        for (TradePairRespDTO tradePairDTO : not24HourTradePairs) {
            long hourId;
            if (isDST) {
                hourId = NumberUtil.nullToZero(tradePairDTO.getTransactionTimeDstId());
            } else {
                hourId = NumberUtil.nullToZero(tradePairDTO.getTransactionTimeWinterId());
            }

            if (hourId == 0) {
                continue;
            }

            final TradeTransactionTimeDTO configuredTransactionTime = transactionHourApi.getTradeTransactionTimeById(hourId);
            if (configuredTransactionTime == null || configuredTransactionTime.getTimezone() == null) {
                continue;
            }

            // 将当前服务器时间转成交易对的交易市场时间
            final ZoneId transactionZoneId = ZoneId.of(configuredTransactionTime.getTimezone());
            ZonedDateTime marketDateTime = serverTime.withZoneSameInstant(transactionZoneId);

            // 先判断星期是否在开市的工作日内
            final boolean isDuringDayOfWeek = isDuringDayOfWeek(configuredTransactionTime, marketDateTime);

            // 是否需要生成随机k线数据
            boolean isNeedGenerateRandomCandle;
            // 再判断时间是否在交易时间内
            final boolean isDuringMarketOpenTime = isDuringMarketOpenTime(configuredTransactionTime, marketDateTime);
            if (isDuringDayOfWeek && isDuringMarketOpenTime) {
                // 是否在交易日内的休息时间
                isNeedGenerateRandomCandle = configuredTransactionTime.getHasDailyBreak() && isDuringDailyBreak(configuredTransactionTime, marketDateTime);
            } else {
                isNeedGenerateRandomCandle = true;
            }

            // 生成休市随机数据
            if (isNeedGenerateRandomCandle) {
                long tsMills = System.currentTimeMillis();
                // 生成休市数据
                final Map<String, Object> resultMap = candleGenerator.randomGenerateCandlePriceData(tradePairDTO);
                final BigDecimal realTimePrice = (BigDecimal) resultMap.get("price");
                if (realTimePrice == null || BigDecimal.ZERO.compareTo(realTimePrice) == 0) {
                    continue;
                }
                final TreeMap<BigDecimal, BigDecimal> bids = (TreeMap<BigDecimal, BigDecimal>) resultMap.get("bids");
                final TreeMap<BigDecimal, BigDecimal> asks = (TreeMap<BigDecimal, BigDecimal>) resultMap.get("asks");

                if (CandleDataSource.ALLTICK.getValue() == tradePairDTO.getSource()) {
                    // 获取数据源交易对的最后更新的时间戳
                    final Map<String, UpdateDto> lastTickTimeMap = RocketCandleConsumer.getCodeLastTickTimeMap();
                    final UpdateDto updateDto = lastTickTimeMap.get(tradePairDTO.getCode());
                    // 如果没有数据源的最后更新时间则使用当前时间
                    long lastTickTime = tsMills;
                    if (updateDto != null) {
                        lastTickTime = updateDto.getTickTime();
                    } else if (CACHED_LAST_TICK_TIME.containsKey(tradePairDTO.getCode())) {
                        lastTickTime = CACHED_LAST_TICK_TIME.get(tradePairDTO.getCode());
                    }
                    // 构造假数据源的ws消息
                    final String priceMessage = constructAllTickPriceMessage(tradePairDTO.getCode(), realTimePrice, lastTickTime);
                    final String orderBookMessage = constructAllTickOrderBookMessage(tradePairDTO.getCode(), lastTickTime, bids, asks);
                    // 发送假的数据源ws消息
                    mqProducer.sendPriceMessage(tradePairDTO.getSource(), priceMessage);
                    mqProducer.sendOrderBookMessage(tradePairDTO.getSource(), orderBookMessage);
                } else if (CandleDataSource.POLYGON.getValue() == tradePairDTO.getSource()) {
                    // TODO: 生成Polygon数据源的休市数据
                }
            }
        }
    }

    private String constructAllTickPriceMessage(String code, BigDecimal price, long timestamp) {
        // AllTick的产品列表
        String productCode = AllTickUtil.getProductCode(code);

        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put(AllTickKeyConstants.KEY_CODE, productCode);
        dataMap.put(AllTickKeyConstants.KEY_PRICE, price);
        dataMap.put(AllTickKeyConstants.KEY_TICK_TIMe, timestamp);
        dataMap.put(AllTickKeyConstants.KEY_SEQ_ID, "0");
        dataMap.put(AllTickKeyConstants.KEY_VOLUME, "0");
        dataMap.put(AllTickKeyConstants.KEY_TURNOVER, "0");
        dataMap.put(AllTickKeyConstants.KEY_TRADE_DIRECTION, 1);

        Map<String, Object> messageMap = new HashMap<>();
        messageMap.put(AllTickKeyConstants.KEY_CMD_ID, AllTickCommand.DEAL_PRICE_MSG.getCmdCode());
        messageMap.put(AllTickKeyConstants.KEY_DATA, dataMap);

        return JsonUtils.toJsonString(messageMap);
    }

    private String constructAllTickOrderBookMessage(String code, long timestamp, TreeMap<BigDecimal, BigDecimal> bids, TreeMap<BigDecimal, BigDecimal> asks) {
        final String productCode = AllTickUtil.getProductCode(code);

        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put(AllTickKeyConstants.KEY_CODE, productCode);
        dataMap.put(AllTickKeyConstants.KEY_TICK_TIMe, timestamp);
        dataMap.put(AllTickKeyConstants.KEY_SEQ_ID, "0");

        dataMap.put(AllTickKeyConstants.KEY_BIDS, createOrderListWithMap(bids));
        dataMap.put(AllTickKeyConstants.KEY_ASKS, createOrderListWithMap(asks));

        Map<String, Object> messageMap = new HashMap<>();
        messageMap.put(AllTickKeyConstants.KEY_CMD_ID, AllTickCommand.PK_PRICE_MSG.getCmdCode());
        messageMap.put(AllTickKeyConstants.KEY_DATA, dataMap);

        return JsonUtils.toJsonString(messageMap);
    }

    private List<Map<String, String >> createOrderListWithMap(TreeMap<BigDecimal, BigDecimal> bids) {
        List<Map<String, String>> list = new ArrayList<>();
        for (Map.Entry<BigDecimal, BigDecimal> entry : bids.entrySet()) {
            Map<String, String> order = new HashMap<>();
            order.put(AllTickKeyConstants.KEY_PRICE, entry.getKey().toString());
            order.put(AllTickKeyConstants.KEY_VOLUME, entry.getValue().toString());
            list.add(order);
        }
        return list;
    }

    /**
     * 判断当前时间是否是每 N 分钟的最后一秒钟
     *
     * @param second 秒钟数
     * @param minute 分钟数
     * @return true:是最后一秒钟
     */
    private static boolean isLastSecondOfEveryNMinutes(int second, int minute, int n) {
        return second == 59 && minute % n == 0;
    }

    /**
     * 判断当前时间是否是每 N 小时的最后一秒钟
     *
     * @param second 秒钟数
     * @param hour   小时数
     * @return true:是最后一秒钟
     */
    private static boolean isLastSecondOfEveryNHours(int second, int hour, int n) {
        return second == 59 && hour % n == 0;
    }

    /**
     * 当前市场时间是否在交易工作日内
     *
     * @param transactionTimeDTO 交易时间信息
     * @param marketDateTime     当前市场时间
     * @return true:在交易工作日
     */
    private boolean isDuringDayOfWeek(TradeTransactionTimeDTO transactionTimeDTO, ZonedDateTime marketDateTime) {
        // 当前交易对的交易市场时间是星期几
        final LocalDate marketLocalDate = marketDateTime.toLocalDate();
        final Week marketWeek = LocalDateTimeUtil.dayOfWeek(marketLocalDate);

        final Week dayOfWeekOpen = Week.of(transactionTimeDTO.getDayOfWeekOpen());
        final Week dayOfWeekClose = Week.of(transactionTimeDTO.getDayOfWeekClose());

        return marketWeek.getValue() >= dayOfWeekOpen.getValue() && marketWeek.getValue() <= dayOfWeekClose.getValue();
    }

    /**
     * 当前市场时间是否在开市时间范围内
     *
     * @param transactionTimeDTO 交易时间信息
     * @param marketDateTime     当前市场时间
     * @return true:在开市时间内
     */
    private boolean isDuringMarketOpenTime(TradeTransactionTimeDTO transactionTimeDTO, ZonedDateTime marketDateTime) {
        final LocalTime marketLocalTime = marketDateTime.toLocalTime();

        final LocalTime configOpenTime = transactionTimeDTO.getMarketOpen();
        final LocalTime configCloseTime = transactionTimeDTO.getMarketClose();

        return configOpenTime.isBefore(marketLocalTime) && configCloseTime.isAfter(marketLocalTime);
    }

    /**
     * 是否是交易日的日内休息时间
     *
     * @return true:此刻是处于日内休息时间
     */
    private boolean isDuringDailyBreak(TradeTransactionTimeDTO transactionTimeDTO, ZonedDateTime marketDateTime) {
        final LocalTime marketLocalTime = marketDateTime.toLocalTime();
        final LocalTime dailyBreakStart = transactionTimeDTO.getDailyBreakStart();
        final LocalTime dailyBreakEnd = transactionTimeDTO.getDailyBreakEnd();
        return dailyBreakStart.isBefore(marketLocalTime) && dailyBreakEnd.isAfter(marketLocalTime);
    }

    /**
     * 是否是dst(夏令时)时间
     *
     * @return true:是dst时间 false:非
     */
    private boolean isDSTTimeNow() {
        final ZoneId gmtZone = ZoneId.of("Europe/London");
        ZonedDateTime gmtTime = ZonedDateTime.now(gmtZone);
        return gmtTime.getZone().getRules().isDaylightSavings(gmtTime.toInstant());
    }
}
