package com.rf.exchange.server.data.service;

import com.rf.exchange.module.candle.api.CandleDataApi;
import com.rf.exchange.module.candle.enums.CandleTimeRangeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024-07-05
 */
@Slf4j
@Service
public class CandleDataHttpServiceImpl implements CandleDataHttpService {


    @Resource
    private CandleDataApi candleDataApi;


}
