package com.rf.exchange.server.data.datasource.jiangshan.dto;

import lombok.Data;

/**
 * @description: 匠山ws返回的数据格式
 * @author: Petter
 * @create: 2024-10-02
 **/
@Data
public class JiangShanPriceDTO {

    private String symbol;
    private String timestamp;
    private String open;
    private String close;
    private String bid;
    private String low;
    private String high;
    private String percent;
    private String change;
    private String volume;

    // formatter:off
    // {
    // "symbol": "XAUUSD", // 股票代码
    // "timestamp": 1713609687,
    // "open": 2378.38, // 今开
    // "close": 2378.88, // 昨收
    // "bid": 2387.51, // 最新价格
    // "low": 2372.76, // 最低
    // "high": 2417.73, // 最高
    // "percent": 0.36, // 涨幅率
    // "change": 8.63, // 涨幅
    // "volume": 162366 // 交易量
    // }
}
