package com.rf.exchange.server.data.datasource.jiangshan.dto;

import java.util.List;

import org.dromara.hutool.core.net.url.UrlEncoder;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import lombok.Data;

/**
 * @description:
 * @author: Petter
 * @create: 2024-10-09
 **/
@Data
public class KineHistoryDTO {
    private Integer code;
    private CurbarDTO curbar;
    private List<KineItemDTO> list;

    public static void main(String[] args) {
        // #C-RICE
        String url = UrlEncoder.encodeQuery(
            "http://f-test.js-stock.top/getkine?symbol=#C-ROBUSTA&cmd=history&period=1&key=1KcHQ8nPaicZeqIsnlOE");
        String resp = HttpUtil.get(url);

        KineHistoryDTO kineHistoryDTO = JSONUtil.toBean(resp, KineHistoryDTO.class);
        System.out.println(JSONUtil.toJsonStr(kineHistoryDTO));
    }
}
