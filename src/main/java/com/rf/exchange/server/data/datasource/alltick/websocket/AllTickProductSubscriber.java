package com.rf.exchange.server.data.datasource.alltick.websocket;

import cn.hutool.core.collection.CollectionUtil;
import com.rf.exchange.server.data.dal.redis.CandleDataSourceSubscribeRedisDAO;
import com.rf.exchange.server.data.enums.CandleDataSource;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024-10-01
 */
@Component
public class AllTickProductSubscriber {

    @Resource
    private CandleDataSourceSubscribeRedisDAO subscribeRedisDAO;

    /**
     * 更新订阅的交易对
     *
     * @param subscribedCodes 已经订阅的交易对
     */
    public void updateSubscribeCods(Set<String> subscribedCodes) {
        final ArrayList<String> codes = new ArrayList<>(subscribedCodes);
        subscribeRedisDAO.set(CandleDataSource.ALLTICK.getName(), codes);
    }

    public List<String> getSubscribedCodes() {
        return subscribeRedisDAO.get(CandleDataSource.ALLTICK.getName());
    }

    public void cleanAllSubscribedCodes() {
        subscribeRedisDAO.delete(CandleDataSource.ALLTICK.getName());
    }

    /**
     * 是否需要更新AllTick的订阅的产品列表
     *
     * @param newCodes 重新读取的交易对代码集合
     * @return 大于0更新订阅 等于0不需要订阅或者取消订阅 -1需要取消订阅
     */
    public int getSubscribeBehavior(Set<String> newCodes) {
        if (CollectionUtil.isEmpty(newCodes)) {
            return -1;
        }
        final List<String> subscribedCodes = subscribeRedisDAO.get(CandleDataSource.ALLTICK.getName());
        // 没有订阅任何的产品则需要订阅
        if (CollectionUtil.isEmpty(subscribedCodes)) {
            return 1;
        }
        // 如果已经订阅的产品数量和新的交易对数量不一致也需要重新订阅交易对
        if (newCodes.size() != subscribedCodes.size()) {
            return 1;
        }
        // 如果已经订阅的交易对和新的交易对内容不一致则需要重新订阅
        for (String code: subscribedCodes) {
            if (!newCodes.contains(code)) {
                return 1;
            }
        }
        // 否则不重新订阅
        return 0;
    }
}
