package com.rf.exchange.server.data.datasource.jiangshan.websocket;

import com.rf.exchange.server.data.core.mq.RocketCandleProducer;
import com.rf.exchange.server.data.enums.CandleDataSource;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.IOException;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * @description: 匠山数据处理
 * @author: Petter
 * @create: 2024-10-07
 **/
@Component
@RequiredArgsConstructor
@Slf4j
public class JiangShanMessageProcessor {
    @Value("${exchange.candle-datasource.jiangShan.secret:1KcHQ8nPaicZeqIsnlOE}")
    private String reqSecret;
    @Setter
    private WebSocketSession webSocketSession;
    private final RocketCandleProducer rocketCandleProducer;
    // 心跳的执行器
    private final ScheduledExecutorService heartbeatScheduler = Executors.newScheduledThreadPool(1);

    /**
     * 处理返回的消息<br>
     * 数据格式:<br>
     * @formatter:off
     * {
     * 	"symbol": "AUDCAD",
     * 	"timestamp": 1727715501678,
     * 	"open": 0.93702,
     * 	"close": 0.93702,
     * 	"bid": "0.93705",
     * 	"low": 0.93695,
     * 	"high": 0.93705,
     * 	"percent": "0.00",
     * 	"change": "0.00",
     * 	"volume": 29
     * }
     *
     * @param responseMessage
     */
    @Async("websocketProcessExecutor")
    public void handleWebSocketResponseMessage(String responseMessage) {
        if ("身份验证成功".equals(responseMessage)) {
            log.info("匠山 身份验证成功，启动心跳");
            // 验证成功后开始执行心跳
            this.startHeartbeat();
            return;
        }
        if("pong".equals(responseMessage) || !responseMessage.startsWith("{")){
            log.info(">>>>>>>>>>>>> responseMessage:{}", responseMessage);
            return;
        }
        log.debug("匠山 推送数据:{}", responseMessage);
        rocketCandleProducer.sendPriceMessage(CandleDataSource.JIANG_SHAN.getValue(), responseMessage);
    }

    public void sendAuthMessage() {
        try {
            webSocketSession.sendMessage(new TextMessage(this.getForeignExchangeAndFuturesAuthMessage()));
        } catch (IOException e) {
            log.error("jiangShan websocket sendAuthMessage error", e);
        }
    }

    public void startHeartbeat() {
        heartbeatScheduler.scheduleAtFixedRate(() -> {
            try {
                webSocketSession.sendMessage(new TextMessage(this.getHeartbeatMessage()));
                log.info("jiangShan 发送心跳消息");
            } catch (IOException e) {
                log.error("jiangShan websocket sendHeartbeatMessage error", e);
            }
        }, 0, 10, TimeUnit.SECONDS);
    }

    public void stopHeartbeat() {
        heartbeatScheduler.shutdown();
    }

    public String getHeartbeatMessage() {
        return "heartbeat";
    }

    /**
     * 外汇期货认证消息 <br>
     * 格式： key:你的密钥:M <br>
     * 注: M是分钟数据范围 D是天数据范围 传D最高最低等数据就是当天的 M是每分钟的 <br>
     * !!测试传D会连接中断
     *
     * @return
     */
    public String getForeignExchangeAndFuturesAuthMessage() {
        return "key:" + reqSecret + ":M";
    }
}
