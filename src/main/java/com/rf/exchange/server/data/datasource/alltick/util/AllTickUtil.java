package com.rf.exchange.server.data.datasource.alltick.util;

import cn.hutool.core.util.IdUtil;
import com.rf.exchange.framework.dict.core.DictFrameworkUtils;
import com.rf.exchange.module.system.api.dict.dto.DictDataRespDTO;
import com.rf.exchange.module.system.enums.DictTypeConstants;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024-07-05
 */
@Slf4j
public class AllTickUtil {

    /**
     * 产品代码和系统交易对代码 Map
     * <p>
     * key: 产品code
     * value: 系统交易对代码
     */
    private static final ConcurrentMap<String, String> PRODUCT_TO_TRADE_CODE_MAP = new ConcurrentHashMap<>(100);

    /**
     * 系统交易对代码和产品代码 Map
     * <p>
     * key: 系统交易对代码
     * value: 产品代码
     */
    private static final ConcurrentMap<String, String> TRADE_CODE_TO_PRODUCT_MAP = new ConcurrentHashMap<>(100);

    public static void init() {
        reloadProductCodeMap();
    }

    /**
     * 重新加载产品代码和系统交易对代码的映射关系
     */
    public static void reloadProductCodeMap() {
        // 获取数据字典中AllTick所有支持的产品code label:系统交易对code value:产品code
        List<DictDataRespDTO> allDictDataList = DictFrameworkUtils.getDictDataDtoList(DictTypeConstants.ALLTICK_CODE_LIST);
        //
        Map<String, String> map = allDictDataList.stream().collect(Collectors.toMap(DictDataRespDTO::getLabel, DictDataRespDTO::getValue));
        TRADE_CODE_TO_PRODUCT_MAP.putAll(map);
        for (Map.Entry<String, String> entry : map.entrySet()) {
            PRODUCT_TO_TRADE_CODE_MAP.put(entry.getValue(), entry.getKey());
        }
        log.info("AllTick的产品列表 product2trade:[{}] trade2product:[{}]", PRODUCT_TO_TRADE_CODE_MAP, TRADE_CODE_TO_PRODUCT_MAP);
    }

    public static Map<String, String> getTradePairProductCodeMap() {
        return TRADE_CODE_TO_PRODUCT_MAP;
    }

    public static String getProductCode(String tradePairCode) {
        return TRADE_CODE_TO_PRODUCT_MAP.getOrDefault(tradePairCode, tradePairCode);
    }

    public static String getTradePairCode(String productCode) {
        return PRODUCT_TO_TRADE_CODE_MAP.getOrDefault(productCode, productCode);
    }

    public static String getTraceString() {
        return IdUtil.getSnowflakeNextIdStr();
    }

    public static boolean isProductContain(String code) {
        return TRADE_CODE_TO_PRODUCT_MAP.get(code) != null;
    }
}
