package com.rf.exchange.server.data.core.http.executor;

import com.rf.exchange.framework.xxljob.core.log.XJLog;
import com.rf.exchange.module.candle.enums.CandleTimeRangeEnum;
import com.rf.exchange.module.exc.enums.TradeAssetTypeEnum;
import com.rf.exchange.server.data.datasource.alltick.http.AllTickHttpClient;
import com.rf.exchange.server.data.datasource.polygon.http.PolygonMarketEnum;
import com.rf.exchange.server.data.datasource.polygon.http.PolygonService;
import com.rf.exchange.server.data.dto.DataSyncRequestDTO;
import com.rf.exchange.server.data.enums.CandleDataSource;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.BitSet;
import java.util.concurrent.ConcurrentHashMap;


/**
 * <AUTHOR>
 * @since 2024-07-21
 */
@Slf4j
@Component
public class CandleSyncGuardTaskExecutor {

    @Resource
    private AllTickHttpClient allTickHttpClient;
    @Resource
    private PolygonService polygonService;

    /**
     * 时间戳和BitSet的映射
     * 如果这个BitSet对应的k线类型索引都是1，则表示所有级别的k线数据都已经拉取完毕
     * <p>
     * key: 时间戳
     */
    private static final ConcurrentHashMap<Long, BitSet> REQUEST_BIT_SET_MAP = new ConcurrentHashMap<>((int) (200 / 0.75 + 1));


    public void executeRequest(DataSyncRequestDTO requestDTO) {
        // 如果数据源是AllTick的则发送请求
        if (CandleDataSource.ALLTICK.getValue() == requestDTO.getDataSource()) {
            if ((requestDTO.getTimeRangeEnum() != null && requestDTO.getTimeRangeEnum().getKLineTypeAllTick() == null) ||
                    (requestDTO.getTimeRangeEnums() != null && requestDTO.getTimeRangeEnums().isEmpty())) {
                XJLog.info("AllTick 不支持的k线类型 code:{}", requestDTO.getTradePairCode());
                return;
            }
            // 发送AllTick的k线请求
            if (requestDTO.isBatch()) {
                allTickHttpClient.sendBatchGetRequest(requestDTO);
            } else {
                allTickHttpClient.sendGetRequest(requestDTO);
            }
        } else if (CandleDataSource.POLYGON.getValue() == requestDTO.getDataSource()) {
            PolygonMarketEnum marketEnum = null;
            if (TradeAssetTypeEnum.CRYPTO.getType().equals(requestDTO.getTradeAssetType())) {
                marketEnum = PolygonMarketEnum.CRYPTO;
            } else if (TradeAssetTypeEnum.FOREX.getType().equals(requestDTO.getTradeAssetType())) {
                marketEnum = PolygonMarketEnum.FOREX;
            }
            if (marketEnum != null) {
                log.info("Polygon k线请求:[{}]", requestDTO);
                polygonService.fetchPolygonForexKLine(marketEnum, requestDTO);
            }
        }
    }

    /**
     * 添加ts关联的BitSet
     *
     * @param ts 时间戳
     */
    public static BitSet getBitSetOfTS(long ts) {
        BitSet bitSet = REQUEST_BIT_SET_MAP.get(ts);
        if (bitSet == null) {
            bitSet = new BitSet();
            REQUEST_BIT_SET_MAP.putIfAbsent(ts, bitSet);
        }
        return bitSet;
    }

    /**
     * 移除ts关联的bitSet
     *
     * @param ts 时间戳
     */
    public static void removeBitByTS(long ts) {
        REQUEST_BIT_SET_MAP.remove(ts);
    }

    /**
     * 检查所有的请求是否完成
     *
     * @param ts               时间戳
     * @param kLineType        k线类型
     * @param isIgnoreMinKline 是否忽略1分钟线
     * @return true:所有类型的k线数据都已经拉取
     */
    public static boolean setAndReturn(Long ts, Integer kLineType, Boolean isIgnoreMinKline) {
        BitSet bitSet = getBitSetOfTS(ts);
        bitSet.set(kLineType);

        for (CandleTimeRangeEnum timeRange : CandleTimeRangeEnum.values()) {
            if (isIgnoreMinKline && timeRange.equals(CandleTimeRangeEnum.MIN_ONE)) {
                continue;
            }
            // 如果任意一个k线类型没有获取到则返回false
            if (!bitSet.get(timeRange.getKLineTypeAllTick())) {
                return false;
            }
        }
        return true;
    }

}
