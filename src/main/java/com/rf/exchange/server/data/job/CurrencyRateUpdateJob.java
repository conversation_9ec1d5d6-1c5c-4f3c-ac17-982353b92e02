package com.rf.exchange.server.data.job;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.rf.exchange.module.system.api.currencyrate.dto.CurrencyRateDTO;
import com.rf.exchange.module.system.dal.redis.currencyrate.CurrencyRateRedisDAO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * @description: 更新汇率
 * @author: Petter
 * @create: 2024-09-16
 **/
@Slf4j
@Component
public class CurrencyRateUpdateJob {

    private static final String DEFAULT_CURRENCY_CODE = "USD";

    @Resource
    private CurrencyRateRedisDAO currencyRateRedisDAO;

    /**
     * 收费订阅-可以10分钟更新一次
     * 三方接口返回数据格式：
     * @formatter:off
     * {
     *   "success":true,
     *   "timestamp":1726489457,
     *   "base":"EUR",
     *   "date":"2024-09-16",
     *   "rates":{
     *     "AED":4.087396,
     *     "AFN":77.365063,
     *     "ANG":2.006627,
     *     ...
     *    }
     * }
     * @formatter:on
     */
    // @Scheduled(cron = "0/15 * * * * ?")
    public void updateCurrencyRateByV1() {
        try {
            String reqApi =
                "https://data.fixer.io/api/latest?access_key=********************************&format=1&base="
                    + DEFAULT_CURRENCY_CODE;
            int timeout = 10 * 1000;
            String responseStr = HttpUtil.get(reqApi, timeout);
            if (StrUtil.isNotEmpty(responseStr)) {
                JSONObject jsonObject = JSONUtil.parseObj(responseStr);
                if (jsonObject.get("success") == Boolean.TRUE) {
                    Object rates = jsonObject.get("rates");
                    if (Objects.nonNull(rates) && rates instanceof JSONObject entries) {
                        List<CurrencyRateDTO> currencyRateDTOList = Lists.newArrayList();
                        for (String currencyKey : entries.keySet()) {
                            CurrencyRateDTO currencyRateDTO = new CurrencyRateDTO();
                            currencyRateDTO.setBaseCurrency(DEFAULT_CURRENCY_CODE);
                            currencyRateDTO.setQuoteCurrency(currencyKey);
                            currencyRateDTO.setRate(new BigDecimal(String.valueOf(entries.get(currencyKey))));
                            currencyRateDTOList.add(currencyRateDTO);
                        }
                        currencyRateRedisDAO.set(currencyRateDTOList);
                    }
                }
            }
        } catch (Exception e) {
            log.error("update currency rate error", e);
        }
    }

    /**
     * 免费接口，数据每天更新一次<br>
     * Updates Once Per Day
     * 1.5K API Requests p/m
     * 数据格式：
     *  @formatter:off
     * {
     *  "result":"success",
     *  "documentation":"https://www.exchangerate-api.com/docs",
     *  "terms_of_use":"https://www.exchangerate-api.com/terms",
     *  "time_last_update_unix":1726444801,
     *  "time_last_update_utc":"Mon, 16 Sep 2024 00:00:01 +0000",
     *  "time_next_update_unix":1726531201,
     *  "time_next_update_utc":"Tue, 17 Sep 2024 00:00:01 +0000",
     *  "base_code":"USD",
     *  "conversion_rates":{
     *   "USD":1,
     *   "AED":3.6725,
     *   "AFN":69.7374,
     *   "ALL":89.8572,
     *   "AMD":387.2627,
     *   "ANG":1.7900,
     *   .....
     *   }
     * }
     *    @formatter:on
     */
    @Scheduled(cron = "0 0 0 1/3 * ?")
    public void updateCurrencyRateByV2() {
        log.info(">>> update currency rate by v2 start <<<");
        try {
            String reqApi =
                "https://v6.exchangerate-api.com/v6/************************/latest/" + DEFAULT_CURRENCY_CODE;
            String responseStr = HttpUtil.get(reqApi);
            if (StrUtil.isNotEmpty(responseStr)) {
                JSONObject jsonObject = JSONUtil.parseObj(responseStr);
                if ("success".equals(jsonObject.get("result"))) {
                    Object rates = jsonObject.get("conversion_rates");
                    if (Objects.nonNull(rates) && rates instanceof JSONObject entries) {
                        List<CurrencyRateDTO> currencyRateDTOList = Lists.newArrayList();
                        for (String currencyKey : entries.keySet()) {
                            CurrencyRateDTO currencyRateDTO = new CurrencyRateDTO();
                            currencyRateDTO.setBaseCurrency(DEFAULT_CURRENCY_CODE);
                            currencyRateDTO.setQuoteCurrency(currencyKey);
                            currencyRateDTO.setRate(new BigDecimal(String.valueOf(entries.get(currencyKey))));
                            currencyRateDTOList.add(currencyRateDTO);
                        }
                        currencyRateRedisDAO.set(currencyRateDTOList);
                    }
                }
            }
        } catch (Exception e) {
            log.error("update currency rate error", e);
        }
        log.info(">>> update currency rate by v2 end <<<");
    }

}
