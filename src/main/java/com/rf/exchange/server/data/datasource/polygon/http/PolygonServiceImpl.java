package com.rf.exchange.server.data.datasource.polygon.http;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Joiner;
import com.rf.exchange.module.candle.config.CandleDataSourceProperties;
import com.rf.exchange.module.candle.enums.CandleTimeRangeEnum;
import com.rf.exchange.module.exc.api.tradepair.TradePairApi;
import com.rf.exchange.module.exc.api.tradepair.dto.TradePairRespDTO;
import com.rf.exchange.server.data.core.http.executor.CandleSyncGuardTaskExecutor;
import com.rf.exchange.server.data.core.mq.RocketCandleProducer;
import com.rf.exchange.server.data.dal.dataobject.CandleLastSyncDO;
import com.rf.exchange.server.data.dal.redis.PolygonTickerRedis;
import com.rf.exchange.server.data.datasource.alltick.http.resp.AllTickKLine;
import com.rf.exchange.server.data.datasource.polygon.dto.PolygonKLine;
import com.rf.exchange.server.data.datasource.polygon.dto.PolygonKLinePathArgus;
import com.rf.exchange.server.data.datasource.polygon.util.PolygonUtil;
import com.rf.exchange.server.data.datasource.polygon.websocket.PolygonWebSocketHandler;
import com.rf.exchange.server.data.datasource.polygon.websocket.PolygonWebsocketConnection;
import com.rf.exchange.server.data.dto.DataSyncRequestDTO;
import com.rf.exchange.server.data.enums.CandleDataSource;
import com.rf.exchange.server.data.service.CandleDataSyncService;
import com.rf.exchange.server.data.service.CandleLastSyncService;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentSkipListSet;
import java.util.stream.Collectors;

import static com.rf.exchange.server.data.datasource.polygon.websocket.PolygonConstants.*;

/**
 * <AUTHOR>
 * @since 2024-10-08
 */
@Slf4j
@Service
public class PolygonServiceImpl implements PolygonService {

    @Resource
    private PolyonHTTPClient polyonHTTPClient;
    @Resource
    private CandleDataSourceProperties properties;
    @Resource
    private PolygonTickerRedis polygonTickerRedis;
    @Resource
    private CandleDataSyncService candleDataSyncService;
    @Resource
    private RocketCandleProducer rocketCandleProducer;
    @Resource
    private CandleLastSyncService lastSyncService;
    @Resource
    @Lazy
    private TradePairApi tradePairApi;

    private static final ConcurrentHashMap<String, WebSocketSession> SESSION_MAP = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, PolygonWebsocketConnection> CONNECTION_MAP = new ConcurrentHashMap<>();

    // 需要订阅的Polygon的ticker, 值为Polygon的ticker非系统交易对
    private static final Set<String> NEED_SUB_TICKERS = new HashSet<>();

    private static final ConcurrentHashMap<String, ConcurrentSkipListSet<String>> TMP_TICKER_MAP = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, String> TMP_SUB_TICKERS = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        CandleDataSourceProperties.PolygonDataSource polygon = properties.getPolygon();
        PolygonWebSocketHandler cryptoHandler = new PolygonWebSocketHandler(PolygonMarketEnum.CRYPTO, this);
        PolygonWebsocketConnection cryptoConnection = new PolygonWebsocketConnection(polygon.getWsHostCrypto(), cryptoHandler);
        PolygonWebSocketHandler forexHandler = new PolygonWebSocketHandler(PolygonMarketEnum.FOREX, this);
        PolygonWebsocketConnection forexConnection = new PolygonWebsocketConnection(polygon.getWsHostForex(), forexHandler);
        CONNECTION_MAP.put(PolygonMarketEnum.FOREX.getMarket(), forexConnection);
        CONNECTION_MAP.put(PolygonMarketEnum.CRYPTO.getMarket(), cryptoConnection);
    }

    @Override
    public boolean isSupportedTicker(PolygonMarketEnum marketEnum, String ticker) {
        if (StrUtil.isEmpty(ticker)) {
            return false;
        }
        if (TMP_TICKER_MAP.isEmpty()) {
            final Map<String, List<String>> allTickerMap = polygonTickerRedis.getAllTickers();
            if (allTickerMap.containsKey(marketEnum.getMarket())) {
                final List<String> tickerNames = allTickerMap.get(marketEnum.getMarket());
                TMP_TICKER_MAP.put(marketEnum.getMarket(), new ConcurrentSkipListSet<>(tickerNames));
            }
        }
        final ConcurrentSkipListSet<String> tickers = TMP_TICKER_MAP.get(marketEnum.getMarket());
        if (CollUtil.isEmpty(tickers)) {
            return false;
        }
        return tickers.contains(ticker);
    }

    //@Override
    //public String getPolygonTickerName(Integer assetType, String baseCode, String quoteCode, boolean isWebSocket) {
    //    String ticker = null;
    //    final String replacedQuote = quoteCode.replace("USDT", "USD");
    //    if (TradeAssetTypeEnum.FOREX.getType().equals(assetType)) {
    //        if (isWebSocket) {
    //            ticker = "C:" + baseCode + "/" + replacedQuote;
    //        } else {
    //            ticker = "C:" + baseCode + replacedQuote;
    //        }
    //    } else if (TradeAssetTypeEnum.CRYPTO.getType().equals(assetType)) {
    //        if (isWebSocket) {
    //            ticker = "X:" + baseCode + "-" + replacedQuote;
    //        } else {
    //            ticker = "X:" + baseCode + replacedQuote;
    //        }
    //    } else {
    //        log.error("Polygon不支持的交易对类型");
    //    }
    //    return ticker;
    //}

    @Override
    @SuppressWarnings({"unchecked", "rawtypes"})
    public void fetchPolygonTickerTypes(PolygonMarketEnum marketEnum) {
        if (!properties.getPolygon().getEnable()) {
            return;
        }
        final Map<String, Object> response = polyonHTTPClient.sendTickerSnapshotsRequest(properties.getPolygon(), marketEnum);

        Object obj = response.get("tickers");
        if (obj instanceof List tickers) {
            Set<String> tickerNameSet = new HashSet<>(tickers.size());

            for (Object tickerObj : tickers) {
                Map<String, Object> tickerObjMap = (Map<String, Object>) tickerObj;
                String ticker = tickerObjMap.get("ticker").toString();
                tickerNameSet.add(ticker);
            }

            // 缓存ticker的Name
            polygonTickerRedis.setMarketTickers(marketEnum.getMarket(), new ArrayList<>(tickerNameSet));
            // 更新内存中的ticker map
            TMP_TICKER_MAP.put(marketEnum.getMarket(), new ConcurrentSkipListSet<>(tickerNameSet));
        }
    }

    @Override
    public void fetchPolygonForexKLine(PolygonMarketEnum marketEnum, DataSyncRequestDTO requestDTO) {
        final String ticker = PolygonUtil.tickerName(requestDTO.getTradeAssetType(), requestDTO.getBaseCode(), requestDTO.getQuoteCode(), false);
        if (!isSupportedTicker(marketEnum, ticker)) {
            log.error("Polygon不支持的交易对:[{}]", requestDTO.getTradePairCode());
            return;
        }
        final Map<String, Object> response = internalSendHttpRequest(ticker, requestDTO, requestDTO.getTimeRangeEnum());
        if (requestDTO.isPolygonHistorySync()) {
            final List<PolygonKLine> kLines = parseResponse(response, requestDTO);
            log.info("获取Polygon的k线历史数据 条数:[{}] 交易对:[{}] from:[{}] to:[{}] spanName:[{}] mul:[{}]", kLines.size(), requestDTO.getTradePairCode(), requestDTO.getStartTime(), requestDTO.getStartTime(), requestDTO.getTimeRangeEnum().getSpanName(), requestDTO.getTimeRangeEnum().getRange());
            // 保存数据到数据库
            insertKLines(requestDTO, kLines);

            // 如果没有任何一条数据返回，需要检查交易对是否是休市
            //if (kLines.isEmpty() && requestDTO.getTimeRangeEnum().equals(CandleTimeRangeEnum.DAY_ONE) || kLines.size() >= requestDTO.getBarCount()) {

            // 标记请求参数中的k线类型的数据已经拉取完成了，如果所有类型的k线类型都拉取完成了则更新交易对的最后数据同步时间
            boolean isAllFinished = CandleSyncGuardTaskExecutor.setAndReturn(requestDTO.getEndTS(), requestDTO.getTimeRangeEnum().getKLineTypeAllTick(), false);
            log.info("Polygon code:[{}] bitset:[{}] kLineType:[{}]", isAllFinished, requestDTO.getTradePairCode(), requestDTO.getTimeRangeEnum().getKLineTypeAllTick());
            if (isAllFinished) {
                if (requestDTO.isNeedUpdateLastSyncDate()) {
                    final CandleLastSyncDO syncRecord = new CandleLastSyncDO();
                    syncRecord.setCode(requestDTO.getTradePairCode());
                    syncRecord.setLastSyncDate(requestDTO.getStartTime());
                    lastSyncService.updateLastSyncRecord(syncRecord);
                }
                log.info("Polygon {} 所有请求类型的k线拉取数据请求都已经完成", requestDTO.getTradePairCode());
                // 移除时间戳关联的BitSet
                CandleSyncGuardTaskExecutor.removeBitByTS(requestDTO.getEndTS());
            }

            //}

        } else if (requestDTO.isPolygonRealTimeSync()) {
            final List<PolygonKLine> kLines = parseResponse(response, requestDTO);
            log.info("获取Polygon的k线实时数据 条数:[{}] 交易对:[{}] from:[{}] to:[{}]", kLines.size(), requestDTO.getTradePairCode(), requestDTO.getStartTS(), requestDTO.getEndTS());
            insertKLines(requestDTO, kLines);
        }
    }

    // =============== websocket相关

    @Override
    public void saveWSSession(PolygonMarketEnum marketEnum, WebSocketSession session) {
        SESSION_MAP.put(marketEnum.getMarket(), session);
    }

    private WebSocketSession getWSSession(PolygonMarketEnum marketEnum) {
        return SESSION_MAP.get(marketEnum.getMarket());
    }

    @Override
    public void handleWSMessage(TextMessage textMessage, WebSocketSession session, PolygonMarketEnum marketEnum) {
        log.debug("收到polygon的ws消息:{}", textMessage.getPayload());
        String json = textMessage.getPayload();
        final JSONArray objectArray = JSONUtil.parseArray(json);
        final List<Dict> objectList = JSONUtil.toList(objectArray, Dict.class);
        if (objectList.isEmpty()) {
            return;
        }
        for (Dict dict : objectList) {
            handlePolygonWSRespDict(dict, session, marketEnum);
        }
    }

    @Override
    public void updateSubscribeTradePair(List<TradePairRespDTO> tradePairs) {
        if (CollUtil.isEmpty(tradePairs)) {
            return;
        }
        if (!properties.getPolygon().getEnable()) {
            return;
        }

        List<TradePairRespDTO> forexTradePairs = new ArrayList<>();
        List<TradePairRespDTO> cryptoTradePairs = new ArrayList<>();

        for (TradePairRespDTO tradePair : tradePairs) {
            if (tradePair.getSource().equals(CandleDataSource.POLYGON.getValue())) {
                final PolygonMarketEnum anEnum = PolygonUtil.marketOfAsset(tradePair.getAssetType());
                if (PolygonMarketEnum.CRYPTO.equals(anEnum)) {
                    cryptoTradePairs.add(tradePair);
                } else if (PolygonMarketEnum.FOREX.equals(anEnum)) {
                    forexTradePairs.add(tradePair);
                }
            }
        }
        final Set<String> forexTradeCode = forexTradePairs.stream().map(TradePairRespDTO::getCode).collect(Collectors.toSet());
        final Set<String> cryptoTradeCode = cryptoTradePairs.stream().map(TradePairRespDTO::getCode).collect(Collectors.toSet());

        if (isNeedUpdateSub(forexTradeCode, PolygonMarketEnum.FOREX)) {
            internalSubscribeTradePairs(tradePairs, PolygonMarketEnum.FOREX);
        }
        if (isNeedUpdateSub(cryptoTradeCode, PolygonMarketEnum.CRYPTO)) {
            internalSubscribeTradePairs(tradePairs, PolygonMarketEnum.CRYPTO);
        }
    }

    private boolean isNeedUpdateSub(Set<String> codes, PolygonMarketEnum marketEnum) {
        if (CollUtil.isEmpty(codes)) {
            return false;
        }
        final String newSub = new TreeSet<>(codes).toString();
        final String alreadySub = TMP_SUB_TICKERS.get(marketEnum.getMarket());
        if (StrUtil.isNotEmpty(alreadySub)) {
            return alreadySub.equals(newSub);
        } else {
            return true;
        }
    }

    @Override
    public void connect() {
        connectWithMarket(PolygonMarketEnum.FOREX);
        connectWithMarket(PolygonMarketEnum.CRYPTO);
    }

    @Override
    public void connectWithMarket(PolygonMarketEnum marketEnum) {
        PolygonWebsocketConnection connection = CONNECTION_MAP.get(marketEnum.getMarket());
        connection.connect();
    }

    private void handlePolygonWSRespDict(Dict dict, WebSocketSession session, PolygonMarketEnum marketEnum) {
        if (dict.containsKey(RESP_KEY_EV)) {
            // ws连接状态
            final String ev = dict.getStr(RESP_KEY_EV);
            if (RESP_KEY_STATUS.equals(ev)) {
                String status = dict.getStr(RESP_KEY_STATUS);
                // 如果status为connected则表示已经连接
                if ("connected".equals(status)) {
                    // 发送认证消息
                    sendWebSocketMessage(session, wsAuthMessage());
                } else if ("auth_success".equals(status)) {
                    // 认证成功之后发送订阅消息
                    sendSubscribeMessage(session, marketEnum);
                }
            } else {
                final String jsonMessage = JSONUtil.toJsonStr(dict);
                try {
                    rocketCandleProducer.sendPriceMessage(CandleDataSource.POLYGON.getValue(), jsonMessage);
                } catch (Exception e) {
                    log.error("Polygon的ws消息转发RocketMQ失败 error:[{}]", e.getMessage());
                }
            }
        } else {
            log.error("无法解析Polygon的ws消息:{}", dict);
        }
    }

    private void sendSubscribeMessage(WebSocketSession session, PolygonMarketEnum marketEnum) {
        // 获取需要订阅的交易对
        List<TradePairRespDTO> listCached = tradePairApi.getTradePairEnableListCached();
        if (CollectionUtil.isEmpty(listCached)) {
            return;
        }
        internalSubscribeTradePairs(listCached, marketEnum);
    }

    private void internalSubscribeTradePairs(List<TradePairRespDTO> tradePairs, PolygonMarketEnum marketEnum) {
        // 需要订阅的Ticker名称
        Set<String> tickers = new HashSet<>(tradePairs.size());
        for (TradePairRespDTO tradePair : tradePairs) {
            // 复制币和自发币都跳过
            if (tradePair.getIsCopy() || tradePair.getIsCustom()) {
                continue;
            }
            if (tradePair.getSource().equals(CandleDataSource.POLYGON.getValue())) {
                final PolygonMarketEnum market = PolygonUtil.marketOfAsset(tradePair.getAssetType());
                final String tickerName = PolygonUtil.tickerName(tradePair.getAssetType(), tradePair.getBaseAsset(), tradePair.getQuoteAsset(), true);
                if (null != tickerName) {
                    if (marketEnum.equals(market)) {
                        tickers.add(tickerName);
                        log.info("Polygon >>>tickerName:[{}] <<<tpCode:[{}]", tickerName, tradePair.getCode());
                    }
                }
            }
        }
        if (CollUtil.isNotEmpty(tickers)) {
            final WebSocketSession session = getWSSession(marketEnum);
            if (session == null) {
                log.error("Polygon【{}】ws会话为空", marketEnum.getName());
                return;
            }
            String message = wsSubscribeMessage(tickers, marketEnum);
            sendWebSocketMessage(session, message);
        } else {
            log.error("Polygon没有需要订阅的【{}】交易对", marketEnum.getName());
        }
    }

    private String wsAuthMessage() {
        Dict dict = Dict.create()
                .set(REQ_KEY_ACTION, REQ_ACTION_AUTH)
                .set(REQ_KEY_PARAMS, properties.getPolygon().getSecret());
        return JSONUtil.toJsonStr(dict);
    }

    private String wsSubscribeMessage(Set<String> tickers, PolygonMarketEnum marketEnum) {
        if (CollUtil.isEmpty(tickers)) {
            return "";
        }
        List<String> mappedTickers = new ArrayList<>(tickers.size());
        for (String ticker : tickers) {
            // 如果是外汇的则订阅Quote，其他的订阅trade
            if (PolygonMarketEnum.FOREX.equals(marketEnum)) {
                mappedTickers.add(marketEnum.getEvQuote() + ticker);
            } else {
                mappedTickers.add(marketEnum.getEvTrade() + ticker);
            }
            //// 所有类型的交易对都订阅quote消息
            //mappedTickers.add(marketEnum.getEvQuote() + ticker);
        }
        String combineTicker = Joiner.on(",").join(mappedTickers);
        Dict dict = Dict.create()
                .set(REQ_KEY_ACTION, REQ_ACTION_SUB)
                .set(REQ_KEY_PARAMS, combineTicker);
        return JSONUtil.toJsonStr(dict);
    }

    private void sendWebSocketMessage(WebSocketSession session, String jsonMessage) {
        try {
            session.sendMessage(new TextMessage(jsonMessage));
            log.info("发送Polygon ws消息:[{}]", jsonMessage);
        } catch (IOException e) {
            log.error("发送Polygon的ws消息失败:[{}] 消息内容:[{}]", e.getMessage(), jsonMessage);
        }
    }

    // =============== websocket相关

    /**
     * 解析请求响应
     *
     * @param responseMap 请求返回结果
     * @param requestDTO  请求信息
     * @return k线数组
     */
    private List<PolygonKLine> parseResponse(Map<String, Object> responseMap, DataSyncRequestDTO requestDTO) {
        String ticker = (String) responseMap.get("ticker");
        if (responseMap.containsKey("results")) {
            final ObjectMapper mapper = new ObjectMapper();
            final List<PolygonKLine> results = mapper.convertValue(responseMap.get("results"), new TypeReference<>() {
            });
            for (PolygonKLine kLine : results) {
                kLine.setTicker(ticker);
                kLine.setCode(requestDTO.getTradePairCode());
            }
            return results;
        }
        return List.of();
    }

    private Map<String, Object> internalSendHttpRequest(String ticker, DataSyncRequestDTO requestDTO, CandleTimeRangeEnum timeRangeEnum) {
        assert ticker != null : "ticker不能为空";
        assert requestDTO.getEndTS() != null : "endTs不能为空";

        if (requestDTO.getStartTS() == null) {
            long startTs = requestDTO.getEndTS() - timeRangeEnum.getSecs();
            requestDTO.setStartTS(startTs);
        }
        assert requestDTO.getStartTS() != null : "startTs不能为空";
        assert requestDTO.getStartTS() < requestDTO.getEndTS() : "startTs必须小于等于endTs";

        PolygonKLinePathArgus pathArgus = new PolygonKLinePathArgus();
        pathArgus.setTicker(ticker);
        pathArgus.setTimespan(timeRangeEnum.getSpanName());
        pathArgus.setMultiplier(String.valueOf(timeRangeEnum.getRange()));
        String date = LocalDateTimeUtil.format(requestDTO.getStartTime(), "yyyy-MM-dd");
        pathArgus.setFrom(date);
        pathArgus.setTo(date);
        return polyonHTTPClient.sendKLineRequest(properties.getPolygon(), pathArgus);
    }

    private void insertKLines(DataSyncRequestDTO requestDTO, List<PolygonKLine> kLines) {
        assert requestDTO.getTimeRangeEnum() != null : "";
        List<AllTickKLine> kLineList = new ArrayList<>(kLines.size());
        for (PolygonKLine kLine : kLines) {
            AllTickKLine line = new AllTickKLine();
            line.setTimestamp(String.valueOf(kLine.getTimestamp()));
            line.setTimeRange(requestDTO.getTimeRangeEnum().getRange());
            line.setTimeType(requestDTO.getTimeRangeEnum().getTypeValue());
            line.setVolume(kLine.getVolume().toString());
            line.setHighPrice(kLine.getHighPrice().toString());
            line.setLowPrice(kLine.getLowPrice().toString());
            line.setOpenPrice(kLine.getOpenPrice().toString());
            line.setClosePrice(kLine.getClosePrice().toString());
            line.setTurnover("0");
            kLineList.add(line);
        }
        candleDataSyncService.insertCandleRecords(requestDTO.getTradePairCode(), requestDTO.getTimeRangeEnum(), kLineList);
    }
}
