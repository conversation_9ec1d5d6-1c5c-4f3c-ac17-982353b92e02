package com.rf.exchange.server.data.datasource.polygon.util;

import com.rf.exchange.module.exc.enums.TradeAssetTypeEnum;
import com.rf.exchange.server.data.datasource.polygon.http.PolygonMarketEnum;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2024-10-20
 */
@Slf4j
public class PolygonUtil {

    public static PolygonMarketEnum marketOfAsset(int assetType) {
        if (TradeAssetTypeEnum.CRYPTO.getType() == assetType) {
            return PolygonMarketEnum.CRYPTO;
        } else if (TradeAssetTypeEnum.FOREX.getType() == assetType) {
            return PolygonMarketEnum.FOREX;
        }
        return null;
    }

    /**
     * Polygon的交易对的名称
     *
     * @param assetType   资产类型
     * @param baseCode    基础资产
     * @param quoteCode   报价资产
     * @param isWebSocket 是否是websocket使用
     * @return ticker名称
     */
    public static String tickerName(int assetType, String baseCode, String quoteCode, boolean isWebSocket) {
        String ticker = null;
        final String replacedQuote = quoteCode.replace("USDT", "USD");
        if (TradeAssetTypeEnum.FOREX.getType() == assetType) {
            if (isWebSocket) {
                ticker = "C:" + baseCode + "-" + replacedQuote;
            } else {
                ticker = "C:" + baseCode + replacedQuote;
            }
        } else if (TradeAssetTypeEnum.CRYPTO.getType() == assetType) {
            if (isWebSocket) {
                ticker = "X:" + baseCode + "-" + replacedQuote;
            } else {
                ticker = "X:" + baseCode + replacedQuote;
            }
        }
        return ticker;
    }

    /**
     * 获取系统交易对的代码
     * ticker转系统交易对代码
     *
     * @param ticker      polygon的ticker名称
     * @param isWebSocket 是否是websocket中使用的ticker
     * @return 系统交易对代码
     */
    public static String tradeCode(String ticker, boolean isWebSocket) {
        assert ticker != null : "ticker不能为空";
        String tradeCode = null;
        if (isWebSocket) {
            if (ticker.contains("/")) {
                final String[] splits = ticker.split("/");
                tradeCode = splits[0] + splits[1];
            } else if (ticker.contains("-")) {
                final String[] splits = ticker.split("-");
                tradeCode = splits[0] + splits[1].replace("USD", "USDT");
            }
        } else {
            if (ticker.startsWith("C:")) {
                final String[] splits = ticker.split(":");
                tradeCode = splits[1];
            } else if (ticker.startsWith("X:")) {
                final String[] splits = ticker.split(":");
                tradeCode = splits[1].replace("USD", "USDT");
            }
        }
        return tradeCode;
    }
}
