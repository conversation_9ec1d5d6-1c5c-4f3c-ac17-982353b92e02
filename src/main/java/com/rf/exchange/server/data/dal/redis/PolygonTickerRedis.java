package com.rf.exchange.server.data.dal.redis;

import cn.hutool.core.collection.CollUtil;
import com.rf.exchange.framework.common.util.json.JsonUtils;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.rf.exchange.server.data.dal.redis.RedisKeyConstants.POLYGON_TICKER_TYPE;

/**
 * <AUTHOR>
 * @since 2024-10-08
 */
@Repository
public class PolygonTickerRedis {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 缓存所有Polygon的Ticker
     */
    public void setMarketTickers(String market, List<String> values) {
        if (CollUtil.isEmpty(values)) {
            return;
        }
        final String json = JsonUtils.toJsonString(values);
        stringRedisTemplate.opsForHash().put(POLYGON_TICKER_TYPE, market, json);
    }

    /**
     * 获取Polygon的Ticker
     */
    public List<String> getMarketTickers(String market) {
        final Object obj = stringRedisTemplate.opsForHash().get(POLYGON_TICKER_TYPE, market);
        return parseJsonList(obj);
    }

    /**
     * 获取所有的polygon的ticker
     * @return polygon的ticker key:market类型 value:ticker列表
     */
    public Map<String, List<String>> getAllTickers() {
        final Map<Object, Object> entries = stringRedisTemplate.opsForHash().entries(POLYGON_TICKER_TYPE);
        Map<String, List<String>> result = new HashMap<>();
        for (Map.Entry<Object, Object> entry : entries.entrySet()) {
            final List<String> tickers = parseJsonList(entry.getValue());
            result.put(entry.getKey().toString(), tickers);
        }
        return result;
    }

    private List<String> parseJsonList(Object obj) {
        if (obj instanceof String json) {
            return JsonUtils.parseArray(json, String.class);
        }
        return List.of();
    }
}
