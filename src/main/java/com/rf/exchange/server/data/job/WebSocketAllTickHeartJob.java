package com.rf.exchange.server.data.job;

import com.rf.exchange.server.data.core.websocket.container.CandleWebSocketManagerContainer;
import com.rf.exchange.server.data.enums.CandleDataSource;
import com.rf.exchange.server.data.service.CandleDataWebSocketService;
import com.xxl.job.core.handler.annotation.XxlJob;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @since 2024-06-26
 */
@Slf4j
@Component
public class WebSocketAllTickHeartJob {

    @Resource
    private CandleWebSocketManagerContainer managerContainer;

    @Resource
    private CandleDataWebSocketService webSocketService;

    /**
     * 这个定时任务不使用xxl-job管理
     * <p>
     * 因为这个心跳与alltick的websocket连接关系紧密
     * 防止因为失误操作导致这个定时任务没有执行而造成ws的连接断开
     */
    @XxlJob("allTickWSHeartJob")
    public void heart() {
        if (managerContainer.isManagerConnected(CandleDataSource.ALLTICK)) {
            webSocketService.sendHeartbeatMessage(CandleDataSource.ALLTICK);
        } else {
            log.error("AllTick的websocket处于非连接状态");
        }
    }
}
