package com.rf.exchange.server.data.job;

import cn.hutool.core.collection.CollectionUtil;
import com.rf.exchange.module.candle.config.CandleDataSourceProperties;
import com.rf.exchange.module.exc.api.tradepair.TradePairApi;
import com.rf.exchange.module.exc.api.tradepair.dto.TradePairRespDTO;
import com.rf.exchange.module.system.api.currencyrate.CurrencyRateApi;
import com.rf.exchange.server.data.core.mq.RocketCandleConsumer;
import com.rf.exchange.server.data.core.mq.dto.UpdateDto;
import com.rf.exchange.server.data.core.websocket.container.CandleWebSocketManagerContainer;
import com.rf.exchange.server.data.dal.redis.CandleDataSourceMonitorRedisDAO;
import com.rf.exchange.server.data.dal.redis.CandleDataSourceSubscribeRedisDAO;
import com.rf.exchange.server.data.datasource.alltick.util.AllTickUtil;
import com.rf.exchange.server.data.datasource.alltick.websocket.AllTickProductSubscriber;
import com.rf.exchange.server.data.datasource.jiangshan.service.JiangShanWebSocketService;
import com.rf.exchange.server.data.datasource.jiangshan.util.JiangShanUtil;
import com.rf.exchange.server.data.datasource.polygon.http.PolygonService;
import com.rf.exchange.server.data.enums.CandleDataSource;
import com.rf.exchange.server.data.service.CandleDataWebSocketService;
import com.xxl.job.core.handler.annotation.XxlJob;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 数据源切换定时任务
 *
 * <AUTHOR>
 * @since 2024-06-26
 */
@Slf4j
@Component
public class WebSocketDataSourceSwitchJob {

    @Value("${exchange.candle-datasource.expire_secs:30}")
    private int expireSeconds;

    @Resource
    @Lazy
    private TradePairApi tradePairApi;
    @Resource
    private CandleWebSocketManagerContainer managerContainer;
    @Resource
    private CandleDataWebSocketService candleDataWebSocketService;
    @Resource
    private CandleDataSourceSubscribeRedisDAO candleDataSourceSubscribeRedisDAO;
    @Resource
    private CandleDataSourceMonitorRedisDAO candleDataSourceMonitorRedisDAO;
    @Resource
    private CandleDataSourceProperties dataSourceProperties;
    @Resource
    private CurrencyRateApi currencyRateApi;
    @Resource
    private AllTickProductSubscriber allTickSubscriber;
    @Resource
    JiangShanWebSocketService jiangShanWebSocketService;
    @Resource
    private PolygonService polygonService;

    /**
     * 扫描交易对数据的定时任务
     */
    @XxlJob("scanTradePairSourceJob")
    public void scanTradePairSource() {
        log.info("开始检查数据源 {}", LocalDateTime.now());

        // 更新AllTickCodeUtil中的产品code和交易对代码的映射关系
        AllTickUtil.reloadProductCodeMap();
        // 更新匠山中的产品code和交易对代码的映射关系
        JiangShanUtil.reloadProductCodeMap();

        // 获取系统所有可用的交易对
        List<TradePairRespDTO> allSystemTradePairList = tradePairApi.getTradePairEnableListCached();
        if (CollectionUtil.isEmpty(allSystemTradePairList)) {
            return;
        }

        // 所有需要订阅AllTick的交易对
        Set<String> allTickTradPairCodes = new HashSet<>(allSystemTradePairList.size());
        List<TradePairRespDTO> polygonTradPairs = new ArrayList<>(allSystemTradePairList.size());
        for (TradePairRespDTO tradePairRespDTO : allSystemTradePairList) {
            if (tradePairRespDTO.getIsCustom() == null || tradePairRespDTO.getIsCustom()) {
                continue;
            }
            if (CandleDataSource.ALLTICK.getValue() == tradePairRespDTO.getSource()
                    && AllTickUtil.isProductContain(tradePairRespDTO.getCode())) {
                allTickTradPairCodes.add(tradePairRespDTO.getCode());
            } else if (CandleDataSource.POLYGON.getValue() == tradePairRespDTO.getSource()) {
                polygonTradPairs.add(tradePairRespDTO);
            }
        }

        // alltick的websocket连接
        if (!managerContainer.isManagerConnected(CandleDataSource.ALLTICK)) {
            candleDataWebSocketService.connect(CandleDataSource.ALLTICK);
        } else {
            log.info("系统启用的AllTick交易对: [{}]", allTickTradPairCodes);
            int op = allTickSubscriber.getSubscribeBehavior(allTickTradPairCodes);
            if (op < 0) {
                // 取消订阅
                final List<String> subscribedCodes = allTickSubscriber.getSubscribedCodes();
                candleDataWebSocketService.sendUnsubscribeMessage(CandleDataSource.ALLTICK, new HashSet<>(subscribedCodes));
            } else if (op > 0) {
                // 更新订阅
                candleDataWebSocketService.sendUpdateSubscribeMessage(CandleDataSource.ALLTICK, allTickTradPairCodes);
            } else {
                log.info("新旧订阅交易对[相同] 已经订阅交易对:{}", allTickTradPairCodes);
            }
        }

        // polygon的websocket连接
        if (!managerContainer.isManagerConnected(CandleDataSource.POLYGON)) {
            polygonService.connect();
        } else {
            // 订阅Polygon的交易对
            polygonService.updateSubscribeTradePair(polygonTradPairs);
        }

        if (jiangShanWebSocketService.isEnable() && !jiangShanWebSocketService.isConnected()) {
            // 不需要订阅，认证成功后即开始推送数据
            jiangShanWebSocketService.connect();
        }

        // 获取数据源最后有推送的时间
        // Long lastPushTime = candleDataSourceMonitorRedisDAO.getLastPushTime(CandleDataSource.ALLTICK);
        // log.info("allTick 最近推送时间:{}", lastPushTime);
        // if (Objects.nonNull(lastPushTime) && (System.currentTimeMillis() - lastPushTime > expireSeconds * 1000L)) {
        // if (jiangShanWebSocketService.isEnable() && !jiangShanWebSocketService.isConnected()) {
        // // 不需要订阅，认证成功后即开始推送数据
        // jiangShanWebSocketService.connect();
        // }
        // } else if (jiangShanWebSocketService.isConnected()) {
        // jiangShanWebSocketService.disconnect();
        // }

        // 更新数据源的最后更新时间
        updateSourceUpdateTime();
    }

    /**
     * 获取系统所有可用的数据源
     *
     * @return 可用数据源列表
     */
    private Set<Integer> getEnabledSystemDatasource() {
        Set<Integer> result = new HashSet<>();
        if (dataSourceProperties.getPolygon().getEnable()) {
            result.add(CandleDataSource.POLYGON.getValue());
        }
        if (dataSourceProperties.getAlltick().getEnable()) {
            result.add(CandleDataSource.ALLTICK.getValue());
        }
        // 新增的数据源需要添加处理逻辑
        return result;
    }

    /**
     * 获取系统所有交易对的各自的可用数据源
     *
     * @param tradePairList         系统交易对列表
     * @param allTickProductCodeMap allTick支持的产品列表
     * @param polygonProductCodeMap polygon支持的产品列表
     * @return 交易对的可用数据源 格式：<交易对代码: 该交易对的所有可用数据源集合>
     */
    private Map<String, TreeSet<Integer>> getTradePairAvailableDatasourceMap(List<TradePairRespDTO> tradePairList,
                                                                             Map<String, String> allTickProductCodeMap, Map<String, String> polygonProductCodeMap) {
        Map<String, TreeSet<Integer>> result = new HashMap<>();
        // 系统启用的所有交易数据源
        Set<Integer> enabledSystemDatasource = getEnabledSystemDatasource();
        // 更新超时的交易对 <交易对代码：数据源>
        Map<String, Integer> outOfDateDataSourceMap = getOutOfDateTradePairCodeMap();
        for (TradePairRespDTO tradePairRespDTO : tradePairList) {
            // 支持该交易对的所有数据源
            TreeSet<Integer> datasourceForTradePair = datasourceProductSupported(tradePairRespDTO,
                    enabledSystemDatasource, allTickProductCodeMap, polygonProductCodeMap);
            if (datasourceForTradePair.size() <= 1) {
                result.put(tradePairRespDTO.getCode(), datasourceForTradePair);
            } else {
                // 如果超时的交易对中有当前交易对的数据源则从可用数据源中移除它
                if (outOfDateDataSourceMap.containsKey(tradePairRespDTO.getCode())) {
                    datasourceForTradePair.remove(outOfDateDataSourceMap.get(tradePairRespDTO.getCode()));
                } else {
                    result.put(tradePairRespDTO.getCode(), datasourceForTradePair);
                }
            }
        }
        return result;
    }

    /**
     * 获取数据源产品列表中支持该交易对的数据源
     *
     * @param tradePairRespDTO       交易对
     * @param systemAllDataSourceSet 系统所有数据源
     * @param allTickProductCodeMap  allTick产品列表
     * @param polygonProductCodeMap  polygon产品列表
     * @return 产品列表中有该交易对的数据源
     */
    private TreeSet<Integer> datasourceProductSupported(TradePairRespDTO tradePairRespDTO,
                                                        Set<Integer> systemAllDataSourceSet, Map<String, String> allTickProductCodeMap,
                                                        Map<String, String> polygonProductCodeMap) {
        TreeSet<Integer> result = new TreeSet<>();
        if (systemAllDataSourceSet.contains(CandleDataSource.ALLTICK.getValue())) {
            if (allTickProductCodeMap.containsKey(tradePairRespDTO.getCode())) {
                result.add(CandleDataSource.ALLTICK.getValue());
            }
        }
        if (systemAllDataSourceSet.contains(CandleDataSource.POLYGON.getValue())) {
            if (polygonProductCodeMap.containsKey(tradePairRespDTO.getCode())) {
                result.add(CandleDataSource.POLYGON.getValue());
            }
        }
        // 新增的数据源需要添加处理逻辑
        return result;
    }

    /**
     * 获取最后更新时间超时的交易对和数据 源 判断最后更新时间超过expireSeconds秒的交易对
     *
     * @return Map格式<交易对id ： 过期数据源>
     */
    private Map<String, Integer> getOutOfDateTradePairCodeMap() {
        // 获取consumer中记录的最后ticktime时间
        Map<String, UpdateDto> codeLastTickTimeMap = RocketCandleConsumer.getCodeLastTickTimeMap();
        if (CollectionUtil.isEmpty(codeLastTickTimeMap)) {
            return Collections.emptyMap();
        }
        // 更新时间超时的交易对 格式: <交易对id: 数据源>
        Map<String, Integer> outOfDateTradePairSet = new HashMap<>(codeLastTickTimeMap.size());
        long now = System.currentTimeMillis() / 1000;
        for (Map.Entry<String, UpdateDto> entry : codeLastTickTimeMap.entrySet()) {
            UpdateDto updateInfo = entry.getValue();
            Integer dataSource = updateInfo.getSource();
            long lastTickTime = updateInfo.getTickTime();
            long diff = now - lastTickTime;
            if (diff > expireSeconds) {
                outOfDateTradePairSet.put(entry.getKey(), dataSource);
            }
        }
        return outOfDateTradePairSet;
    }

    /**
     * 更新所有交易对的数据更新时间到redis中
     */
    private void updateSourceUpdateTime() {
        Map<String, UpdateDto> codeLastTickTimeMap = RocketCandleConsumer.getCodeLastTickTimeMap();
        Map<String, String> updateMap = new HashMap<>(codeLastTickTimeMap.size());
        for (Map.Entry<String, UpdateDto> entry : codeLastTickTimeMap.entrySet()) {
            UpdateDto value = entry.getValue();
            String updateInfo = value.getSource() + ":" + value.getTickTime();
            updateMap.put(entry.getKey(), updateInfo);
        }
        log.info("数据最后的更新拉取时间 {}", updateMap);
        candleDataSourceMonitorRedisDAO.setAll(updateMap);
    }
}
