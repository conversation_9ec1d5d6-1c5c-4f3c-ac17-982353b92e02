package com.rf.exchange.server.data.utils;

import org.springframework.beans.factory.annotation.Value;

import java.time.LocalTime;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 固定窗口限流器
 *
 * <AUTHOR>
 * @since 2024-07-25
 */
public class RateLimiterSimpleWindow {

    // 阈值，1秒内最多允许的请求数量
    private static Integer QPS;

    // 时间窗口（毫秒）
    private static final long TIME_WINDOWS = 1000;
    // 计数器
    private static final AtomicInteger REQ_COUNT = new AtomicInteger();

    private static long START_TIME = System.currentTimeMillis();

    public static void setupQPS(int qps) {
        QPS = qps;
    }

    public synchronized static boolean tryAcquire() {
        if ((System.currentTimeMillis() - START_TIME) > TIME_WINDOWS) {
            REQ_COUNT.set(0);
            START_TIME = System.currentTimeMillis();
        }
        return REQ_COUNT.incrementAndGet() <= QPS;
    }

    public static void main(String[] args) throws InterruptedException {
        for (int i = 0; i < 400; i++) {
            Thread.sleep(5);
            LocalTime now = LocalTime.now();
            if (!tryAcquire()) {
                System.out.println(now + " 被限流");
            } else {
                System.out.println(now + " 做点什么");
            }
        }
    }
}