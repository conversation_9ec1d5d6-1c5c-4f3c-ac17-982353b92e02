package com.rf.exchange.server.data.datasource.alltick.dto;

import lombok.Data;

import java.util.List;

/**
 * 盘口报价消息对象
 *
 * <AUTHOR>
 * @since 2024-07-03
 */
@Data
public class AllTickPKQuoteDTO {

    private String code;
    private String seq;
    private String tickTime;
    private List<OrderBook> bids;
    private List<OrderBook> asks;

    @Data
    public static class OrderBook {
        private String price;
        private String volume;
    }
}
