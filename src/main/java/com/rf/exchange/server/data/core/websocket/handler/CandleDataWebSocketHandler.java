package com.rf.exchange.server.data.core.websocket.handler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.rf.exchange.server.data.core.websocket.container.CandleWebSocketManagerContainer;
import com.rf.exchange.server.data.enums.CandleDataSource;
import jakarta.annotation.PreDestroy;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.io.IOException;

/**
 * <AUTHOR>
 * @since 2024-06-25
 */
@Slf4j
@Data
public class CandleDataWebSocketHandler extends TextWebSocketHandler {

    private static final ObjectMapper mapper = new ObjectMapper().setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);

    private CandleDataSource dataSource;
    private WebSocketSession session = null;

    @Resource
    private CandleWebSocketManagerContainer managerContainer;

    public CandleDataWebSocketHandler(CandleDataSource dataSource) {
        this.dataSource = dataSource;
    }

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        this.session = session;
        log.info("数据源:[{}] 建立websocket连接 SessionId:[{}]", dataSource.getName(), session.getId());
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        log.error("数据源:[{}] websocket发生传输错误 SessionId:[{}] error:[{}]", dataSource.getName(), session.getId(), exception.getMessage());
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        log.info("数据源:[{}] websocket连接关闭 SessionId:[{}] status: {}", dataSource.getName(), session.getId(), closeStatus);
        // 连接断开之后停止manager
        managerContainer.stopManager(dataSource);
    }

    @PreDestroy
    public void destroy() {
        if (session == null) {
            return;
        }
        try {
            session.close(CloseStatus.NORMAL);
        } catch (IOException e) {
            log.error("关闭 {} 的WebSocketSession失败", dataSource.getName());
        }
    }
}
