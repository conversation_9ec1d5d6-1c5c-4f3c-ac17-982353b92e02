//package com.rf.exchange.server.data.core.http.scheduler;
//
//import cn.hutool.core.date.LocalDateTimeUtil;
//import com.google.common.util.concurrent.RateLimiter;
//import com.rf.exchange.framework.xxljob.core.log.XJLog;
//import com.rf.exchange.module.candle.enums.CandleTimeRangeEnum;
//import com.rf.exchange.server.data.core.http.executor.CandleSyncGuardTaskExecutor;
//import com.rf.exchange.server.data.dto.DataSyncRequestDTO;
//import com.rf.exchange.server.data.dto.DataSyncTaskDTO;
//import jakarta.annotation.Resource;
//import org.springframework.stereotype.Component;
//
//import java.time.Duration;
//import java.time.LocalDateTime;
//import java.util.ArrayList;
//import java.util.Collection;
//import java.util.List;
//import java.util.concurrent.*;
//
//import static java.util.concurrent.TimeUnit.SECONDS;
//
///**
// * <AUTHOR>
// * @since 2024-07-22
// */
//@Component
//public class CandleSyncGuardTaskScheduler {
//
//    // k线请求的任务队列
//    private final ConcurrentLinkedQueue<DataSyncRequestDTO> requestQueue = new ConcurrentLinkedQueue<>();
//
//    @Resource
//    private CandleSyncGuardTaskExecutor taskExecutor;
//    @Resource(name = "allTickRateLimiter")
//    private RateLimiter allTickRateLimiter;
//
//    /**
//     * 添加单个任务到队列中
//     *
//     * @param task 单个任务
//     */
//    public void queueTask(DataSyncTaskDTO task) {
//        Collection<DataSyncRequestDTO> requests = createSyncRequest(task);
//        requestQueue.addAll(requests);
//    }
//
//    /**
//     * 队列是否空
//     * @return ture: 空
//     */
//    public boolean isQueueEmpty() {
//        return requestQueue.isEmpty();
//    }
//
//    /**
//     * 批量添加任务到队列中
//     *
//     * @param tasks 任务集合
//     */
//    public void queueTasks(Collection<DataSyncTaskDTO> tasks) {
//        Collection<DataSyncRequestDTO> requests = createSyncRequestWithTaskList(tasks);
//        requestQueue.addAll(requests);
//    }
//
//    /**
//     * 读取queue
//     * <p>
//     * 会阻塞当前执行线程
//     */
//    public void pollQueue() {
//        while (!isQueueEmpty()) {
//            final double wait = allTickRateLimiter.acquire();
//            if (wait > 0) {
//                long microsToWait = (long) (wait * SECONDS.toMicros(1L)) / SECONDS.toMillis(1L) + 1000L;
//                Duration duration = Duration.ofMillis(microsToWait);
//                try {
//                    // 强制睡眠10毫秒
//                    Thread.sleep(Duration.ofMillis(10));
//                    XJLog.info("线程睡眠 {}微妙", microsToWait);
//                } catch (InterruptedException e) {
//                    XJLog.error("线程睡眠异常 {}", e.getMessage());
//                }
//            }
//            DataSyncRequestDTO request = requestQueue.poll();
//            if (request != null) {
//                taskExecutor.executeRequest(request);
//            }
//        }
//    }
//
//    /**
//     * 创建同步数据的http请求任务
//     *
//     * @param task 同步任务
//     * @return 请求列表
//     */
//    private Collection<DataSyncRequestDTO> createSyncRequest(DataSyncTaskDTO task) {
//        List<DataSyncRequestDTO> requests = new ArrayList<>();
//        for (CandleTimeRangeEnum timeRange : CandleTimeRangeEnum.values()) {
//            // 如果不需要同步分钟级别的数据则跳过
//            if (!task.isNeedRequestMinKline() && timeRange.equals(CandleTimeRangeEnum.MIN_ONE)) {
//                continue;
//            }
//
//            // 截止时间，单位秒
//            long endTime = LocalDateTimeUtil.toEpochMilli(task.getEndTime()) / 1000;
//
//            Integer minBarCount = task.getBarCount();
//            //如果task中没有设置请求多少条k线则按照k线的时间级别获取请求k线的条数，最多一次拉取1000条k线
//            if (minBarCount == null) {
//                minBarCount = Math.min(timeRange.getCandleCountMax(), 1000);
//            }
//            requests.add(createRequest(task, timeRange, task.getStartTime(), endTime, minBarCount));
//
//            //如果是同步的历史数据则需要创建分钟级别k线的第二个请求，否则无法将一天的数据同步完整，因为一个请求最多拉取1000条数据
//            if (task.isHistorySync() && timeRange.equals(CandleTimeRangeEnum.MIN_ONE)) {
//                // 创建第二个1分钟线的请求时把endTime往前移动440分钟
//                endTime = endTime - 60000;
//                minBarCount = 440;
//                requests.add(createRequest(task, timeRange, task.getStartTime(), endTime, minBarCount));
//            }
//        }
//        return requests;
//    }
//
//    public Collection<DataSyncRequestDTO> createSyncRequestWithTaskList(Collection<DataSyncTaskDTO> taskList) {
//        List<DataSyncRequestDTO> requests = new ArrayList<>();
//        for (DataSyncTaskDTO task : taskList) {
//            Collection<DataSyncRequestDTO> syncRequest = createSyncRequest(task);
//            if (!syncRequest.isEmpty()) {
//                requests.addAll(syncRequest);
//            }
//        }
//        return requests;
//    }
//
//    /**
//     * 创建单个请求
//     *
//     * @param task      任务
//     * @param timeRange 时间范围枚举
//     * @param startTime 开始时间
//     * @param endTimeTS 截止时间
//     * @param barCount  k线条数
//     * @return 请求信息
//     */
//    private DataSyncRequestDTO createRequest(DataSyncTaskDTO task,
//                                             CandleTimeRangeEnum timeRange,
//                                             LocalDateTime startTime,
//                                             long endTimeTS,
//                                             int barCount) {
//        DataSyncRequestDTO minCandleRequest = new DataSyncRequestDTO();
//        minCandleRequest.setStock(task.isStock());
//        minCandleRequest.setDataSource(task.getDataSource());
//        minCandleRequest.setTradePairCode(task.getTradePairCode());
//        minCandleRequest.setTimeRangeEnum(timeRange);
//        minCandleRequest.setStartTime(startTime);
//        minCandleRequest.setEnd(endTimeTS);
//        minCandleRequest.setBarCount(barCount);
//        return minCandleRequest;
//    }
//}
