package com.rf.exchange.server.data.dal.dataobject;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fhs.core.trans.vo.TransPojo;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * k线数据统计表
 * 记录各个时间点拉取数据的状态
 *
 * <AUTHOR>
 * @since 2024-07-06
 */
@Data
@JsonIgnoreProperties(value = "transMap") // 由于 Easy-Trans 会添加 transMap 属性，避免 Jackson 在 Spring Cache 反序列化报错
@TableName("data_candle_last_sync")
public class CandleLastSyncDO implements Serializable, TransPojo {
    @Serial
    private final static long serialVersionUID = 1L;

    @TableId
    private Long id;
    /**
     * 交易对代码
     */
    private String code;
    /**
     * 最后同步的日期
     */
    private LocalDateTime lastSyncDate;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
