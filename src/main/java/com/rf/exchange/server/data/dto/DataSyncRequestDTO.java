package com.rf.exchange.server.data.dto;

import com.rf.exchange.module.candle.enums.CandleTimeRangeEnum;
import com.rf.exchange.module.exc.enums.TradeAssetTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-07-21
 */
@Data
public class DataSyncRequestDTO {
    /**
     * 数据源
     */
    private Integer dataSource;
    /**
     * 交易对代码
     */
    private String tradePairCode;
    /**
     * 基础code
     */
    private String baseCode;
    /**
     * 报价code
     */
    private String quoteCode;
    /**
     * 交易对类型
     * {@link TradeAssetTypeEnum}
     */
    private Integer tradeAssetType;
    /**
     * 是否是股票交易对
     */
    private boolean isStock;
    /**
     * k线的时间范围
     */
    private CandleTimeRangeEnum timeRangeEnum;
    /**
     * 批量请求的k线时间范围
     */
    private List<CandleTimeRangeEnum> timeRangeEnums;
    /**
     * 查询的k线数量
     */
    private Integer barCount;
    /**
     * 起始时间
     */
    private LocalDateTime startTime;
    /**
     * 开始时间戳
     */
    private Long startTS;
    /**
     * 结束时间戳
     */
    private Long endTS;
    /**
     * AllTick数据源的股票类型产品的复权类型
     */
    private Integer allTickStockAdjustType;
    /**
     * 是否是批量查询k线请求
     * true: 批量查询请求
     */
    private boolean isBatch;
    /**
     * 是否需要更新最后同步记录时间
     */
    private boolean isNeedUpdateLastSyncDate = true;
    /**
     * 是否需要同步分钟级别k线数据
     * true:创建分钟级别请求 false:不创建分钟级别数据的请求
     */
    private boolean isNeedRequestMinKline = false;

    /**
     * 是否是Polygon的实时k线请求
     */
    private boolean isPolygonRealTimeSync = false;

    /**
     * 是否是Polygon的历史k线请求
     */
    private boolean isPolygonHistorySync = false;
}
