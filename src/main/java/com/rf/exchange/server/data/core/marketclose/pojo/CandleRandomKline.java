package com.rf.exchange.server.data.core.marketclose.pojo;

import com.rf.exchange.module.candle.enums.CandleTimeRangeEnum;
import lombok.Data;

import java.math.BigDecimal;

/**
 * k线随机数据
 * <AUTHOR>
 * @since 2024-10-04
 */
@Data
public class CandleRandomKline {
    private String code;
    private BigDecimal openPrice;
    private BigDecimal closePrice;
    private BigDecimal highPrice;
    private BigDecimal lowPrice;
    private long timestamp;
    /**
     * 成交量
     */
    private long volume;
    /**
     * k线的时间范围级别
     */
    private CandleTimeRangeEnum timeRange;
}
