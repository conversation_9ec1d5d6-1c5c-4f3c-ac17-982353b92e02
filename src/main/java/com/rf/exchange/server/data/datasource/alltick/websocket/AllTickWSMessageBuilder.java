package com.rf.exchange.server.data.datasource.alltick.websocket;

import cn.hutool.core.map.MapUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.rf.exchange.server.data.datasource.alltick.util.AllTickUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

import static com.rf.exchange.server.data.datasource.alltick.util.AllTickUtil.getTraceString;

/**
 * <AUTHOR>
 * @since 2024-06-28
 */
@Slf4j
public class AllTickWSMessageBuilder implements MessageBuilder {

    private static final ObjectMapper mapper = new ObjectMapper().setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
    private static final AtomicInteger counter = new AtomicInteger(0);

    public AllTickWSMessageBuilder() {
        mapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
    }

    @Override
    public AllTickCandleIdMessage buildSubscribeMessage(Set<String> tradePairCodes, Integer cmdId, Map<String, Object> extraInfo) {
        List<Map<String, Object>> symbolList = new ArrayList<>();
        for (String code : tradePairCodes) {
            Map<String, Object> codeMap = MapUtil.newHashMap();

            // 转换交易对代码为产品code
            codeMap.put(AllTickKeyConstants.KEY_CODE, AllTickUtil.getProductCode(code));

            if (MapUtil.isNotEmpty(extraInfo)) {
                codeMap.putAll(extraInfo);
            }
            symbolList.add(codeMap);
        }

        return AllTickWSMessage.builder()
                .cmdId(cmdId)
                .seqId(uniqueID())
                .trace(getTraceString())
                .data(Map.of(AllTickKeyConstants.KEY_SYMBOL_LIST, symbolList))
                .tradePairCodes(tradePairCodes)
                .build();
    }

    @Override
    public AllTickCandleIdMessage buildUnsubscribeMessage(Set<String> tradePairCodes, Integer cmdId) {
        return AllTickWSMessage.builder()
                .cmdId(cmdId)
                .seqId(uniqueID())
                .trace(getTraceString())
                // cancel_type为0：取消所有报价订阅，1：取消盘口报价订阅，2：取消成交报价订阅，3：取消汇率订阅
                .data(Map.of(AllTickKeyConstants.KEY_CANCEL_TYPE, 0))
                .tradePairCodes(tradePairCodes)
                .build();
    }

    @Override
    public AllTickCandleIdMessage buildHeartbeatMessage() {
        return AllTickWSMessage.builder()
                .cmdId(AllTickCommand.HEART.getCmdCode())
                .seqId(uniqueID())
                .trace(getTraceString())
                .data(Collections.emptyMap()).build();
    }

    @Override
    public AllTickCandleIdMessage rebuildMessage(Object message) {
        if (message instanceof AllTickWSMessage oldTickMessage) {
            return AllTickWSMessage.builder()
                    .cmdId(oldTickMessage.getCmdId())
                    .seqId(uniqueID())
                    .trace(getTraceString())
                    .data(oldTickMessage.getData())
                    .tradePairCodes(oldTickMessage.getTradePairCodes())
                    .build();
        }
        return null;
    }

    private static long uniqueID() {
        int currentCounterValue = counter.incrementAndGet();
        long timestamp = System.currentTimeMillis() / 1000;
        return ((timestamp & 0xFFFFL) << 16) | (currentCounterValue & 0xFFFFL);
    }
}
