package com.rf.exchange.server.data.datasource.jiangshan.service;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

import org.apache.commons.compress.utils.Lists;

/**
 * @description: 匠山字典数据初始化
 * @author: Petter
 * @create: 2024-10-08
 **/
public class JiangShanCodeInit {

    public static void main(String[] args) {
        // 外汇
        List<String> forexList =
            List.of("AUDCAD", "AUDCHF", "AUDJPY", "AUDNZD", "AUDUSD", "CADCHF", "CADJPY", "CHFJPY", "EURAUD", "EURCAD",
                "EURCHF", "EURGBP", "EURJPY", "EURNZD", "EURSEK", "EURUSD", "GBPAUD", "GBPCAD", "GBPCHF", "GBPJPY",
                "GBPNZD", "GBPSEK", "GBPUSD", "NZDCAD", "NZDCHF", "NZDUSD", "USDCAD", "NZDJPY", "USDCHF", "USDDKK",
                "USDJPY", "USDNOK", "USDSEK", "USDSGD", "EURCZK", "EURHKD", "EURMXN", "EURPLN", "EURRUB", "EURTRY",
                "EURZAR", "USDCZK", "USDMXN", "USDPLN", "USDRUB", "USDTRY", "USDZAR", "USDCNH", "USDHKD");
        // 期货
        List<String> futuresList = List.of("OIL", "#C-COPPER", "#C-BRENT", "#C-HEATOIL", "#C-NATGAS", "#C-SUGAR",
            "#C-COCOA", "#C-COFFEE", "#C-SOYB", "#C-SOYBM", "#C-WHEAT", "#C-ORANGE", "#C-OATS", "#C-CORN", "#C-LCATTLE",
            "#C-FCATTLE", "#C-COTTON", "#C-LHOG", "#C-RICE", "#C-ROBUSTA");
        // 贵金属
        List<String> preciousMetalsList = List.of("XAGEUR", "XPTUSD", "XPDUSD", "XAUEUR", "XAUUSD", "XAGUSD");
        // 黄金
        List<String> goldList = List.of("XAUOIL", "XAUXAG");

        List<String> allList = Lists.newArrayList();
        allList.addAll(forexList);
        allList.addAll(futuresList);
        allList.addAll(preciousMetalsList);
        allList.addAll(goldList);
        AtomicInteger sort = new AtomicInteger(1);
        allList.forEach(item -> {
            System.out.println(
                "INSERT INTO `exchange-dev`.`system_dict_data` ( `sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES ( "
                    + sort + ", '" + item + "', '" + item
                    + "', 'code_list_jiang_shan', 0, '', '', '', '超级管理员', 1728320272476, '超级管理员', 1728320272476, b'0');");
            sort.getAndIncrement();
        });
    }
}
