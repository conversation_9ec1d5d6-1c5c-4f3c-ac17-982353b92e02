package com.rf.exchange.server.data.datasource.alltick.websocket;

import static com.rf.exchange.server.data.datasource.alltick.websocket.AllTickKeyConstants.*;

import java.util.Map;

import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.rf.exchange.server.data.core.mq.RocketCandleProducer;
import com.rf.exchange.server.data.dal.redis.CandleDataSourceMonitorRedisDAO;
import com.rf.exchange.server.data.enums.CandleDataSource;

import cn.hutool.core.util.StrUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2024-07-01
 */
@Slf4j
@Component
public class AllTickMessageProcessor {

    private static final ObjectMapper MAPPER =
        new ObjectMapper().setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);

    @Resource
    private AllTickMessageQueue messageQueue;

    @Resource
    private RocketCandleProducer mqProducer;
    @Resource
    private CandleDataSourceMonitorRedisDAO candleDataSourceMonitorRedisDAO;

    @Async("websocketProcessExecutor")
    public void process(String message) {
        if (StrUtil.isEmpty(message)) {
            log.error("AllTick的websocket收到空消息");
            return;
        }
        try {
            Map<String, Object> map = MAPPER.readValue(message, new TypeReference<>() {});
            if (map == null || map.isEmpty()) {
                return;
            }
            if (map.containsKey(KEY_RET)) {
                log.info("Recv AllTick消息 [响应]: {}", message);
                // 消息响应
                Integer cmdId = (Integer)map.get(KEY_CMD_ID);
                if (!AllTickCommand.HEART_RESP.getCmdCode().equals(cmdId)) {
                    messageQueue.processResponse((Integer)map.get(KEY_RET), String.valueOf(map.get(KEY_SEQ_ID)));
                }
            } else {
                log.debug("Recv AllTick消息 [推送]: {}", message);
                // 转发普通消息到MQ
                Integer cmdId = (Integer)map.get(KEY_CMD_ID);
                if (AllTickCommand.DEAL_PRICE_MSG.getCmdCode().equals(cmdId)) {
                    mqProducer.sendPriceMessage(CandleDataSource.ALLTICK.getValue(), message);
                } else if (AllTickCommand.PK_PRICE_MSG.getCmdCode().equals(cmdId)) {
                    mqProducer.sendOrderBookMessage(CandleDataSource.ALLTICK.getValue(), message);
                }
                candleDataSourceMonitorRedisDAO.updateLastPushTime(CandleDataSource.ALLTICK.getValue());
            }
        } catch (JsonProcessingException e) {
            log.error("AllTick 消息解析失败 json转对象 {}", e.getMessage());
        }
    }
}
