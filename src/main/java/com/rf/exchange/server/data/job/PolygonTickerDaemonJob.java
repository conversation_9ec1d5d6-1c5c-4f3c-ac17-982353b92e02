package com.rf.exchange.server.data.job;

import com.rf.exchange.module.exc.api.tradepair.TradePairApi;
import com.rf.exchange.server.data.datasource.polygon.http.PolygonService;
import jakarta.annotation.Resource;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024-10-08
 */
@Service
public class PolygonTickerDaemonJob {

    @Resource
    private PolygonService polygonService;
    @Resource
    private TradePairApi tradePairApi;

    /**
     * 拉取Polygon的forex的Ticker Type
     * 每10分钟获取一次
     */
    @Scheduled(cron = "0 10 * * * ?")
    public void fetchPolygonTickerType() {
        polygonService.fetchPolygonTickerTypeAll();
    }

    ///**
    // * 获取k线数据
    // */
    //@Scheduled(cron = "0 * * * * *")
    //public void fetchPolygonKline() {
    //    final List<TradePairRespDTO> allTradePair = tradePairApi.getTradePairEnableListCached();
    //    List<TradePairRespDTO> polygonTradePairs = new ArrayList<>();
    //    for (TradePairRespDTO tradePairRespDTO : allTradePair) {
    //        if (CandleDataSource.POLYGON.getValue() == tradePairRespDTO.getSource()) {
    //            polygonTradePairs.add(tradePairRespDTO);
    //        }
    //    }
    //    for (TradePairRespDTO tradePairRespDTO : polygonTradePairs) {
    //        polygonService.fetchPolygonForexKLine(tradePairRespDTO);
    //    }
    //}
}
