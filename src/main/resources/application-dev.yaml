debug: false

server:
  port: 48082

--- #################### 数据库相关配置 ####################

spring:
  # 数据源配置项
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.quartz.QuartzAutoConfiguration # 默认 local 环境，不开启 Quartz 的自动配置
      - de.codecentric.boot.admin.server.config.AdminServerAutoConfiguration # 禁用 Spring Boot Admin 的 Server 的自动配置
      - de.codecentric.boot.admin.server.ui.config.AdminServerUiAutoConfiguration # 禁用 Spring Boot Admin 的 Server UI 的自动配置
      - de.codecentric.boot.admin.client.config.SpringBootAdminClientAutoConfiguration # 禁用 Spring Boot Admin 的 Client 的自动配置
  datasource:
    druid: # Druid 【监控】相关的全局配置
      web-stat-filter:
        enabled: true
      stat-view-servlet:
        enabled: true
        allow: # 设置白名单，不填则允许所有访问
        url-pattern: /druid/*
        login-username: # 控制台管理用户名和密码
        login-password:
      filter:
        stat:
          enabled: true
          log-slow-sql: true # 慢 SQL 记录
          slow-sql-millis: 100
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
    dynamic: # 多数据源配置
      druid: # Druid 【连接池】相关的全局配置
        initial-size: 5 # 初始连接数
        min-idle: 10 # 最小连接池数量
        max-active: 20 # 最大连接池数量
        max-wait: 600000 # 配置获取连接等待超时的时间，单位：毫秒
        time-between-eviction-runs-millis: 60000 # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位：毫秒
        min-evictable-idle-time-millis: 300000 # 配置一个连接在池中最小生存的时间，单位：毫秒
        max-evictable-idle-time-millis: 900000 # 配置一个连接在池中最大生存的时间，单位：毫秒
        validation-query: SELECT 1 FROM DUAL # 配置检测连接是否有效
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
      primary: master
      datasource:
        master:
          url: ******************************************************************************************************************************************** # MySQL Connector/J 8.X 连接的示例
          username: exchange
          password: 'MhxcadY@1sdi^sd'
        slave: # 模拟从库，可根据自己需要修改 # 模拟从库，可根据自己需要修改
          lazy: true # 开启懒加载，保证启动速度
          url: ******************************************************************************************************************************************** # MySQL Connector/J 8.X 连接的示例
          username: exchange
          password: 'MhxcadY@1sdi^sd'

  # Redis 配置。Redisson 默认的配置足够使用，一般不需要进行调优
  data:
    redis:
      host: 127.0.0.1 # 地址
      port: 6379 # 端口
      database: 0 # 数据库索引
      password: 'MxhBsd@12&s*^d' # 密码，建议生产环境开启

--- #################### 接口文档相关配置 ####################

springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true

knife4j:
  enable: true

--- #################### xxl-job相关配置 ####################
xxl:
  job:
    enable: true
    access-token: 'xT4/idpxifGEqgZf11an28eDqVuXZqF2'
    admin:
      addresses: http://127.0.0.1:8989/xxl-job-admin
    executor:
      app-name: ${spring.application.name}
      address:
      port:
      ip:
      log-path: ${logging.file.path}/xxl-job/jobhandler

--- #################### 消息队列相关 ####################

# rocketmq 配置项，对应 RocketMQProperties 配置类
rocketmq:
  name-server: 127.0.0.1:9876 # RocketMQ Namesrv

--- #################### 服务保障相关配置 ####################

# Lock4j 配置项
lock4j:
  acquire-timeout: 3000 # 获取分布式锁超时时间，默认为 3000 毫秒
  expire: 30000 # 分布式锁的超时时间，默认为 30 毫秒

--- #################### 监控相关配置 ####################

# Actuator 监控端点的配置项
management:
  endpoints:
    web:
      base-path: /actuator # Actuator 提供的 API 接口的根目录。默认为 /actuator
      exposure:
        include: '*' # 需要开放的端点。默认值只打开 health 和 info 两个端点。通过设置 * ，可以开放所有端点。

# Spring Boot Admin 配置项
spring:
  boot:
    admin:
      # Spring Boot Admin Client 客户端的相关配置
      client:
        url: http://127.0.0.1:${server.port}/${spring.boot.admin.context-path} # 设置 Spring Boot Admin Server 地址
        instance:
          service-host-type: IP # 注册实例时，优先使用 IP [IP, HOST_NAME, CANONICAL_HOST_NAME]
      # Spring Boot Admin Server 服务端的相关配置
      context-path: /admin # 配置 Spring

# 日志文件配置
logging:
  config: classpath:logback-spring-plain.xml
  file:
    path: ${user.home}/logs # 日志文件目录
    name: ${logging.file.path}/${spring.application.name}.log # 日志文件名，全路径


--- #################### 交易所功能配置 ####################

# 交易所功能配置项，设置当前项目所有自定义的配置
exchange:
  xss:
    enable: false
    exclude-urls: # 如下两个 url，仅仅是为了演示，去掉配置也没关系
      - ${spring.boot.admin.context-path}/** # 不处理 Spring Boot Admin 的请求
      - ${management.endpoints.web.base-path}/** # 不处理 Actuator 的请求
  access-log: # 访问日志的配置项
    enable: false
  candle-datasource:
    enable: true
    alltick:
      enable: true
      host: 'https://quote.tradeswitcher.com'
      ws-host: 'wss://quote.tradeswitcher.com/quote-b-ws-api'
      secret: '3aa05480f89c3fba55c3faf2df353b5e-c-app'
      heart-sec: 10
      single-k-line: '/quote-b-api/kline'
      batch-k-line: '/quote-b-api/batch-kline'
    polygon:
      enable: false
      host: 'https://api.polygon.io'
      secret: 'DZfU3ExOw1f9ZmE3YakPK_M6PdtKjJAk'
    jiangShan:
      enable: false
      host: 'http://f-test.js-stock.top'
      ws-host: 'ws://f-test.js-stock.top:3001'
      secret: "1KcHQ8nPaicZeqIsnlOE"
  alltick-qps: 20 # AllTick请求限流器的QPS
