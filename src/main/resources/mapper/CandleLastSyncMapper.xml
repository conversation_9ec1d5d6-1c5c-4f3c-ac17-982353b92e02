<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rf.exchange.server.data.dal.mysql.CandleLastSyncMapper">

    <insert id="insertOrUpdateOnDuplicate">
        INSERT INTO data_candle_last_sync (code, last_sync_date)
        VALUES (#{updateObj.code}, #{updateObj.lastSyncDate})
        ON DUPLICATE KEY
            UPDATE last_sync_date = #{updateObj.lastSyncDate}
    </insert>
</mapper>