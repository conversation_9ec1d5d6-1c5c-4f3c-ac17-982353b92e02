{"id": "jp-virtual-list", "displayName": "虚拟列表 长列表优化 大数据列表高度不固定时展示，支持下拉刷新和回到顶部", "version": "2.0.1", "description": "当需要渲染大量的列表数据时，如果一次性将所有的数据渲染到DOM会导致页面崩溃。为了解决这个问题，可以使用虚拟滚动技术，只渲染可见区域内的数据，将未展示的数据暂不渲染", "keywords": ["虚拟滚动列表", "百万级数据展示", "只渲染可见区域内的数据", "虚拟列表", "长列表"], "engines": {"HBuilderX": "^3.1.0"}, "dcloudext": {"sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "插件不采集任何数据", "permissions": "无"}, "type": "component-vue"}, "uni_modules": {"encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"App": {"app-vue": "y", "app-nvue": "y"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "y", "百度": "y", "字节跳动": "y", "QQ": "y"}, "快应用": {"华为": "y", "联盟": "y"}, "Vue": {"vue2": "y", "vue3": "y"}}}}}