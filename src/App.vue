<script>
import { inject } from "vue";
import { strToParams } from "./common/utils";
import cache from "./common/cache";
import { SHARE_CODE } from "@/common/cacheEnums";
import { onMounted } from 'vue';
import config from "@/common/config";
import tui from "@/common/httpRequest.js";
import { i18n } from "@/locale";

// import { scheduleTokenCheck, resetTokenCheck } from '@/common/utils.js';

//缓存邀请码
const cacheInvite = (query) => {
  const code =
    query[SHARE_CODE] ||
    strToParams(decodeURIComponent(query["scene"]))[SHARE_CODE];
  if (code) {
    cache.set(SHARE_CODE, code, 60 * 60 * 24);
  }
};

export default {
  onLaunch: async function () {
    const websocketClient = inject("websocketClient");
    websocketClient.connect();

    // websocketClient.onMessage = handleMessage;
    // //发送消息出去
    // function handleMessage(data) {

    // }
    console.log("App Launch");

    // 初始化应用时调用TOKEN检查
    //  scheduleTokenCheck();

    uni.onTabBarMidButtonTap(() => {
      // 处理中间按钮点击事件，例如跳转到某个页面
      uni.navigateTo({
        url: "/pages/tabBar/transaction",
      });
    });
  },
  onShow: function (opinion) {
    cacheInvite(opinion?.query);
  },
  onHide: function () {
    console.log("App Hide");
  },
  setup() {
    

    // 设置文档标题
    function setDocumentTitle(title) {
      document.title = title;
    }
    
    // 设置Favicon
    function setFavicon(icoUrl) {
      if (!icoUrl) return;
      const link =
        document.querySelector("link[rel~='icon']") ||
        document.createElement("link");
      link.id = "linkicon";
      link.rel = "icon";
      link.href = icoUrl;
      if (!link.parentNode) {
        document.head.appendChild(link);
      }
    }
    
    
    // 全局数据加载
    async function fetchGlobalData() {
      const data = uni.getStorageSync('config');
      if (data) {
        config.assetType = data.assetType;
        config.country = data.country;
        config.currency = data.currency;
        config.language = data.language;
        config.exchangeRates = data.exchangeRates;
        config.defaultTradePair = data.defaultTradePair;
        config.tenantName = data.tenantName;
        config.tenantLogo = data.tenantLogo;
        // config.currencyUnit = data.currencyUnit;
        config.currencyUnit = uni.getStorageSync('currencyUnit');
      }
      try {
        const configApi = await tui.request("/app-api/system/config/list", "GET");
        config.assetType = configApi.data.assetType.map((i) => {
          return {
            ...i,
            value: i.type,
            name: i.name,
          };
        });
        config.country = configApi.data.country;
        config.currency = configApi.data.currency;
        config.language = configApi.data.language;
        config.exchangeRates = configApi.data.exchangeRates;
        config.defaultTradePair = configApi.data.defaultTradePair;
        config.tenantName = configApi.data.tenantName;
        config.tenantLogo = configApi.data.tenantLogo;

        const currencyUnit =  uni.getStorageSync('currencyUnit');
        if(configApi.data.currency.length>0 && !currencyUnit){
          const langInfo =  configApi.data.currency.filter(i=> i.isDefault==true)
          if(langInfo.length>0){
             config.currencyUnit = langInfo[0].code;
             uni.setStorageSync('currencyUnit', config.currencyUnit)
          }
        }
        if(configApi.data.language.length>0){
          const langInfo =  configApi.data.language.filter(i=> i.isDefault==true)
          if(langInfo.length>0){
            const  locale = langInfo[0].code;
            config.defaultLanguage = locale
            const defaultLanguage = uni.getStorageSync('defaultLanguage') || ''
            if(defaultLanguage){
              
            }else{
                uni.setStorageSync('defaultLanguage', locale)
                uni.setLocale(locale);
                i18n.locale = locale;
                i18n.global.locale = locale;
    
          }
        }
      }
    
        uni.setStorageSync('tenantName', config.tenantName)
        uni.setStorageSync('tenantLogo', config.tenantLogo)
        setFavicon(config.tenantLogo)
        setDocumentTitle(config.tenantName)
    
        console.log("平台配置:", config);
        uni.setStorageSync("config", config);
        // setDocumentTitle(config.name);
        // setFavicon(config.ico);
        // 在这里可以设置其他配置...
      } catch (error) {
        console.error("获取平台配置失败:", error);
      }
    }
    
    
        onMounted(async () => {
           await fetchGlobalData()
        });
      }
};
</script>

<style lang="scss">
/*每个页面公共css */
.uni-tabbar__mid {
  top: -26rpx;
}
/* modal弹框全局样式 */
.uni-modal {
  border-radius: 16rpx;
  padding-left: 30rpx;
  padding-right: 30rpx;
}
.uni-modal__btn_default {
  overflow: hidden;
  text-align: center;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-family: "PingFang SC";
  font-size: 32rpx;
  font-style: normal;
  font-weight: bolder;
  height: 88rpx;
  line-height: 140%; /* 44.8rpx */

  display: flex;
  justify-content: center;
  align-items: center;
}
.uni-modal__btn_primary {
  overflow: hidden;
  text-align: center;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-family: "PingFang SC";
  font-size: 32rpx;
  font-style: normal;
  font-weight: bolder;
  height: 88rpx;
  line-height: 140%; /* 44.8rpx */

  display: flex;
  justify-content: center;
  align-items: center;

  border-radius: 2000rpx;
  background: rgba(50, 72, 244, 1);

  padding: 16rpx 24rpx 16rpx 24rpx;

  font-size: 28rpx !important;
  font-weight: 600;
  letter-spacing: 0rpx;
  line-height: 48rpx;
  color: rgba(255, 255, 255, 1) !important;
  box-sizing: border-box;
  max-width: 80%;
  margin: auto;
}
.uni-modal__ft:after{
  content: none !important;
}
.uni-modal__ft{
  margin-bottom: 64rpx;
  column-gap: 32rpx;

}
.uni-modal__btn_default{
border-radius: 20000rpx;
background: rgba(239, 239, 239, 1);
display: flex;
justify-content: center;
align-items: center;
padding: 16rpx 24rpx 16rpx 24rpx;


font-size: 28rpx;
font-weight: 600;
letter-spacing: 0rpx;
line-height: 40.6rpx;
color: rgba(151, 151, 151, 1) !important;
text-align: center;
vertical-align: top;


}
.uni-modal__title {
  color: #272729;
  text-align: center;
  font-family: "PingFang SC";
  font-size: 32rpx;
  font-style: normal;
  font-weight: bolder;
  line-height: normal;
}
uni-button:after {
  border: none !important;
}
</style>
