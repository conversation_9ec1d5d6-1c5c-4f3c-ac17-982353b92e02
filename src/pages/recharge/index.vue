<template>
  <view class="recharge-container">
    <view class="title-class">{{$t("银行卡入款")}}</view>
    <view class="item-class">
      <view class="label-class">{{ $t("国家/地区") }}</view>
      <InputSelect :placeholder="$t('请选择国家/地区')"
                   :list="country"
                   :popupTitle="$t('国家/地区')"
                   keyLable="code"
                   keyValue="areaId"
                   :value="form.areaId"
                   @change="countryChange"
                   v-model:value="form.areaId"
      ></InputSelect>
    </view>
    <view class="item-class">
      <view class="label-class">{{ $t("币种") }}</view>
      <InputSelect
          :placeholder="$t('请选择币种')"
          :popupTitle="$t('币种')"
          keyLable="quoteCurrency"
          keyValue="quoteCurrency"
          :list="config.exchangeRates"
          :value="form.currencyCode"
          v-model:value="form.currencyCode"
          @change="currentChange"
      >
      </InputSelect>
    </view>
    <view class="item-class">
      <view class="label-class">{{ $t("入款金额") }}</view>
      <uni-easyinput v-model="form.amount" trim="all" @input="inputChange"    :placeholder="$t('请输入金额')"  type="number"/>
      <view class="label-class money">{{`=${currenyRatePrice}$`}}</view>
    </view>

    <view class="desc-wrapper-class">
      {{$t("为了确保您的资金安全，请在汇款前与在线客服确认您所选的银行卡是否正常。收到您的汇款后，我们会根据您汇出的法定货币将其转换为美元。汇款完成后请提交汇款凭证并联系我们的客服人员。")}}
    </view>

    <view class="submit" @click="handleSumbit">
      {{ $t("提交") }}
    </view>
  </view>

</template>

<script setup>
import config from "@/common/config";
import { useFormatMoney } from "@/common/utils";
import { t } from "@/hooks/useI18n";
import { ref,reactive, computed } from "vue";
import {onLoad} from "@dcloudio/uni-app";
import tui from "@/common/httpRequest";
import BigNumber from 'bignumber.js';

const form= ref({
  "areaId": '', // 国家地区
  "currencyCode": "", //币种
  "payMethod": 1,//入款方式
  "amount": '' //金额
})

const currenyRate=ref(0)
// const currenyRatePrice=ref(0)

 onLoad(async ()=>{
  getCountry()
})

const country = ref([]);
function getCountry(){
  tui.request("/app-api/system/config/list-country",'GET').then(res=>{
    country.value = res.data
  })
}

// async function  getRatePrice(){
//   try {
//     // currenyRatePrice.value=Number(form.value.amount )* currenyRate.value
//     currenyRatePrice.value =  new BigNumber(form.value.amount||0).div(currenyRate.value||1).toFixed(2);;
//   }catch (e) {
//     currenyRatePrice.value=0
//   }
// }

const currenyRatePrice = computed(()=>{
   return  new BigNumber(form.value.amount||0).div(currenyRate.value||1).toFixed(2) || 0;
})


//币种
function currentChange(currency){
  try {
   let exchangeRates = config.exchangeRates.find(item=>item.quoteCurrency===currency)
    if(exchangeRates){
      currenyRate.value =Number(exchangeRates.realRate)
    }
    // getRatePrice();
  }catch (e) {
    currenyRate.value = 0
  }

}

function countryChange(value,item){
  form.value.currencyCode = item.currency
  currentChange(item.currency)
}
function inputChange(e){
  // setTimeout(()=>{
  //   getRatePrice();
  // },100)

}
//提交
async function handleSumbit(){
  if(!form.value.areaId){
    tui.toast(t("请选择国家/地区"))
    return
  }
  if(!form.value.currencyCode){
    tui.toast(t("请选择币种"))
    return
  }
  if(!form.value.amount){
    tui.toast(t("请输入有效金额"))
    return
  }
   await tui.request("/app-api/member/funds-records/create-recharge",'POST',form.value)
   tui.toast(t('申请成功'))
  setTimeout(()=>{
    uni.navigateBack({delta:1})
  },1000)

}





const coin = ref("1");
</script>

<style scoped  lang="scss">
  .recharge-container{
    padding: 20rpx 31rpx;
    display: flex;
    flex-direction: column;
    row-gap: 32rpx;
    box-sizing: border-box;
    .title-class{
      color: #000;
      font-family: "PingFang SC";
      font-size: 36rpx;
      font-style: normal;
      font-weight: bold;
      padding-top: 32rpx;
      padding-bottom: 32rpx;
    }
    .submit {
      width: 690rpx;
      height: 96rpx;
      flex-shrink: 0;
      border-radius: 20000rpx;
      background: #3248f4;
      color: #fff;
      text-align: center;
      font-family: "Noto Sans";
      font-size: 32rpx;
      font-style: normal;
      font-weight: 600;
      line-height: 145%; /* 46.4rpx */
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 50rpx;
    }
    .item-class {
      .label-class {
        margin-bottom: 20rpx;
        color: #999;
        font-family: "Noto Sans";
        font-size: 24rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 42rpx; /* 175% */
        letter-spacing: -0.64rpx;
      }
      .money{
        margin-top: 3rpx;
        letter-spacing: 3rpx;
      }
    }
    .desc-wrapper-class{
      display: flex;
      padding: 20rpx;
      justify-content: center;
      align-items: center;
      margin-top: 48rpx;
      border-radius: 10px;
      background: rgba(255, 153, 0, 0.04);
      color: #F90;
      font-size: 24rpx;
      font-style: normal;
      line-height: 44rpx; /* 183.333% */
      letter-spacing: -0.64rpx;
    }
  }

</style>