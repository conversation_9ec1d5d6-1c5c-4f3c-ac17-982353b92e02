
<template>
  <view class="add-bank-card">
    <view class="item-class">
      <view class="label-class">{{ $t("国家/地区") }}</view>

      <InputSelect :placeholder="$t('请选择国家/地区')"
                   :list="country"
                   :popupTitle="$t('国家/地区')"
                   keyLable="code"
                   keyValue="areaId"
                   @change="changeCountry"
                   :value="form.areaId"
                   v-model:value="form.areaId"
      ></InputSelect>
    </view>

    <view class="item-class">
      <view class="label-class">{{ $t("法币") }}</view>

      <InputSelect
        :placeholder="$t('请选择法币')"
        :popupTitle="$t('法币')"
        keyLable="code"
        keyValue="code"
        :list="config.currency"
        :value="form.currencyCode"
        v-model:value="form.currencyCode"
      ></InputSelect>
    </view>

    <view class="item-class">
      <view class="label-class">{{ $t("账户名称") }}</view>
      <uni-easyinput v-model="form.name" trim="none" :placeholder="$t('请输入账户名称')" />
    </view>
    <view class="item-class">
      <view class="label-class">{{ $t("银行名称") }}</view>
      <uni-easyinput v-model="form.typeName" trim="none" :placeholder="$t('请输入银行名称')" />
    </view>

    <view class="item-class">
      <view class="label-class">{{ $t("银行卡号") }}</view>
      <uni-easyinput v-model="form.account" trim="none" :placeholder="$t('请输入银行卡号')" />
    </view>

    <view class="item-class">
      <view class="label-class">{{ $t("银行地址") }}</view>
      <uni-easyinput  v-model="form.bankAddress" trim="none" :placeholder="$t('请输入银行地址')" />
    </view>
    <view class="item-class">
      <view class="label-class">{{ $t("分行号") }}</view>
      <uni-easyinput  v-model="form.bankBranch" trim="none" :placeholder="$t('请输入分行号')" />
    </view>


    <view class="delBtn" v-if="form.id" @click="handleUnBind()">
      {{ $t("解绑") }}
    </view>
    <view class="submit" @click="handleSumbit">
      {{ $t(form.id?'修改':"提交") }}
    </view>
  </view>
</template>
<script setup>
import config from "@/common/config";
import { t } from "@/hooks/useI18n";
import { ref,reactive } from "vue";
import {onLoad} from "@dcloudio/uni-app";
import tui from "@/common/httpRequest";

const defaultForm = {
  walletType: 1,
  name: "",
  account: "",
  typeName: "",
  bankAddress: "",
  bankBranch: "",
  areaId: '',
  currencyCode: ''
};



const form= ref({...defaultForm})

 onLoad(async ({id})=>{
  getCountry()
  const bankInfo =  uni.getStorageSync('bankInfo');
  if(bankInfo.account){
    uni.setNavigationBarTitle({title:t("修改银行卡")})
    form.value = bankInfo
  }
  // if(id){
  //   uni.setNavigationBarTitle({title:t("修改银行卡")})
  //   let {data} =  await tui.request('/app-api/member/wallet/get','GET',{id:id})
  //   form.value =data
  //   console.log("form",form)
  // }
})

function changeCountry(value,item){
   form.value.currencyCode = item.currency
}

const country = ref([]);
function getCountry(){
  tui.request("/app-api/system/config/list-country",'GET').then(res=>{
    country.value = res.data
  })
}
const isLoading = ref(false);

async function handleSumbit(){
  console.log("form",form.value)
  if(!form.value.areaId){
    tui.toast(t('请选择国家/地区'))
    return
  }
  if(!form.value.currencyCode){
    tui.toast(t('请选择法币'))
    return
  }
  if(!form.value.name){
    tui.toast(t('请输入账户名称'))
    return
  }
  if(!form.value.typeName){
    tui.toast(t('请输入银行名称'))
    return
  }
  if(!form.value.account){
    tui.toast(t('请输入银行卡号'))
    return
  }
  if(!form.value.bankAddress){
    tui.toast(t('请输入银行地址'))
    return
  }
  // if(!form.value.bankBranch){
  //   tui.toast(t('请输入分行号'))
  //   return
  // }

  if(isLoading.value) return
  isLoading.value = true
  //提交数据上一页
  uni.navigateBack({delta:1})
  uni.$emit('updateBankCard',form.value)
  
  
  // tui.request('/app-api/member/wallet/save','POST',form.value).then(res=>{
  //   if(res.code == 0){
  //     tui.toast(t('提交成功'))
  //     form.value = {...defaultForm}
  //     setTimeout(()=>{
  //       uni.navigateBack({delta:1})
  //     },1000)
  //   }
  // }).finally(() =>{
      isLoading.value = false;
  //  });

}

async function handleUnBind(){
  uni.showModal({
    title: t("提示"),
    content: t("是否确认解绑"),
    confirmText: t("确认"),
    async success(res){
        if(res.confirm){
          await tui.request('/app-api/member/wallet/remove','POST',form.value,false,true)
          tui.toast(t('解绑成功'))
          setTimeout(()=>{
            uni.navigateBack({delta:1})
          },1000)
        }
    }
  });

}

</script>

<style scoped lang="scss">
.add-bank-card {
  padding: 20rpx 31rpx;
  display: flex;
  flex-direction: column;
  row-gap: 32rpx;
  .submit {
    width: 690rpx;
    height: 96rpx;
    flex-shrink: 0;
    border-radius: 20000rpx;
    background: #3248f4;
    color: #fff;
    text-align: center;
    font-family: "Noto Sans";
    font-size: 32rpx;
    font-style: normal;
    font-weight: 600;
    line-height: 145%; /* 46.4rpx */
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
    margin-bottom: 100rpx;
  }
  .delBtn{
    width: 690rpx;
    height: 96rpx;
    flex-shrink: 0;
    border-radius: 20000rpx;
    background: #999999;
    color: #fff;
    text-align: center;
    font-family: "Noto Sans";
    font-size: 32rpx;
    font-style: normal;
    font-weight: 600;
    line-height: 145%; /* 46.4rpx */
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 50rpx;
  }
  .item-class {
    .label-class {
      margin-bottom: 20rpx;
      color: #999;
      font-family: "Noto Sans";
      font-size: 24rpx;
      font-style: normal;
      font-weight: 400;
      line-height: 42rpx; /* 175% */
      letter-spacing: -0.64rpx;
    }
  }
}
</style>