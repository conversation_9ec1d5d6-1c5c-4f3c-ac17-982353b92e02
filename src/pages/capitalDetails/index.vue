<template>
	<view class="capital-details">
		<z-paging ref="paging" v-model="dataList" @query="queryList">
			<template #top>
				<z-tabs :list="tabList" @change="tabChange" bar-height="0" class="tabs-contract-line-class" />
			</template>
			<view class="item" v-for="(item, index) in dataList" :key="index">
				<view class="row-class">
					<view class="lable-class">{{ $t("数量") }}</view>
					<view v-if="item.opType == 1" class="value-class">
						<view>+{{ useFormatMoney(item.usdtAmount) }}USD </view>
						<view>≈ +{{ useFormatMoney(item.currencyAmount) }}{{ item.currencyCode }}</view>
					</view>
					<view v-else-if="item.opType == 2" class="value-class-red">
						<view>-{{ useFormatMoney(item.usdtAmount) }}USD </view>
						<view>≈ -{{ useFormatMoney(item.currencyAmount) }}{{ item.currencyCode }}</view>
					</view>
					<view v-else class="value-class">
						<view>+{{ useFormatMoney(item.usdtAmount) }}USD </view>
						<view>≈ +{{ useFormatMoney(item.currencyAmount) }}{{ item.currencyCode }}</view>
					</view>
				</view>
				<view class="row-class" v-if="item.walletAccount">
					<view class="lable-class">{{ $t("银行卡号") }}</view>
					<view class="time-class">
						<view>{{ item.walletAccount }} </view>
					</view>
				</view>
				<view class="row-class">
					<view class="lable-class">{{ $t("时间") }}</view>
					<view class="time-class">
						<view> {{ useFormatTime(item.createTime) }}</view>
						<view> {{ item.orderMemberStatusStr }}</view>
					</view>
				</view>
			</view>
		</z-paging>
	</view>
</template>
<script setup>
	import { computed, ref } from "vue";
	import tui from "@/common/httpRequest.js";
	import { t } from "@/hooks/useI18n";
	import config from "@/common/config.js";
	import { useFormatMoney, useFormatTime } from "@/common/utils";
	// v-model绑定的这个变量不要在分页请求结束中自己赋值！！！
	const tabList = computed(() => {
		return [
			{ value: 0, name: t("全部") },
			{ value: 1, name: t("入款记录") },
			{ value: 2, name: t("取款记录") },
		];
	});
	const tabIndex = ref(0);
	const paging = ref(null);
	const dataList = ref([]);
	function tabChange(index) {
		tabIndex.value = index;
		// 当切换tab或搜索时请调用组件的reload方法，请勿直接调用：queryList方法！！
		paging.value.reload();
	}
	function queryList(pageNo, pageSize) {
		// 组件加载时会自动触发此方法，因此默认页面加载时会自动触发，无需手动调用
		// 这里的pageNo和pageSize会自动计算好，直接传给服务器即可
		// 模拟请求服务器获取分页数据，请替换成自己的网络请求
		const params = {
			pageNo: pageNo,
			pageSize: pageSize,
			opType: tabList.value[tabIndex.value].value,
		};
		tui.request("/app-api/member/funds-records/page", "GET", params).then((res) => {
			paging.value.complete(res.data.list);
		});
		// paging.value.complete([1, 1, 1, 1]);

		// tui.request.queryList(params).then(res => {
		// 	// 将请求的结果数组传递给z-paging
		// 	paging.value.complete(res.data.list);
		// }).catch(res => {
		// 	// 如果请求失败写this.$refs.paging.complete(false);
		// 	// 注意，每次都需要在catch中写这句话很麻烦，z-paging提供了方案可以全局统一处理
		// 	// 在底层的网络请求抛出异常时，写uni.$emit('z-paging-error-emit');即可
		// 	paging.value.complete(false);
		// })
	}
</script>
<style scoped lang="scss">
	.capital-details {
		.item {
			padding: 30rpx;
			border-bottom: 1rpx solid #f7f7f7;
			display: flex;
			flex-direction: column;
			row-gap: 16rpx;
			.row-class {
				display: flex;
				justify-content: space-between;
				align-items: flex-start;
				.lable-class {
					color: #999;
					font-family: "Noto Sans";
					font-size: 26rpx;
					font-style: normal;
					font-weight: bold;
					line-height: 145%; /* 37.7rpx */
				}
				.value-class {
					color: #05c06f;
					text-align: right;
					font-family: "PingFang SC";
					font-size: 26rpx;
					font-style: normal;
					font-weight: bold;
					line-height: 145%; /* 37.7rpx */

					display: flex;
					justify-content: center;
					align-items: flex-end;
					flex-direction: column;
					row-gap: 20rpx;
				}
				.value-class-red {
					color: #d72828;
					text-align: right;
					font-family: "PingFang SC";
					font-size: 26rpx;
					font-style: normal;
					font-weight: bold;
					line-height: 145%; /* 37.7rpx */

					display: flex;
					justify-content: center;
					align-items: flex-end;
					flex-direction: column;
					row-gap: 20rpx;
				}
				.time-class {
					color: #000;
					text-align: right;
					font-family: "PingFang SC";
					font-size: 26rpx;
					font-style: normal;
					font-weight: bold;
					line-height: 145%; /* 37.7rpx */
					display: flex;
					justify-content: center;
					align-items: flex-end;
					flex-direction: column;
					row-gap: 20rpx;
				}
			}
		}
		.tabs-contract-line-class {
			box-sizing: border-box;
			padding: 0 24rpx;
			// border-bottom: 2rpx solid #f9f9f9;
			::v-deep .z-tabs-list-container {
				justify-content: flex-start;
				.z-tabs-list {
					flex: 0 !important;
				}
				.z-tabs-item-title {
					color: #000;
					text-align: center;
					font-family: "PingFang SC";
					font-size: 36rpx;
					font-style: normal;
					font-weight: 600;
					line-height: 32rpx; /* 88.889% */
				}
			}
		}
	}
</style>
