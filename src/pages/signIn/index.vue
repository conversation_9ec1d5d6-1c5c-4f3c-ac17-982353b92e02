<template>
  <view class="sign-in-class">
    <uni-nav-bar  :border="false">
      <template #left>
        <image
          src="@/static/images/index/16.png"
          class="return-icon-class"
          @click="goHome"
        ></image>
      </template>
    </uni-nav-bar>
    <view class="page-title-class">
      <view class="title-class">{{ $t("登录账号") }}</view>
      <view class="language-class" @click="navLanguageset">
        <image
          src="@/static/images/index/17.png"
          class="language-icon-class"
        ></image>
        <span>{{ $t("语言") }}</span>
      </view>
    </view>

    <view class="login-information-class">
      <view class="item-class">
        <view class="label-class">{{ $t("用户名") }}</view>
        <view class="value-class">
          <uni-easyinput v-model="form.account" trim="all" :placeholder="$t('请输入用户名')" />
        </view>
      </view>

      <view class="item-class">
        <view class="label-class">{{ $t("密码") }}</view>
        <view class="value-class">
          <uni-easyinput v-model="form.password" type="password" trim="all" :placeholder="$t('请输入密码')" />
        </view>
      </view>
    </view>

    <view class="forgot-password">
      <!-- {{ $t("") }} -->
    </view>

    <view class="sign-in" @click="handleSumbit">{{ $t("登录") }}</view>
    <view class="no-account-go-register" @click="navRegistration">{{ $t("没有账号？去注册") }}</view>
  </view>
</template>
<script setup>
import { t } from "@/hooks/useI18n";
import {ref,reactive,inject} from "vue";
import tui from "@/common/httpRequest";
import {onLoad} from "@dcloudio/uni-app";

const websocketClient = inject("websocketClient");

const form =reactive({
  account:'',
  password:''
})


function goHome() {
  uni.switchTab({
    url: "/pages/tabBar/index",
  });
}
function navLanguageset() {
  uni.navigateTo({
    url: "/pages/languageset/index",
  });
}
function navRegistration(){
    uni.navigateTo({
    url: "/pages/register/index",
  });
}

//登陆
const handleSumbit = async()=>{
  try {
    if(!form.account){
      tui.toast(t("请输入账号"))
      return;
    }
    if(!form.password){
      tui.toast(t("请输入密码"))
      return;
    }
    const {data} = await tui.request("/app-api/member/auth/login",'POST',form)
    // uni.setStorageSync("Token",data.accessToken)
    tui.setToken(data.accessToken);
    const login = {
        type: "cmd",
        cmd: 999,
        data:{
          token:data.accessToken
        }
      };
    websocketClient.sendMessage(JSON.stringify(login));
    tui.toast(t('登录成功'),1500,true)
    uni.setStorageSync("account",form.account)
    setTimeout(()=>{
      uni.reLaunch({
        url: "/pages/tabBar/index"
      })
    },1000)
  }catch (e) {

  }

}
onLoad(()=>{
  form.account=uni.getStorageSync("account")||''
})
</script>

<style scoped lang="scss">
.sign-in-class {
  .sign-in {
    color: #fff;
    text-align: center;
    font-family: "PingFang SC";
    font-size: 32rpx;
    font-style: normal;
    font-weight: bolder;
    line-height: 145%; /* 46.4rpx */
    border-radius: 20000rpx;
    background: #3248f4;
    width: 690rpx;
    height: 96rpx;
    flex-shrink: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
  }
  .no-account-go-register {
    color: #2752e7;
    text-align: center;
    font-family: "PingFang SC";
    font-size: 28rpx;
    font-style: normal;
    font-weight: 400;
    line-height: 32rpx; /* 114.286% */
    margin-top: 48rpx;
  }
  .forgot-password {
    color: #2752e7;
    text-align: center;
    font-family: "PingFang SC";
    font-size: 28rpx;
    font-style: normal;
    font-weight: 400;
    line-height: 145%; /* 40.6rpx */
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 30rpx;
    padding-top: 32rpx;
    margin-bottom: 112rpx;
  }
  .login-information-class {
    display: flex;
    flex-direction: column;
    row-gap: 32rpx;

    .item-class {
      box-sizing: border-box;
      padding: 0 30rpx;
      .label-class {
        color: #999;
        font-family: "Noto Sans";
        font-size: 24rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 42rpx; /* 175% */
        letter-spacing: -0.64rpx;
        margin-bottom: 10rpx;
      }
    }
  }
  .page-title-class {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx 30rpx;
    margin-bottom: 96rpx;
    .language-class {
      color: #000;
      font-family: "PingFang SC";
      font-size: 28rpx;
      font-style: normal;
      font-weight: bold;
      line-height: 145%; /* 40.6rpx */
      letter-spacing: -0.65rpx;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      column-gap: 8rpx;
      .language-icon-class {
        width: 32rpx;
        height: 32rpx;
      }
    }
    .title-class {
      color: #111;
      font-family: "PingFang SC";
      font-size: 44rpx;
      font-style: normal;
      font-weight: 500;
      line-height: 145%; /* 63.8rpx */
      letter-spacing: -2rpx;
    }
  }
  .return-icon-class {
    width: 48rpx;
    height: 48rpx;
    flex-shrink: 0;
  }
}
</style>