<template>
  <view class="authenticate-identity-class">
    <view class="item-class">
      <view class="label-class">{{ $t("国家/地区") }}</view>
      <!-- <uni-easyinput
        trim="all"
        @input-click="navRouting($event, '')"
        suffixIcon="down"
        :placeholder="$t('请选择国家/地区')"
      /> -->
      <InputSelect :placeholder="$t('请选择国家/地区')"
                   :list="country"
                   :popupTitle="$t('国家/地区')"
                   keyLable="code"
                   keyValue="areaId"
                   :value="form.areaId"
                   v-model:value="form.areaId"
                   ></InputSelect>
    </view>

    <view class="item-class">
      <view class="label-class">{{ $t("证件类型") }}</view>
      <InputSelect v-model:value="form.credentialsType"
                   :placeholder="$t('请选择证件类型')"
                   :popupTitle="$t('证件类型')"
                   keyLable="name"
                   keyValue="id"
                   :list="cardList" ></InputSelect>
      <!-- <uni-easyinput
        trim="all"
        suffixIcon="down"
        :placeholder="$t('请选择证件类型')"
      /> -->
    </view>

    <view class="item-class">
      <view class="label-class">{{ $t("证件号码") }}</view>
      <uni-easyinput  v-model="form.credentialsCode" trim="none" :placeholder="$t('请输入证件号码')" />
    </view>

    <view class="item-class">
      <view class="label-class">{{ $t("姓名") }}</view>
      <uni-easyinput  v-model="form.realName"  trim="none" :placeholder="$t('请输入姓名')" />
    </view>

    <view class="item-class">
      <view class="label-class">{{ $t("请上传证件照片") }}</view>
      <uni-file-picker
        limit="1"
        :imageStyles="imageStyles"
        v-model="formImage.credentialsFront"
        @select="uploadCard($event,'credentialsFront')"
        :placeholder="$t('上传证件正面')"
      ></uni-file-picker>
      <uni-file-picker
          limit="1"
          fileMediatype="image"
          v-model="formImage.credentialsBack"
          @select="uploadCard($event,'credentialsBack')"
          :imageStyles="imageStyles"
          :placeholder="$t('上传证件反面')"
          class="gap"
      ></uni-file-picker>
    </view>


    <view class="submit" @click="handleSub()">
      {{ $t("提交") }}
    </view>
  </view>
</template>
<script setup>
import config from "@/common/config";
import { t } from "@/hooks/useI18n";
import { ref,reactive, computed } from "vue";
import {onLoad} from "@dcloudio/uni-app";
import tui from "@/common/httpRequest";
const imageStyles = {
  width: "100%",
  height: 137,
};
const form= ref({
  "sex": "",
  "areaId": "",
  "realName": "",
  "birthday": "",
  "credentialsType": "",
  "credentialsCode": "",
  "credentialsFront": "",
  "credentialsBack": ""
})

const sexList=ref([])
const formImage = reactive({
  credentialsFront: [],
  credentialsBack: [],
})

//证件类型
const cardList= computed(()=>{
  return [{name:t('身份证'),id:1},{name:t('护照'),id:2},{name:t('驾驶证'),id:3}]
})

onLoad(({status})=>{
  // if(id){
  //  tui.request('/app-api/member/certification/get','POST',form).then(({data})=>{
  //    form.value = data
  //  })
  // }
  if(status==3){
       tui.request('/app-api/member/certification/get','POST',{}).then(({data})=>{
        form.value = data;
        formImage.credentialsFront = {
          url: data.credentialsFront,
        };
        formImage.credentialsBack = {
          url: data.credentialsBack,
        };
    })
  }
  getCountry()

})

const country = ref([]);
function getCountry(){
  tui.request("/app-api/system/config/list-country",'GET').then(res=>{
    country.value = res.data
  })
}



const  uploadCard=async (e,key)=>{
  const {data} =  await tui.uploadFile('/app-api/infra/file/upload',e.tempFilePaths[0])
  form.value[key]=data;
  formImage[key] = {
      url: data,
  };
}

//提交
const handleSub=async ()=>{
  try {
    if(!form.value.areaId){
      tui.toast(t('请选择国家/地区'))
      return;
    }
    if(!form.value.credentialsType){
      tui.toast(t('请选择证件类型'))
      return;
    }
    if(!form.value.credentialsCode){
      tui.toast(t('请输入证件号码'))
      return;
    }
    if(!form.value.realName){
      tui.toast(t('请输入姓名'))
      return;
    }
    if(!form.value.credentialsFront){
      tui.toast(t('上传证件正面'))
      return;
    }
    if(!form.value.credentialsBack){
      tui.toast(t('上传证件反面'))
      return;
    }
    await tui.request('/app-api/member/certification/save','POST',form.value)
    tui.toast(t("提交审核成功"))
    setTimeout(()=>{
      uni.navigateBack({delta:1})
    },1000)
  }catch (e) {
    console.log(e)
  }
}
</script>
<style lang="scss" scoped>
.authenticate-identity-class {
  padding: 20rpx 31rpx;
  display: flex;
  flex-direction: column;
  row-gap: 32rpx;
  .submit {
    width: 690rpx;
    height: 96rpx;
    flex-shrink: 0;
    border-radius: 20000rpx;
    background: #3248f4;
    color: #fff;
    text-align: center;
    font-family: "Noto Sans";
    font-size: 32rpx;
    font-style: normal;
    font-weight: 600;
    line-height: 145%; /* 46.4rpx */
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
    margin-bottom: 100rpx;
  }
  .item-class {
    .label-class {
      margin-bottom: 20rpx;
      color: #999;
      font-family: "Noto Sans";
      font-size: 24rpx;
      font-style: normal;
      font-weight: 400;
      line-height: 42rpx; /* 175% */
      letter-spacing: -0.64rpx;
    }
  }
  .gap{
    margin-top: 30rpx;
  }
}
</style>