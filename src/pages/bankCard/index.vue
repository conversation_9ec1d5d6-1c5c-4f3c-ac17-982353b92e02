<template>
  <view class="bank-card-class">
    <view class="add-bank-card" @click="addBankCard">
      <image
        src="@/static/images/common/3.png"
        class="add-bank-card-img"
      ></image>
      <view class="add-bank-card-text">
        <view class="add-bank-card-text-title">{{ $t("绑定银行卡") }}</view>
      </view>
    </view>

    <view class="bank-list-class">
      <view class="bank-list-item" v-for="(item,index) in list" @click="$jump('/pages/addBankCard/index?id='+item.id)" :key="index">
        <view class="bank-list-item-top"> {{item.typeName||''}} </view>
        <view class="bank-list-item-center"> {{item.account||''}} </view>
        <view class="bank-list-item-bottom"> {{item.name||'--'}} </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import {ref} from 'vue'
import {onShow} from "@dcloudio/uni-app";
import tui from "../../common/httpRequest";
const list = ref([]) //银行卡列表

function addBankCard() {
  uni.navigateTo({
    url: "/pages/addBankCard/index",
  });
}
onShow(()=>{
  requestBankCardList()
})

async function requestBankCardList(){
  let {data} = await tui.request("/app-api/member/wallet/page",'GET')
  list.value=data
}
</script>

<style scoped lang="scss">
.bank-card-class {
  .bank-list-class {
    padding: 0 32rpx;

    .bank-list-item {
      height: 262rpx;
      flex-shrink: 0;
      border-radius: 20rpx;
      background: #3248f4;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: flex-start;
      row-gap: 20rpx;
      padding: 30rpx 32rpx;
      margin-bottom: 30rpx;
      .bank-list-item-top {
        color: #fff;
        font-family: "PingFang SC";
        font-size: 28rpx;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }
      .bank-list-item-center {
        color: #fff;
        font-family: BigNoodleTitling;
        font-size: 56rpx;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }
      .bank-list-item-bottom {
        color: #fff;
        font-family: "PingFang SC";
        font-size: 28rpx;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }
    }
  }
  .add-bank-card {
    display: flex;
    width: 686rpx;
    height: 210rpx;

    padding: 0rpx 22rpx;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    border-radius: 20rpx;
    border: 2rpx solid #d8d8d8;
    margin: 22rpx 32rpx 32rpx 32rpx;
    box-sizing: border-box;
    row-gap: 20rpx;
    .add-bank-card-img {
      width: 48rpx;
      height: 48rpx;
      flex-shrink: 0;
    }
    .add-bank-card-text {
      .add-bank-card-text-title {
        color: #d8d8d8;
        font-family: "PingFang SC";
        font-size: 30rpx;
        font-style: normal;
        font-weight: bold;
        line-height: 44rpx; /* 146.667% */
        letter-spacing: -0.64rpx;
      }
    }
  }
}
</style>