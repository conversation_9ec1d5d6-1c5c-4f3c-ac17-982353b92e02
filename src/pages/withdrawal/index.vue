<template>
  <view class="recharge-container">
    <view class="title-class"> {{ $t("银行卡取款") }}</view>
    <view class="item-class">
      <view class="label-class">{{ $t("银行卡") }}</view>
      <view class="content-class" @click="addBankCard">
        <template v-if="bankInfo.name">
          <view class="current-label-class">
            {{ bankInfo.name }}
          </view>
        </template>
        <template v-else>
          <view class="placeholder-class">
            {{ $t("请选择银行卡") }}
          </view>
        </template>

        <image
          src="@/static/images/common/1.png"
          mode=""
          class="right-icon"
        ></image>
      </view>
      <!-- <InputSelect :placeholder="$t('请选择银行卡')" :popupTitle="$t('请选择银行卡')" :list="bankCardlist" keyLable="name"
        ref="refInputSelect" keyValue="id" v-model:value="form.walletId">
        <template #empty v-if="bankCardlist.length == 0">
          <view class="empty-wrapper">
            <view class="empty-bank-wrapper">{{ $t('暂无银行卡~~') }}</view>
            <view class="empty-btn" @click="addBankCard"> {{ $t('去添加') }}</view>
          </view>

        </template>
      </InputSelect> -->
    </view>
    <view class="item-class">
      <view class="label-class">
        <span>{{ $t("取款金额") }}</span>
        <span v-if="maxAmount"
          >{{ $t("最大") }}：${{ useFormatMoney(maxAmount) }}</span
        >
      </view>
      <uni-easyinput
        v-model="form.amount"
        trim="all"
        :placeholder="$t('请输入金额')"
        type="number"
      >
        <template #left>
          <view class="money-unit-text-class">$</view>
        </template>
        <template #right>
          <view
            class="all-text-class"
            @click="form.amount = userInfo.usdtBalance"
            >{{ $t("全部") }}</view
          >
        </template>
      </uni-easyinput>
      <view class="label-class mt-gap sw-p">
        <span v-if="false"> ≈ {{ useFormatMoney(currenyRatePrice) }}JPY</span>
        <span
          >{{ $t("可用") }}：${{ useFormatMoney(userInfo.usdtBalance) }}</span
        >
      </view>
    </view>
    <view class="item-class">
      <view class="label-class">{{ $t("资金密码") }}</view>
      <uni-easyinput
        v-model="form.fundPassword"
        trim="all"
        :placeholder="$t('请输入交易密码')"
        type="password"
      />
    </view>
    <span
      class="set-fund-passowrd"
      v-if="
        userInfo.hasSafePassword != null && userInfo.hasSafePassword == false
      "
      @click="goSetFundPassword()"
      >{{ $t("还未设置交易密码？点击去设置") }}</span
    >
    <view class="p1-wrapper-class">
      <view>{{ $t("手续费") }}</view>
      <view>{{ withdrawConfigText }}</view>
    </view>
    <view class="p2-wrapper-class"
      >{{
        $t(
          "您的本次取款，手续费将在您本次的取款金额中扣除，实际金额以实际到账金额为准。"
        )
      }}
    </view>
    <view class="desc-wrapper-class">
      {{
        $t(
          "您申请的取款我们将以美元账户为您转账结算，因为每个银行的国际汇款到账时间不同，到账时间以实际到账时间为准。"
        )
      }}
    </view>

    <view class="submit" @click="handleSumbit">
      {{ $t("提交") }}
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from "vue";
import config from "@/common/config";
import { useFormatMoney } from "@/common/utils";
import { t } from "@/hooks/useI18n";
import { onLoad, onShow } from "@dcloudio/uni-app";
import tui from "@/common/httpRequest";
import BigNumber from "bignumber.js";

const userInfo = ref({});
const refInputSelect = ref(null);
const currenyRate = ref(0);
const currentLabel = ref("");
function addBankCard() {
  uni.setStorageSync("bankInfo", bankInfo.value);
  uni.navigateTo({
    url: "/pages/addBankCard/index",
    success() {
      // refInputSelect.value.close();
      // uni.$emit("bankInfo", bankInfo.value);
    },
  });
}

const form = ref({
  walletId: "",
  amount: "",
  remark: "",
  fundPassword: "",
});

const bankCardlist = ref([]);
function open() {}
const withdrawConfigText = ref("0.00$");

const maxAmount = ref();

const bankInfo = ref({
  account: "",
  areaId: "",
  bankAddress: "",
  bankBranch: "",
  currencyCode: "",
  name: "",
  typeName: "",
  walletType: 1,
});

onLoad(({ type }) => {
  getWithdrawConfig();
  uni.setStorageSync("bankInfo", {});
  uni.$on("updateBankCard", (e) => {
    bankInfo.value = e;
  });
});
//
async function getWithdrawConfig() {
  try {
    const { data } = await tui.request(
      "/app-api/system/dict-data/tenant/type",
      "GET",
      { type: "withdraw_config" }
    );
    if (data) {
      //获取最大取款金额
      const maxData = data.find((item) => item.label == "max");
      if (maxData && maxData.value && maxData.value != "0") {
        maxAmount.value = maxData.value;
      }
      //获取手续费
      let freeData;
      freeData = data.find((item) => item.label == "fee_fix");
      if (freeData && freeData.value && freeData.value != "0") {
        withdrawConfigText.value = useFormatMoney(freeData.value) + "$";
        return;
      }
      freeData = data.find((item) => item.label == "fee_scale");
      if (freeData && freeData.value && freeData.value != "0") {
        withdrawConfigText.value = freeData.value + "%";
        return;
      }
    }
  } catch (e) {
    console.log(e);
  }
}

onShow(() => {
  getUserInfo();
  // hasBankCard()
  getCurrenyRate();
});

const currenyRatePrice = computed(() => {
  console.log("currenyRate", currenyRate.value);
  return (
    new BigNumber(form.value.amount || 0)
      .multipliedBy(currenyRate.value || 1)
      .toFixed(2) || 0
  );
});

async function hasBankCard() {
  //判断是否有银行卡,没有的话去添加银行卡
  let { data } = await tui.request("/app-api/member/wallet/page", "GET");
  bankCardlist.value = data;
  if (!(data && data.length > 0)) {
    uni.showModal({
      title: t("提示"),
      content: t("您暂无银行卡，需要去添加银行卡"),
      cancelText: t("取消"),
      confirmText: t("去添加"),
      success(res) {
        if (res.confirm) {
          addBankCard();
        }
      },
    });
  }
}

//去设置密码
function goSetFundPassword() {
  uni.navigateTo({ url: "/pages/setUserbuspwd/index" });
}

async function getUserInfo() {
  const { data } = await tui.request("/app-api/member/user/get", "GET");
  userInfo.value = data;
}

//币种
function getCurrenyRate(currency) {
  try {
    // 默认日本币，后续可以后台配置
    let exchangeRates = config.exchangeRates.find(
      (item) => item.quoteCurrency === "JPY"
    );
    if (exchangeRates) {
      currenyRate.value = Number(exchangeRates.realRate);
    }
  } catch (e) {
    currenyRate.value = 0;
  }
}

//提交
async function handleSumbit() {
  // if (!form.value.walletId) {
  //   tui.toast(t("请选择银行卡"));
  //   return;
  // }
  if (!bankInfo.value.account) {
    tui.toast(t("请选择银行卡"));
    return;
  }

  if (!form.value.amount) {
    tui.toast(t("请输入有效金额"));
    return;
  }
  if (!userInfo.value.hasSafePassword) {
    uni.showModal({
      title: t("提示"),
      content: t("提现必须设置交易密码，发现您未设置，是否去设置"),
      confirmText: t("去设置"),
      success(res) {
        if (res.confirm) {
          goSetFundPassword();
        }
      },
    });
    return;
  }
  if (!form.value.fundPassword) {
    tui.toast(t("请输入交易密码"));
    return;
  }
  await tui.request(
    "/app-api/member/funds-records/create-withdraw",
    "POST",
    {...form.value,...bankInfo.value}
  );
  tui.toast(t("申请成功t"));
  setTimeout(() => {
    uni.navigateBack({ delta: 1 });
  }, 1000);
}
</script>

<style scoped lang="scss">
.recharge-container {
  padding: 20rpx 31rpx;
  display: flex;
  flex-direction: column;
  row-gap: 32rpx;

  .title-class {
    color: #000;
    font-family: "PingFang SC";
    font-size: 36rpx;
    font-style: normal;
    font-weight: bold;
    padding-top: 32rpx;
    padding-bottom: 32rpx;
  }

  .submit {
    width: 690rpx;
    height: 96rpx;
    flex-shrink: 0;
    border-radius: 20000rpx;
    background: #3248f4;
    color: #fff;
    text-align: center;
    font-family: "Noto Sans";
    font-size: 32rpx;
    font-style: normal;
    font-weight: 600;
    line-height: 145%;
    /* 46.4rpx */
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 50rpx;
  }

  .item-class {
    .label-class {
      margin-bottom: 20rpx;
      color: #999;
      font-family: "Noto Sans";
      font-size: 24rpx;
      font-style: normal;
      font-weight: 400;
      line-height: 42rpx;
      /* 175% */
      display: flex;
      justify-content: space-between;
      letter-spacing: 3rpx;
    }

    .money {
      margin-top: 3rpx;
      letter-spacing: 3rpx;
    }
  }

  .p1-wrapper-class {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-top: 30rpx;
    padding-bottom: 30rpx;
    border-bottom: 1rpx solid #efefef;
  }

  .p2-wrapper-class {
    color: #979797;
    font-size: 24rpx;
    line-height: 145%;
  }

  .desc-wrapper-class {
    display: flex;
    padding: 20rpx;
    justify-content: center;
    align-items: center;
    border-radius: 10px;
    background: rgba(255, 153, 0, 0.04);
    color: #f90;
    font-size: 24rpx;
    font-style: normal;
    line-height: 44rpx;
    /* 183.333% */
    letter-spacing: -0.64rpx;
  }
}

.all-text-class {
  font-size: 30rpx;
  color: #3248f4;
  padding-right: 20rpx;
}

.money-unit-text-class {
  font-size: 30rpx;
  color: #3248f4;
  padding-left: 20rpx;
}

.empty-wrapper {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  padding-top: 150rpx;

  .empty-bank-wrapper {
    color: #999999;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .empty-btn {
    background: white;
    width: 250rpx;
    height: 80rpx;
    border-radius: 100rpx;
    background: var(--common-positive-color);
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 50rpx;
  }
}

.set-fund-passowrd {
  color: var(--common-negative-color);
  font-weight: bold;
  font-size: 24rpx;
}

.mt-gap {
  margin-top: 5rpx;
}

.ml10 {
  margin-left: 40rpx;
}

.content-class {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 88rpx;
  flex-shrink: 0;
  color: #d8d8d8;
  font-family: "PingFang SC";
  font-size: 30rpx;
  font-style: normal;
  font-weight: bold;
  line-height: 44rpx; /* 146.667% */
  letter-spacing: -0.64rpx;
  border: 1rpx solid #dcdfe6;
  border-radius: 10rpx;
  padding: 0 24rpx;
  box-sizing: border-box;
}

.current-label-class {
  color: #000;
  font-family: "PingFang SC";
  font-size: 30rpx;
  font-style: normal;
  font-weight: bold;
  line-height: 44rpx; /* 146.667% */
  letter-spacing: -0.64rpx;
  min-width: 0;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.placeholder-class {
  color: #d8d8d8;
  font-family: "PingFang SC";
  font-size: 30rpx;
  font-style: normal;
  font-weight: bold;
  line-height: 44rpx; /* 146.667% */
  letter-spacing: -0.64rpx;
  min-width: 0;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.right-icon {
  width: 28rpx;
  height: 28rpx;
  flex-shrink: 0;
}
</style>