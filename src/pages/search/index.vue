<template>
  <view class="search-class">
    <z-paging ref="paging" v-model="dataList" @query="queryList" :loading-more-enabled="false" :auto="false" :refresher-enabled="false">
      <template #top>
        <view class="search-value-warp-class">
          <uni-easyinput
            class="search-value-class"
            trim="all"
            prefixIcon="search"
            v-model="searchValue"
            :placeholder="$t('搜索币种、交易对名称')"
            confirmType="search"
            @change="searchFun"
          ></uni-easyinput>
          <span class="cancel-class" @click="returnPage">{{ $t("取消") }}</span>
        </view>
        <z-tabs
          :list="tabList"
          @change="tabChange"
          class="tabs-contract-line-class"
        />
        <view class="table-td-class">
            <SortIcon class="item1-class" :labelName="$t('名称')" sorting="name" @sort-changed="sortChanged"/>
            <SortIcon class="item2-class" :labelName="$t('最新价格')" sorting="currentPrice" @sort-changed="sortChanged"/>
            <SortIcon :labelName="$t('涨跌')" sorting="percentage" @sort-changed="sortChanged"/>
          </view>
      </template>

      <view class="list-warp-class">
        <CurrencyItem v-for="item in dataList" :key="item.id" :item="item"></CurrencyItem>
      </view>
    </z-paging>
  </view>
</template>
<script setup>
import { computed, ref } from "vue";
import tui from "@/common/httpRequest.js";
import { t } from "@/hooks/useI18n";
import { sortArrayByKey } from "@/common/utils";
const searchValue = ref();
const paging = ref(null);
const dataList = ref([]);
const oldDataList = ref([]);
// const tabList = ref([t("限时"), t("合约")]);
const tabList = computed(()=>{
  // return [t("限时"), t("合约")]
  return [t("限时")]
})
const tabIndex = ref(0);

function tabChange(index) {
  tabIndex.value = index;
  // 当切换tab或搜索时请调用组件的reload方法，请勿直接调用：queryList方法！！
  paging.value.reload();
}
function queryList(pageNo, pageSize) {
  const params = {
      // tradeType:tabList.value==0?'3':'1'
      keyword:searchValue.value
    }
    tui.request('/app-api/exchange/trade-pair/search','POST',params).then(res => {
      const list = res.data.filter(item => {
        const index = tabIndex.value==0?'3':'1';
        return item.tradeType==index;
      })

      paging.value.complete(list);
      oldDataList.value = list;
    })

  // tui.request.queryList(params).then(res => {
  // 	// 将请求的结果数组传递给z-paging
  // 	paging.value.complete(res.data.list);
  // }).catch(res => {
  // 	// 如果请求失败写this.$refs.paging.complete(false);
  // 	// 注意，每次都需要在catch中写这句话很麻烦，z-paging提供了方案可以全局统一处理
  // 	// 在底层的网络请求抛出异常时，写uni.$emit('z-paging-error-emit');即可
  // 	paging.value.complete(false);
  // })
}

//排序
function sortChanged(key,sort){
  const sortedList = sortArrayByKey(JSON.parse(JSON.stringify(oldDataList.value)), key, sort);
  paging.value.resetTotalData(sortedList);;
}


function searchFun(e){
  if(e){
    paging.value.reload();
  }
  
  // const list = oldDataList.value.filter(item=>{
  //   return item.name.toLowerCase().indexOf(e.toLowerCase()) > -1;
  // });
  // paging.value.resetTotalData(list);
}
function returnPage(){
  uni.navigateBack()
}
</script>

<style scoped lang="scss">
.search-class {
  .table-td-class {
      display: flex;
      justify-content: space-between;
      box-sizing: border-box;
      padding: 0 32rpx;
      align-items: flex-end;
      height: 62rpx;
      .item1-class{
        width: 33%;
      }
      .item2-class{
        // width: 23%;
      }
    }
  .list-warp-class {
    padding: 24rpx 32rpx;
    display: flex;
    justify-content: center;
    flex-direction: column;
    row-gap: 48rpx;
  }
  .search-value-warp-class {
    display: flex;
    justify-content: center;
    align-items: center;
    column-gap: 40rpx;
    padding: 48rpx 30rpx 30rpx;
    width: 750rpx;
    margin: 0 auto;
    box-sizing: border-box;
    .search-value-class {
      ::v-deep .is-input-border{
        border-radius: 200rpx;
      }
      ::v-deep input{
        height: 80rpx;

      }


    }
    .cancel-class {
      color: #3d3a50;
      text-align: center;
      font-family: "PingFang SC";
      font-size: 28rpx;
      font-style: normal;
      font-weight: 400;
      line-height: 32rpx; /* 114.286% */
    }
  }
  .tabs-contract-line-class {
    border-bottom: 2rpx solid #f9f9f9;
    ::v-deep .z-tabs-list-container {
      justify-content: flex-start;
      .z-tabs-list {
        flex: 0 !important;
      }
      .z-tabs-item-title {
        text-align: center;
        font-family: "PingFang SC";
        font-size: 28rpx;
        font-style: normal;
        line-height: 32rpx; /* 114.286% */
      }
    }
  }
}
</style>