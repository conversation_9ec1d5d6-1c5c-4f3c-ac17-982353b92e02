<template>
  <view class="login-or-registration-class">
    <view class="loading">
      <view
        v-for="(letter, index) in letters"
        :key="index"
        class="letter"
        :style="{ animationDelay: `${index * 0.1}s` }"
      >
        {{ letter }}
      </view>
    </view>
    <view class="bottom-section">
      <view class="sign-in-class" @click="navRouting('/pages/signIn/index')"
        >{{ $t('登录') }}</view
      >
      <view class="register-class" @click="navRouting('/pages/register/index')"
        >{{ $t('注册') }}</view
      >
    </view>
  </view>
</template>
  
  <script setup>
import { ref, onMounted } from "vue";
import config from "@/common/config";
import { onShow } from "@dcloudio/uni-app";
const word = config.tenantName; // 这里可以改成你想要的任何单词
const letters = ref([]);

function navRouting(url) {
  uni.redirectTo({
    url: url,
  });
}
onShow(() => {
  letters.value = word.split("");
});
</script>
  
  <style scoped lang="scss">
@font-face {
  font-family: "Phosphate";
  src: url("@/static/fonts/PhosphateInline.ttf");
  font-weight: normal;
  font-style: normal;
}
page {
  width: 100%;
  height: 100vh;
  background-color: #3248f4;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}
.login-or-registration-class {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;

  .loading {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    text-align: center;
    font-family: "Phosphate";
    font-size: 80rpx;
    font-style: normal;
    font-weight: 400;
    line-height: 140%;
    /* 112rpx */
  }

  .letter {
    opacity: 0;
    animation: fadeInOut 1s linear infinite;
  }

  .bottom-section {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .sign-in-class {
      color: #3248f4;
      text-align: center;
      font-family: "PingFang SC";
      font-size: 32rpx;
      font-style: normal;
      font-weight: bolder;
      line-height: 145%; /* 46.4rpx */
      width: 690rpx;
      height: 96rpx;
      flex-shrink: 0;
      border-radius: 20000rpx;
      background: #fff;

      display: flex;
      justify-content: center;
      align-items: center;
    }
    .register-class {
      color: #fff;
      text-align: center;
      font-family: "PingFang HK";
      font-size: 32rpx;
      font-style: normal;
      font-weight: bold;
      line-height: 140%; /* 44.8rpx */
      display: flex;
      width: 686rpx;
      height: 116rpx;
      padding: 20rpx;
      justify-content: center;
      align-items: center;
      gap: 20rpx;
      border-radius: 16rpx;
    }
  }

  @keyframes fadeInOut {
    0%,
    100% {
      opacity: 0;
    }

    50% {
      opacity: 1;
    }
  }
}
</style>
  