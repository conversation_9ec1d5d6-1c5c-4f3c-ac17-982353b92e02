<template>
	<view class="security-settings-class">
		<uni-list class="list-warp-class" :border="false">
			<uni-list-item v-if="ableModPassword" :title="$t('登录密码')" link to="/pages/changepwd/index?type=1">
				<template #footer>
					<span class="right-class">{{ $t("修改") }}</span>
				</template>
			</uni-list-item>
			<uni-list-item v-if="ableModPayPassword" :title="$t('支付密码')" link :to="userInfo.hasSafePassword ? '/pages/changeUserbuspwd/index' : '/pages/setUserbuspwd/index'">
				<template #footer>
					<span class="right-class">{{ userInfo.hasSafePassword ? $t("修改") : $t("设置") }}</span>
				</template>
			</uni-list-item>
		</uni-list>
	</view>
</template>

<script setup>
	import { ref } from "vue";
	import config from "@/common/config";
	import { useFormatMoney } from "@/common/utils";
	import { t } from "@/hooks/useI18n";
	import { onLoad, onShow } from "@dcloudio/uni-app";
	import tui from "@/common/httpRequest";
	const userInfo = ref({});
	const ableModPassword = ref(false);
	const ableModPayPassword = ref(false);

	onShow(() => {
		getUserInfo();
		getUserConfig();
	});
	async function getUserInfo() {
		const { data } = await tui.request("/app-api/member/user/get", "GET");
		userInfo.value = data;
	}
	async function getUserConfig() {
		const { data } = await tui.request("/app-api/system/dict-data/tenant/type", "GET", { type: "user_config" });
		if (data) {
			const ifAbleModPassword = data.find((item) => item.label == "able_mod_password").value;
			const ifAbleModPayPassword = data.find((item) => item.label == "able_mod_paypassword").value;
			if (ifAbleModPassword == 1 || ifAbleModPassword == "1" || ifAbleModPassword == "true" || ifAbleModPassword == true) {
				ableModPassword.value = true;
			} else {
				ableModPassword.value = false;
			}
			if (ifAbleModPayPassword == 1 || ifAbleModPayPassword == "1" || ifAbleModPayPassword == "true" || ifAbleModPayPassword == true) {
				ableModPayPassword.value = true;
			} else {
				ableModPayPassword.value = false;
			}
		} else {
			ableModPassword.value = true;
			ableModPayPassword.value = true;
		}
	}
</script>

<style scoped lang="scss">
	.security-settings-class {
		.right-class {
			color: #979797;
			font-family: "PingFang SC";
			font-size: 30rpx;
			font-style: normal;
			font-weight: 400;
			line-height: normal;
			letter-spacing: -0.64rpx;
		}
		::v-deep .uni-list-item__content-title {
			color: #000;
			font-family: "PingFang SC";
			font-size: 32rpx;
			font-style: normal;
			font-weight: bold;
			line-height: normal;
			letter-spacing: -0.64rpx;
		}
	}
</style>
