<template>
  <view class="personal-center-class">
    <view class="head-class">
      <view class="top-class" @click="navRouting('/pages/personalInformation/index')">
        <view class="user-information-class">
          <image
            :src="userInfo.avatar||'/static/images/index/2.png'"
            class="head-portrait-class"
          ></image>

          <view class="info-class">
            <view class="mailbox-class">{{userInfo.username}}</view>
            <view class="id-class">
              <span>ID : {{userInfo.id}}</span>
              <image
                class="copy-class"
                src="@/static/images/index/6.png"
                @click.stop="copyText"
              ></image>
            </view>
            <view class="state-warp-class">
              <span class="state-class">{{userInfo.certificationStatusStr||''}}</span>
              <span class="state2-class">{{$t('信用分',[userInfo.creditScore])}}</span>
              <!-- <span class="state2-class" v-if="false">{{userInfo.level?userInfo.level.name:$t('普通用户')}}</span> -->
            </view>
          </view>
        </view>
        <image
          src="@/static/images/index/12.png"
          class="modify-email-icon-class"
        ></image>
      </view>

      <view class="function-class">
        <!-- <view class="function-item-class">
          <image
            src="@/static/images/index/7.png"
            class="function-icon-class"
          ></image>
          <view class="function-text-class">{{ $t("分享") }}</view>
        </view> -->

        <view class="function-item-class" @click="navRouting('/pages/identityAuthentication/index')">
          <image
            src="@/static/images/index/8.png"
            class="function-icon-class"
          ></image>
          <view class="function-text-class">{{ $t("身份认证") }}</view>
        </view>

        <!-- <view class="function-item-class">
          <image
            src="@/static/images/index/9.png"
            class="function-icon-class"
          ></image>
          <view class="function-text-class">{{ $t("钱包地址") }}</view>
        </view> -->

        <!-- <view class="function-item-class" @click="navRouting('/pages/bankCard/index')">
          <image
            src="@/static/images/index/10.png"
            class="function-icon-class"
          ></image>
          <view class="function-text-class">{{ $t("银行卡绑定") }}</view>
        </view> -->
      </view>
    </view>

    <uni-list class="list-warp-class" :border="false">
      <uni-list-item :title="$t('安全设置')" link to="/pages/securitySettings/index">
        <!-- <template #footer>
          <image
            src="@/static/images/index/11.png"
            class="next-step-class"
          ></image>
        </template> -->
      </uni-list-item>

      <uni-list-item :title="$t('语言')" link to="/pages/languageset/index">
        <template #footer>
          <!-- <image
            src="@/static/images/index/11.png"
            class="next-step-class"
          ></image> -->
        </template>
      </uni-list-item>
      <uni-list-item :title="$t('默认法币')" link  to="/pages/currency/index">
        <template #footer>
          <view class="default-fiat-currency-class">
            <span> 1USD ~ {{config.currentRate.realRate}}{{config.currencyUnit}}</span>
            <!-- <image
              src="@/static/images/index/11.png"
              class="next-step-class"
            ></image> -->
          </view>
        </template>
      </uni-list-item>
      <!-- <uni-list-item :title="$t('关于我们')" link class="bottom-line-class" @click="navRouting('/pages/aboutUs/index')">
      </uni-list-item> -->
    </uni-list>
    <view class="exit-login-class" @click="exitLogin()">
      {{ $t("退出登录") }}
    </view>
  </view>
</template>

<script setup>
import {computed, ref} from 'vue'
import { t } from "@/hooks/useI18n";
import tui from "@/common/httpRequest.js";
import {onLoad,onShow} from "@dcloudio/uni-app";
import config from '@/common/config';

const { exchangeRates,currentRate } = config;

// const rate = computed(()=>{
//   return exchangeRates.find(item=>item.quoteCurrency=== config.currencyUnit)
// })
const userInfo = ref({})

onShow(()=>{
   getUserInfo()
})


function exitLogin(){
  uni.showModal({
    title: t("uni-popup.title"),
    content: t("是否退出登录"),
    cancelText: t("uni.showModal.cancel"),
    confirmText: t("uni.showModal.confirm"),
    success:async function (res) {
      if (res.confirm) {
          await tui.request('/app-api/member/auth/logout','POST')
          // uni.clearStorageSync();
          tui.removeToken();
          uni.reLaunch({url:'/pages/tabBar/index'})
      } else if (res.cancel) {
        console.log("用户点击取消");
      }
    },
  });
}

function copyText() {
  uni.setClipboardData({
    data: userInfo.value.id||'',
    success: function () {
      tui.toast(t("uni.setClipboardData.success"));
    },
  });
}

async function getUserInfo(){
 const {data} =  await tui.request('/app-api/member/user/get','GET')
 userInfo.value=data
}

function navRouting(url){
  uni.navigateTo({
    url: url
  })
}
</script>
<style scoped lang="scss">
.personal-center-class {
  .exit-login-class {
    width: 690rpx;
    height: 96rpx;
    flex-shrink: 0;
    color: #000;
    text-align: center;
    font-family: "PingFang SC";
    font-size: 32rpx;
    font-style: normal;
    font-weight: 400;
    line-height: 145%; /* 46.4rpx */
    border-radius: 20000rpx;
    background: #efefef;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    right: 50%;
    margin-top: 80rpx;
    transform: translateX(50%);
  }
  .list-warp-class {
    padding: 0 32rpx;
    padding-top: 48rpx;
    border-top-left-radius: 30rpx;
    border-top-right-radius: 30rpx;
    margin-top: -30rpx;
    .bottom-line-class {
      border-bottom: 2rpx solid #f7f7f7;
    }
    .default-fiat-currency-class {
      color: #999;
      text-align: right;
      font-family: "Noto Sans";
      font-size: 22rpx;
      font-style: normal;
      font-weight: 500;
      line-height: 40rpx; /* 181.818% */
      letter-spacing: -0.64rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      column-gap: 20rpx;
    }
    .next-step-class {
      width: 28rpx;
      height: 28rpx;
    }
  }
  .head-class {
    padding: 0 32rpx;
    background: #3349f7;

    box-sizing: border-box;
    padding-bottom: 98rpx;
    .function-class {
      border-radius: 20rpx;
      background: #fff;
      height: 166rpx;
      flex-shrink: 0;
      display: flex;
      align-items: center;

      .function-item-class {
        display: flex;
        align-items: center;
        flex-direction: column;

        gap: 8rpx;
        flex: 1 0 0;
        .function-icon-class {
          width: 48rpx;
          height: 48rpx;
        }
        .function-text-class {
          color: #3d3a50;
          text-align: center;
          font-family: "PingFang SC";
          font-size: 24rpx;
          font-style: normal;
          font-weight: bold;
          line-height: 42rpx; /* 175% */
          letter-spacing: -0.64rpx;
        }
      }
    }
    .top-class {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 48rpx;
      .modify-email-icon-class {
        width: 24rpx;
        height: 24rpx;
        flex-shrink: 0;
      }
      .user-information-class {
        display: flex;
        justify-content: center;
        align-items: center;
        row-gap: 12rpx;
        .info-class {
          display: flex;
          justify-content: center;
          align-items: flex-start;
          flex-direction: column;
          row-gap: 12rpx;
        }
        .state-warp-class {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          column-gap: 16rpx;
          .state2-class {
            color: #fff;
            text-align: center;
            font-family: "Noto Sans";
            font-size: 23rpx;
            font-style: normal;
            font-weight: 500;
            line-height: normal;
            letter-spacing: -0.64rpx;
            display: flex;
            padding: 8rpx 20rpx;
            justify-content: center;
            align-items: center;
            gap: 20rpx;
            border-radius: 8rpx;
            background: rgba(255, 255, 255, 0.15);
          }
          .state-class {
            color: #000;
            text-align: center;
            font-family: "Noto Sans";
            font-size: 23rpx;
            font-style: normal;
            font-weight: 500;
            line-height: normal;
            letter-spacing: -0.64rpx;
            display: flex;
            padding: 8rpx 20rpx;
            justify-content: center;
            align-items: center;
            gap: 20rpx;
            border-radius: 8rpx;
            background: #fff;
          }
        }
        .id-class {
          color: #fff;
          text-align: center;
          font-family: "Noto Sans";
          font-size: 23rpx;
          font-style: normal;
          font-weight: 400;
          line-height: 42rpx; /* 190.909% */
          letter-spacing: -0.64rpx;
          column-gap: 8rpx;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          .copy-class {
            width: 32rpx;
            height: 32rpx;
          }
        }
        .mailbox-class {
          color: #fff;
          text-align: center;
          font-family: "Noto Sans";
          font-size: 32rpx;
          font-style: normal;
          font-weight: 500;
          line-height: 145%; /* 46.4rpx */
        }
      }
      .head-portrait-class {
        width: 88rpx;
        height: 88rpx;
        flex-shrink: 0;
        border-radius: 88rpx;
        // border: 2rpx solid #fff;
        margin-right: 26rpx;
      }
    }
  }
}
</style>