<template>
  <view class="order-create-container">
    <!-- 头部导航   -->
    <TransactionHead
      :fromType="2"
      :currency="currency"
      @handleCurrencySelect="handleCurrencySelect"
      >></TransactionHead
    >
    <!-- 数据信息   -->
    <TransactionInfo :currency="currency"></TransactionInfo>
    <view class="set-wrapper">
      <view class="set-left-wrapper">
        <!-- 合约/期权tab   -->
        <view class="tabs-wrapper-class">
          <view @click="handleTab(constant.BUY_UP)">
            <view :class="{ 'tab-active': typeTabsActive == constant.BUY_UP }"
              >{{ $t("做多") }}
            </view>
          </view>
          <view @click="handleTab(constant.BUY_DOWN)">
            <view
              :class="{
                'tab-sell-active': typeTabsActive == constant.BUY_DOWN,
              }"
              >{{ $t("做空") }}</view
            >
          </view>
        </view>
        <OrderSetInput
          :minBuy="minBuy"
          :radioList="leverages"
          :user="user"
          :feeRate="feeRate"
          class="order-set-input"
          :type="typeTabsActive"
          @changeType="changeType"
          @comfirm="handleSubmit"
        >
        </OrderSetInput>
      </view>
      <view class="set-right-wrapper">
        <OrderPriceDisplay :quoteAsset="quoteAsset" :baseAsset="baseAsset"></OrderPriceDisplay>
      </view>
    </view>
    <OrderXjList :orderList="orderList" @revokeItem="revokeItem"></OrderXjList>
    <!--OrderSjList :orderList="orderList"></OrderSjList-->
    <!--OrderSure :orderType="typeTabsActive" ref="orderSureRef"></OrderSure-->
    <CurrencySelect ref="currencySelect" @currencyChange="currencyChange" ></CurrencySelect>
  </view>
</template>

<script setup>
import {
  onLoad,
  onPageScroll,
  onReachBottom,
  onReady,
  onShow,
} from "@dcloudio/uni-app";
import OrderSetInput from "./weight/OrderSetInput";
import OrderPriceDisplay from "./weight/OrderPriceDisplay";
import OrderSjList from "./weight/OrderSjList";
import OrderXjList from "./weight/OrderXjList";
import constant from "@/common/constant";
import tui from "@/common/httpRequest";
import { inject, ref } from "vue";
import { EventBus } from '@/common/eventBus';

import { t } from "@/hooks/useI18n";
//买入卖出
const typeTabsActive = ref(1);
//市价限价
const type2TabsActive = ref(0);
//
const tab2TypeIndex = ref(1);

// const orderSureRef = ref(null)
//币种
const currency = ref({ name: "GOLD", code: "XAUUSD" }); //币种
const websocketClient = inject("websocketClient");
const currencySelect = ref(null)

onLoad(({ type, name, code }) => {
  if (type == 0) {
    typeTabsActive.value = 0;
  } else {
    typeTabsActive.value = 1;
  }
  if (code && name) {
    currency.value.code = code;
    currency.value.name = name;
  } else {
    currency.value = config.defaultTradePair || {
      name: "GOLD",
      code: "XAUUSD",
    };
  }
  console.log("code", currency);
  getTransactionData();
  getContract();
  getCommission()
});

// commission

function getCommission(){
  EventBus.on('commission', ({orderNo}) => {
      const list = orderList.value.filter(item => item.orderNo != orderNo)
      orderList.value = [...list]
  });
}

function getUser() {
  tui.request("/app-api/member/user/get", "GET").then((res) => {
    user.value = res.data;
  });
}

const orderList = ref([]);
function getOrderPage() {
  const params = {
    pageNo: 1,
    pageSize: 100,
  };
  tui
    .request("/app-api/trade/order/contract/order-page", "GET", params)
    .then((res) => {
      orderList.value = res.data.list;
    });
}

const user = ref({});
onShow(() => {
  getUser();
  getOrderPage();
});

const leverages = ref([]);
const feeRate = ref(0);
const minBuy = ref(0);
const baseAsset = ref(0);
const quoteAsset = ref(0);
//合约配置
function getContract() {
  const form = {
    code: currency.value.code,
  };
  tui
    .request("/app-api/trade/contract/simple-info", "GET", form)
    .then((res) => {
      if (res.code == 0) {
        leverages.value = res.data.leverages;
        feeRate.value = res.data.feeRate;
        minBuy.value = res.data.minBuy;
        quoteAsset.value = res.data.quoteAsset;
        baseAsset.value = res.data.baseAsset;
      }
    });
}

//获取交易数据
const getTransactionData = (code) => {
  const transaction = {
    type: "cmd",
    cmd: 1000,
    data: {
      code: currency.value.code,
    },
  };
  uni.setStorageSync("WS-TRAN-CURRENCY-CODE", currency.value.code);
  websocketClient.sendMessage(JSON.stringify(transaction));
};
//
//点击合约/期权tab
const handleTab = (tab) => {
  typeTabsActive.value = tab;
};

const type2tabChange = (index) => {
  tab2TypeIndex.value = index;
};

const changeType = (data) => {
  type2TabsActive.value = data;
};
//买入买多
const handleSubmit = (params) => {
  // orderSureRef.value.open()
  const _params = {
    ...params,
    code: currency.value.code,
  };

  console.log("params", _params);
  tui
    .request("/app-api/trade/contract/order-create", "POST", _params)
    .then((res) => {
      if (res.code == 0) {
        uni.showToast({
          title: t("提交成功"),
          icon: "none",
        });
        getUser();
        getOrderPage();
      }
    });
};

function revokeItem(item){
  tui
    .request("/app-api/trade/contract/order-cancel", "POST", {id:item.id})
    .then((res) => {
      if (res.code == 0) {
        uni.showToast({
          title: t("撤单成功"),
          icon: "none",
        });
        getUser();
        getOrderPage();
      }
    });
}

//货币切换
const currencyChange = (e) => {
  // ?type=1&code=XAUUSD&name=GOLD
  const { code,name } = e;
  // currency.value = e;
  // getTransactionData();
  //一些处理数据
  uni.redirectTo({ url: "/pages/orderCreate/index?type=1" + '&code=' + code + '&name=' + name })
};

const handleCurrencySelect=()=>{
     currencySelect.value.open();
   }

</script>

<style scoped lang="scss">
.order-create-container {
  width: 100%;
  height: 100%;
  position: relative;
  box-sizing: border-box;
  padding-top: 130rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.set-wrapper {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 30rpx;
  box-sizing: border-box;
}

.set-left-wrapper {
  width: 430rpx;
}

.set-right-wrapper {
  width: 230rpx;
}

.tabs-wrapper-class {
  width: 420rpx;
  height: 74rpx;
  flex-shrink: 0;
  border-radius: 16rpx;
  border: 2rpx solid #f9f9f9;
  background: white;
  display: flex;
  flex-direction: row;
  align-items: center;
  box-sizing: border-box;
  padding-left: 7rpx;
  padding-right: 7rpx;

  view {
    flex: 1;
    color: #999;
    font-size: 28rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.tab-active {
  width: 200rpx;
  height: 65rpx;
  color: white !important;
  font-weight: bold !important;
  display: flex;
  justify-content: center;
  align-items: center;
  background: var(--common-positive-color) !important;
  border-radius: 8rpx !important;
}

.tab-sell-active {
  width: 200rpx;
  height: 65rpx;
  color: white !important;
  font-weight: bold !important;
  display: flex;
  justify-content: center;
  align-items: center;
  background: var(--common-negative-color) !important;
  border-radius: 8rpx !important;
}

.order-set-input {
  margin-top: 32rpx;
}
</style>