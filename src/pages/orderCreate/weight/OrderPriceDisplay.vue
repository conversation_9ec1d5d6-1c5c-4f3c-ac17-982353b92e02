<template>
  <view class="order-price-display">
      <view class="title-wrapper">
        <view>
          <span> {{$t("价格")}}</span>
          <span>({{quoteAsset}}) </span>
        </view>
        <view>
          <span> {{$t("数量")}}</span>
          <span>({{baseAsset}})</span>
        </view>
      </view>
    <view class="mt10">
      <view class="data-item-wrapper" v-for="(item,index) in buyList"  :key="index">
        <span class="money-positive-color">{{useFormatMoney(item.price)}}</span>
        <span>{{item.volume}}</span>
        <view class="percentage-buy-class" :style="getStyleForValue(item.volume,'buy')"></view>
      </view>
    </view>
    <view class="m1 mt10" :style="getStylePriceForValue()">{{useFormatMoney(priceData.price) ||'--'}}</view>
    <!-- <view class="m2">{{priceForCurrency()}}{{config.currencyUnit}}</view> -->
    <view class="mt10">
      <view class="data-item-wrapper" v-for="(item,index) in sellList" :key="index">
        <span class="money-negative-color">{{useFormatMoney(item.price)}}</span>
        <span>{{item.volume}}</span>
        <view class="percentage-sell-class" :style="getStyleForValue(item.volume,'sell')"></view>
      </view>
    </view>
  </view>

</template>

<script setup>
import config from "@/common/config";
import { useFormatMoney } from "@/common/utils";
import { EventBus } from '@/common/eventBus';
 import {onLoad,onUnload} from "@dcloudio/uni-app";
import {ref} from "vue";
const buyList=ref([]) //买列表
const sellList=ref([]) //卖列表
const totalBuyVolume=ref(0) //买总交易量
const totalSellVolume=ref(0) // 卖总交易量
const priceData = ref({})
defineProps({
  quoteAsset:{
    type:[String,Number],
    default:''
  },
  baseAsset:{
    type:[String,Number],
    default:''
  }
})
onLoad(()=>{
  EventBus.on('transaction-buyseller-list', (data) => {
    buyList.value=data.bids.slice(0,6)
    sellList.value=data.asks.slice(0,6)
    //买总交易量
    totalBuyVolume.value = buyList.value.reduce((total, item) => {
      return total + Number(item.volume)
    }, 0);
    //卖总交易量
    totalSellVolume.value = sellList.value.reduce((total, item) => {
      return total + Number(item.volume)
    }, 0);
  })

  EventBus.on('transaction-price-info', (data) => {
    priceData.value =data
  })
})

//获取交易量百分比
function getStyleForValue(volume,type) {
  let width = 0;
  try {
    if (type == 'buy') {
      width = Number(volume) / totalBuyVolume.value * 100 * 10
    } else if (type == 'sell') {
      width = Number(volume) / totalSellVolume.value * 100 * 10
    }
    if (width < 0) {
      width = 0 + "%"
    } else if (width > 100) {
      width = 100 + "%"
    } else {
      width = width + "%"
    }
    return {width};
  } catch (e) {
    return {width}
  }
}
// 法币对应的价格
function  priceForCurrency(){
  try {
    return useFormatMoney(Number(priceData.value.price)*config.currentRate.realRate)
  }catch (e) {
    return ''
  }
}


function getStylePriceForValue(){
  let color ="#000000"
  let price= Number(priceData.value.price)
  let openPrice = Number(priceData.value.openPrice)
  if ( price > openPrice) {
    color = "var(--positive-color)";
  } else if (price < openPrice) {
    color = "var(--negative-color)";
  } else {
    color = "var(--zero-color)";
  }
  return {color}
}
</script>

<style scoped lang="scss">
  .order-price-display{
    width: 100%;
    display: flex;
    flex-direction: column;
    font-family: "Noto Sans";
    .title-wrapper{
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      font-size: 24rpx;
      span{
        text-align: center;
      }
      view{
        display: flex;
        flex-direction: column;
      }
    }
    .data-item-wrapper{
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      font-size: 23rpx;
      padding-top: 4rpx;
      padding-bottom: 4rpx;
      position: relative;
    }
    .percentage-buy-class{
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      opacity: 0.1 !important;
      background: var(--common-positive-color);
      z-index: 2;
      box-sizing: border-box;
    }
    .percentage-sell-class{
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      opacity: 0.1 !important;
      background: var(--common-negative-color);
      z-index: 2;
      box-sizing: border-box;
    }
    .money-positive-color{
      color: var(--common-positive-color);
    }
    .money-negative-color{
      color: var(--common-negative-color);
    }
    .m1{
      font-size: 28rpx;
      color: #000000;
      font-weight: bold;
    }
    .m2{
      font-size: 22rpx;
      color: #979797;
      margin-top: 5rpx;
    }
    .mt10{
      margin-top: 10rpx;
    }
  }
</style>