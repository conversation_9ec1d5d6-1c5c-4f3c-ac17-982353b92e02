<template>
  <view class="order-sj-list">
    <view class="th one-b-1px">
      <span class="text-left width1">{{ $t("类型") }}</span>
      <span class="width2">{{ $t("时间") }}</span>
      <span class="width1-5">{{ $t("价格") }}</span>
      <span class="width1-5">{{ $t("交易手数") }}</span>
      <span class="text-end width1-5">{{ $t("操作") }}</span>
    </view>
    <template v-if="orderList">
      <view class="th td" v-for="(item, index) in orderList" :key="index">
        <span class="text-left width1">{{ $t("市价") }}</span>
        <span class="width2">2024-05-12 10:11:11</span>
        <span class="width1-5">3000.1212</span>
        <span class="width1-5">100</span>
        <span class="text-end width1-5">{{ $t("买入") }}</span>
      </view>
    </template>
    <Empty class="empty" :placeholder="$t('当前没有委托订单')"></Empty>
  </view>
</template>

<script setup>
import { ref } from "vue";
defineProps({
  orderList:{
    type:Array,
    default:()=>[]
  }
})

</script>

<style scoped  lang="scss">
.order-sj-list {
  width: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding-top: 20rpx;
}
.th {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 20rpx;
  font-size: 22rpx;
  color: #5f5d6a;
  span {
    text-align: center;
  }
  .text-left {
    text-align: left;
  }
  .text-end {
    text-align: end;
  }
}
.width1 {
  flex: 1;
}
.width1-5 {
  flex: 2;
}
.width2 {
  flex: 3;
}
.td {
  color: #999999;
}
.one-b-1px {
  border-bottom: 1rpx solid #efefef;
}
.empty {
  margin-top: 80rpx;
}
</style>