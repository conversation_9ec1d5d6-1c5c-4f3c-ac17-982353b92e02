<template>
  <view class="order-xj-list">
      <view class="list-wrapper">
        <view class="item-wrapper" v-for="item in orderList" :key="item.id">
          <view class="top-wrapper">
            <view class="title-wrapper">
              <span class="title">{{item.code}}/{{item.orderAmountCurrency}}</span>
              <template v-if="item.shortLong==0">
                <view class="tag">{{$t("做空")}}/{{item.leverage}}x</view>
              </template>
              <template v-else>
                <view class="tag2">{{$t("做多")}}/{{item.leverage}}x</view>
              </template>
            </view>
            <span class="time">{{ useFormatTime(item.createTime) }}</span>
          </view>
          <view class="bom-wrapper">
            <view class="info-wrapper">
              <span class="label"> {{$t("价格")}}</span>
              <span class="data">{{useFormatMoney(item.orderPrice)}}</span>
            </view>
            <view class="info-wrapper">
              <span class="label">{{$t("交易金额")}}</span>
              <span class="data">{{ useFormatMoney(item.orderAmount) }}</span>
            </view>
            <view class="info-wrapper" @click="revokeItem(item)">
              <span class="label">{{$t("操作")}}</span>
              <span class="data">{{$t("撤销")}}</span>
            </view>
          </view>

        </view>

      </view>
    <Empty v-if="orderList&&orderList.length==0&&ispEmptyImg"></Empty>
  </view>

</template>

<script setup>
import { useFormatMoney,useFormatTime } from "@/common/utils";
  import {ref} from "vue";
  import { t } from "@/hooks/useI18n";
defineProps({
  orderList:{
    type:Array,
    default:()=>[]
  },
  ispEmptyImg:{
    type:Boolean,
    default:true
  }
})

const emit =  defineEmits(["revokeItem"]);
function revokeItem(item){
  uni.showModal({
    title: t("提示"),
    content: t("是否撤销"),
    cancelText: t("uni.showModal.cancel"),
    confirmText: t("uni.showModal.confirm"),
    success:async function (res) {
      if (res.confirm) {
        emit("revokeItem",item)
      } else if (res.cancel) {
        console.log("用户点击取消");
      }
    },
  });

 
}

</script>

<style scoped  lang="scss">
  .order-xj-list{
    width: 100%;
    display: flex;
    flex-direction: column;
    padding-top: 20rpx;
    .list-wrapper{
      display: flex;
      flex-direction: column;
      .item-wrapper{
        display: flex;
        flex-direction: column;
        padding: 30rpx;
        border-top: 1rpx solid #F1F0F5;
        .top-wrapper{
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;
        }
        .title-wrapper{
          display: flex;
          flex-direction: row;
          align-items: center;
          .title{
            color: #000;
            font-size: 32rpx;
            font-weight: bold;
          }
          .tag{
            font-size: 20rpx;
            padding: 6rpx 4rpx;
            background: var(--common-negative-color);
            color:var(--common-negative-color);
            background: rgba(251, 89, 114, 0.10);
            margin-left: 10rpx;
          }
          .tag2{
            font-size: 20rpx;
            padding: 6rpx 4rpx;
            background: rgba(5, 192, 111, 0.1);
            color:var(--common-positive-color);
            margin-left: 10rpx;
          }
        }
        .time{
          color: #979797;
          font-size: 22rpx;
        }
      }
      .bom-wrapper{
        display: flex;
        flex-direction: row;
        margin-top: 32rpx;
        .info-wrapper{
          display: flex;
          flex-direction: column;
          flex: 1;
          .label{
            font-size: 22rpx;
            color: #979797;
          }
          .data{
            font-size: 24rpx;
            color: #000000;
            font-weight: bold;
            margin-top: 10rpx;
          }
        }
      }
    }
  }

</style>