<template>
  <view class="order-set-input">
    <view class="tab-container">
      <view
        v-for="(item, index) in tabList"
        :class="tabAcitve == item.value ? 'tab-wrapper-active' : 'tab-wrapper'"
        @click="handleTab(item)"
        :key="index"
      >
        {{ item.label }}
      </view>
    </view>
    <uni-easyinput
      class="input"
      v-if="tabAcitve == 1"
      :styles="{}"
      :min="0"
      placeholderStyle="color:#979797;font-size:26rpx"
      :inputBorder="false"
      trim="all"
      :placeholder="$t('价格')"
      type="number"
      v-model="price"
    />
    <view class="radio-box-warpper">
      <view
        :class="['radio', { 'radio-active': item == radioActive }]"
        v-for="(item, index) in radioList"
        @click="handleRadio(item)"
        :key="index"
      >
        {{ item }}X
      </view>
    </view>
    <uni-easyinput
      class="input"
      :styles="{}"
      placeholderStyle="color:#979797;font-size:26rpx"
      :inputBorder="false"
      trim="all"
      :min="0"
      v-model="amount"
      :placeholder="$t('交易金额')"
      type="number"
    />
    <!--view class="silder-wrapper">
      <kw-slider></kw-slider>
    </view -->

    <view class="money-item-wrapper mt30">
      <span class="label">{{ $t("保证金") }}</span>
      <span class="money">{{ useFormatMoney(getMargin) }}{{ config.currencyUnit }}</span>
    </view>
    <view class="money-item-wrapper">
      <span class="label">{{ $t("手续费") }}</span>
      <span class="money">{{useFormatMoney(getFeeRate)}}{{ config.currencyUnit }}</span>
    </view>
    <view class="money-item-wrapper">
      <span class="label">{{ $t("可开多") }}</span>
      <span class="money">{{useFormatMoney(getCanOpenALot)}} {{ config.currencyUnit }}</span>
    </view>
    <view class="money-item-wrapper">
      <span class="label">{{ $t("余额") }}</span>
      <span class="money">{{useFormatMoney(user.usdtBalance)}} {{ config.currencyUnit }}</span>
    </view>
    <view class="btn-wrapper">
      <view
        @click="handlSubmit"
        :class="type == constant.BUY_UP ? 'bg1' : 'bg2'"
        >{{ type == constant.BUY_UP ? $t("买入") : $t("买入") }}</view
      >
    </view>
  </view>
</template>

<script setup>
import { ref, defineProps, computed, watch, watchPostEffect } from "vue";
import constant from "@/common/constant";
import { t } from "@/hooks/useI18n";
import BigNumber from 'bignumber.js';
import { useFormatMoney } from "@/common/utils";
import config from "@/common/config";

const emit = defineEmits(["changeType", "comfirm"]);

import { EventBus } from '@/common/eventBus';
import { onLoad } from "@dcloudio/uni-app";



const props = defineProps({
  type: {
    type: Number,
    default: constant.BUY_UP,
  },
  radioList: {
    type: Array,
    default: () => [],
  },
  user: {
    type: Object,
    default: () => {
      usdtBalance:0
    },
  },
  feeRate: {
    type: [String,Number],
    default: 0
  },
  minBuy: {
    type: [String,Number],
    default: 0
  },
});

//市价 限价
const tabAcitve = ref(0);
const tabList = computed(() => {
  return [
    { label: t("市价"), value: 0 },
    { label: t("限价"), value: 1 },
  ];
});
//倍数
// const radioList = computed(()=>{
//   return [
//     { label: t("5倍"), value: 1 },
//     { label: t("10倍"), value: 2 },
//     { label: t("20倍"), value: 3 },
//     { label: t("50倍"), value: 4 },
//     { label: t("100倍"), value: 5 },
//   ]
// });
const radioActive = ref(null);
const handleTab = (item) => {
  tabAcitve.value = item.value;
  if(item.value == 1){
    price.value = _price.value
  }
  emit("changeType", item.value);
};
const handleRadio = (item) => {
  radioActive.value = item;
};
const handlSubmit = () => {
  const params = {
    "code": "",
    "orderAmount": amount.value,
    "amountCurrency": 'USDT',
    "orderPrice": price.value,
    "shortLong": props.type,
    "leverage": radioActive.value,
    "priceType": tabAcitve.value,
    "orderType": 0
  }
 const max = new BigNumber(getMargin.value).comparedTo(props.user.usdtBalance);
//  const max = new BigNumber(getMargin.value).plus(getFeeRate.value).comparedTo(props.user.usdtBalance);
//  console.log(new BigNumber(0).comparedTo(1)) -1  小
//  console.log(new BigNumber(1).comparedTo(1)) 0 等
//  console.log(new BigNumber(2).comparedTo(1)) 1 大
 const min = new BigNumber(props.minBuy).comparedTo(amount.value || 0);

 if(min == 1){
    uni.showToast({
      title: t('不得低于最小金额') + props.minBuy,
      icon: 'none'
    })
    return
  }
  if(max == 1){
    uni.showToast({
      title: t('余额不足'),
      icon: 'none'
    })
    return
  }

  emit("comfirm",params);
  amount.value = null;
};


const getCanOpenALot = computed(()=>{
  // 公式: 可用余额 * 杠杆倍数 - 可用余额 * 手续费率 * 杠杆倍数^2
  const balance = new BigNumber(props.user.usdtBalance || 0); // 可用余额
const leverage = new BigNumber(radioActive.value || 1);     // 杠杆倍数，给1作为默认值避免乘以0
const feeRate = new BigNumber(props.feeRate || 0).dividedBy(100); // 手续费率，除以100

if (leverage.isLessThanOrEqualTo(0)) {
  return "0"; // 如果杠杆倍数小于等于0，直接返回0
}

// 中间值计算，避免重复操作
const balanceTimesLeverage = balance.multipliedBy(leverage);
const leverageSquared = leverage.pow(2);
const feeAmount = balance.multipliedBy(feeRate).multipliedBy(leverageSquared);

// 公式: 可用余额 * 杠杆倍数 - 可用余额 * 手续费率 * 杠杆倍数^2
const result = balanceTimesLeverage.minus(feeAmount);

return result.toFixed(); // 返回结果字符串，避免精度丢失
})

watch(() => props.radioList, (newVal) => {
  if(newVal.length) {
    if(!radioActive.value){
      radioActive.value = newVal[0];
    }
  }
}, { deep: true });

const amount = ref(null);
const getFeeRate = computed(()=>{
  return new BigNumber(props.feeRate || 0).div(100).times(amount.value || 0).toFixed(2)
})
const getMargin = computed(()=>{
  return new BigNumber(amount.value || 0).div(radioActive.value || 1).toFixed(2)
})

const price = ref(0)
const _price = ref(0)
onLoad(()=>{
  EventBus.on('setPrice', (tempPrice) => {
    price.value = tempPrice
  })

  EventBus.on('setHyPrice', (tempPrice) => {
    _price.value = tempPrice;
  })

})

</script>

<style scoped lang="scss">
.order-set-input {
  display: flex;
  flex-direction: column;
}
.tab-container {
  display: flex;
  flex-direction: row;
  color: #5f5d6a;
  padding-left: 10rpx;
  border-bottom: 1rpx solid #efefef;
  .tab-wrapper {
    margin-right: 30rpx;
  }
}
.tab-wrapper-active {
  margin-right: 30rpx;
  color: black !important;
  font-weight: bold;
  //border-bottom: 1rpx solid #000;
}
.tab-wrapper-active::after {
  content: "";
  width: 34rpx;
  height: 4rpx;
  display: block;
  margin: 12rpx auto 0rpx;
  background: #000000;
}
.radio-box-warpper {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 20rpx;
  .radio {
    padding: 5rpx 13rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 22rpx;
    border-radius: 8rpx;
    color: #8a8992;
    border: 2rpx solid #efefef;
    margin-right: 10rpx;
    box-sizing: border-box;
    white-space: nowrap;
  }
  .radio-active {
    border: 2rpx solid var(--common-positive-color) !important;
    color: var(--common-positive-color);
  }
}
.input {
  width: 100%;
  height: 90rpx;
  border-radius: 10px;
  background: #f7f8fa !important;
  margin-top: 20rpx;
}
.input ::v-deep .uni-easyinput__content {
  background: #f7f8fa !important;
}
.input ::v-deep input {
  color: #333333 !important;
  font-size: 26rpx;
  font-weight: normal !important;
}
.money-item-wrapper {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  font-size: 24rpx;
  margin-bottom: 10rpx;
  .label {
    color: #999;
  }
  .money {
    font-size: 24rpx;
  }
}
.mt30 {
  margin-top: 30rpx;
}
.btn-wrapper {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 10rpx;
  view {
    width: 435rpx;
    height: 70rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 100rpx;
    font-size: 26rpx;
    color: white;
  }
}
.bg1 {
  background: var(--common-positive-color) !important;
}
.bg2 {
  background: var(--common-negative-color) !important;
}
.silder-wrapper {
  padding-left: 20rpx;
  padding-right: 20rpx;
  margin-top: 0rpx;
}
</style>