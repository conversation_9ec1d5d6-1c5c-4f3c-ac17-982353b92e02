<template>
  <view class="home-page-class">
    <Skeleton v-if="skeletonShow"></Skeleton>
    <view  class="tui-skeleton">
      <view class="nav-class">
        <view class="left-class tui-skeleton-fillet">{{ $t("首页") }}</view>
        <view class="right-class tui-skeleton-circular">
          <image
            v-if="customServiceUrl.length"
            src="@/static/images/index/20.png"
            class="avatar-class"
            @click="open"
          ></image>
          <image
            :src="user.avatar || defaultAvatar"
            class="avatar-class"
            @click="goPersonalCenter"
          ></image>
        </view>
      </view>
      <AssetHead :user="user" class="tui-skeleton-fillet"/>
      <view class="carousel-class">
        <view class="warp">
          <!-- <image src="@/static/images/index/4.png" class="img-class"></image> -->
          <!-- <uni-swiper-dot
          class="uni-swiper-dot-box"
          field="content"
          :current="current"
          :info="carouselList"
          :dots-styles="dotsStyles"
        > -->
          <swiper class="swiper-box tui-skeleton-fillet" circular :interval="5000" autoplay>
            <swiper-item v-for="(item, index) in leftBnaner" :key="index" @click="navJump(item)">
              <view class="swiper-item-wrapper">
                <image :src="item.img" class="img-class"></image>
                <view class="info-wrapper">
                     <view class="label">{{$t("埃隆·马斯克")}}</view>
                     <view class="desc"> {{$t("全球独家品牌大使")}}</view>
                </view>
              </view>

            </swiper-item>
          </swiper>
          <!-- </uni-swiper-dot> -->
        </view>
        <view class="warp">
          <uni-swiper-dot
            class="uni-swiper-dot-box"
            field="content"
            :current="current"
            :info="rightBnaner"
            :dots-styles="dotsStyles"
          >
            <swiper
              class="swiper-box tui-skeleton-fillet"
              @change="change"
              circular
              :interval="5000"
              autoplay
            >
              <swiper-item v-for="(item, index) in rightBnaner" :key="index">
                  <CarouselItem :item="item"></CarouselItem>
              </swiper-item>
            </swiper>
          </uni-swiper-dot>
        </view>
      </view>
      <view class="recommended-class">
        <uni-swiper-dot
          class="uni-swiper-dot-box"
          field="content"
          :current="currentRecommended"
          :info="tradePairHotList"
          :dots-styles="dotsRecommendedStyles"
          mode="round"
        >
          <swiper
            class="swiper-box tui-skeleton-fillet"
            @change="changeRecommended"
            circular
            :interval="5000"
          >
            <swiper-item v-for="(item, index) in tradePairHotList" :key="index">
              <view class="recommended-item-warp">
                <RecommendedItem
                  v-for="i in item"
                  :key="i.id"
                  :item="i"
                ></RecommendedItem>
              </view>
            </swiper-item>
          </swiper>
        </uni-swiper-dot>
      </view>
      <view class="tabs-line-class">
        <z-tabs
          :class="{ sticky: isSticky }"
          :list="tabList"
          ref="header"
          bar-height="0"
          @change="tabChange"
          class="header-class tui-skeleton-fillet"
        />
      </view>

      <view class="list-warp-class">
        <z-paging
          ref="paging"
          v-model="dataList"
          use-page-scroll
          @query="queryList"
          auto-show-back-to-top
          :auto-scroll-to-top-when-reload="false"
          :auto-clean-list-when-reload="false"
          :loading-more-enabled="false"
        >
          <view class="currency-list">
            <view class="currency-warp-class">
              <CurrencyItem
                v-for="item in dataList"
                :key="item.id"
                :item="item"
              ></CurrencyItem>
            </view>
          </view>
        </z-paging>
      </view>
    </view>
    <!-- <view class="currency-list">
      <z-tabs :list="tabList" @change="tabChange" class="tabs-line-class"/>
      <view class="currency-warp-class">
        <CurrencyItem v-for="item in 30" :key="item"></CurrencyItem>
      </view>
    </view> -->

    <uni-popup
      ref="refCustomServicePopup"
      type="bottom"
      style="z-index: 999"
      border-radius="20rpx 20rpx 0 0"
    >
      <view class="custom-service-popup-warp">
        <view class="title-class">
          <view class="title-text">{{ $t("客服") }}</view>
          <image
            class="close-img"
            src="@/static/images/index/21.png"
            @click="close"
          ></image>
        </view>
        <view class="custom-service-content">
          <view
            v-for="item in customServiceUrl"
            :key="item.id"
            @click="goCustomerService(item.serviceUrl)"
            class="custom-service-item"
          >
            <image :src="item.ico" class="img-class"></image>
            <view class="text-class">{{ item.name }}</view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>
<script setup>
import config from "@/common/config";
import { useFormatMoney, splitArrayIntoChunks } from "@/common/utils";
import { ref, nextTick, onMounted, computed } from "vue";
import {
  onLoad,
  onPageScroll,
  onReachBottom,
  onReady,
  onShow,
} from "@dcloudio/uni-app";
import useZPaging from "@/uni_modules/z-paging/components/z-paging/js/hooks/useZPaging.js";
import tui from "@/common/httpRequest.js";
import { t } from "@/hooks/useI18n";
import { TabStatus } from "@/common/enum";
import {SHARE_CODE} from "@/common/cacheEnums";
import ceche from "@/common/cache";

const isSticky = ref(false);
const skeletonShow = ref(true);
const headerHeight = ref(0);
const refCustomServicePopup = ref(null);
const header = ref(null);
const paging = ref(null);
useZPaging(paging);
const dataList = ref([]);
const defaultAvatar = new URL("@/static/images/index/2.png", import.meta.url)
  .href;
const current = ref(0);
const currentRecommended = ref(0);
const dotsStyles = {
  backgroundColor: "rgba(255, 255, 255, 0.30)",
  border: "2rpx rgba(255, 255, 255, 0.30) solid",
  color: "#fff",
  selectedBackgroundColor: "#FFF",
  selectedBorder: "2rpx #FFF solid",
};
const dotsRecommendedStyles = {
  backgroundColor: "rgba(237, 237, 237, 0.30)",
  border: "2rpx rgba(237, 237, 237, 0.30) solid",
  color: "#fff",
  selectedBackgroundColor: "#D9D9D9",
  selectedBorder: "2rpx #D9D9D9 solid",
};

// const { assetType } = config;

// 数据源数组，支持形如['tab1','tab2']的格式或[{name:'tab1',value:1}]的格式
const tabList = ref([]);
const tabIndex = ref(0);

function open() {
  refCustomServicePopup.value.open();
}
function close() {
  refCustomServicePopup.value.close();
}

const onScroll = ({ scrollTop }) => {
  // isSticky.value = scrollTop > 50; // 设置粘性阈值
  if (scrollTop > headerHeight.value) {
    isSticky.value = true;
  } else {
    isSticky.value = false;
  }
};

onPageScroll((e) => {
  // onScroll(e);
});

onReady(() => {
  // const query = uni.createSelectorQuery().in(this);
  // query
  //   .select(".header-class")
  //   .boundingClientRect((data) => {
  //     if (data) {
  //       headerHeight.value = data.top; // 获取元素距离页面顶部的距离
  //     }
  //   })
  //   .exec();
  getBanner();

  tradePairAll();

  getCustomerService();
});



onShow(() => {
  //判断是否登录
  tabList.value = TabStatus();
  // tabIndex.value = 0;
  if (tui.getToken()) {
    getUser();
  }
});

const leftBnaner = ref([]);
const rightBnaner = ref([]);

//获取轮播图
function getBanner() {
  //如果是放在show中，那么需要清空一下数组
  tui.request("/app-api/system/banner/get", "GET").then((res) => {
    res.data.forEach((item) => {
      if (item.location == 1) {
        leftBnaner.value.push(item);
      } else {
        rightBnaner.value.push(item);
      }
    });
  });
}
//获取用户信息

const user = ref({
  usdtBalance: 0,
  usdtFrozenBalance: 0,
});
function getUser() {
  tui.request("/app-api/member/user/get", "GET").then((res) => {
    user.value = res.data;
  });
}
const tradePairHotList = ref([]);
const tradePairAllList = ref([]);
//获取热门交易对
function tradePairAll() {
  tui.request("/app-api/exchange/trade-pair/hot", "GET").then((res) => {
    tradePairAllList.value = res.data;
    const hotList = res.data.filter((item) => item.hot);
    tradePairHotList.value = splitArrayIntoChunks(hotList);
  });
}

function queryList(pageNo, pageSize) {
  if (tabList.value[tabIndex.value].value == -1) {
    // 获取会员收藏的交易对
    tui
      .request("/app-api/member/favorite/trade-pair/list", "GET")
      .then((res) => {
        paging.value.complete(res.data);
      }).finally(() =>{
        skeletonShow.value = false;
      });
  } else {
    const params = {
      assetType: tabList.value[tabIndex.value].value,
      tradeType:3
    };
    tui
      .request("/app-api/exchange/trade-pair/all", "GET", params)
      .then((res) => {
        paging.value.complete(res.data);
      }).finally(() =>{
        skeletonShow.value = false;
      });
  }

  // tui.request.queryList(params).then(res => {
  // 	// 将请求的结果数组传递给z-paging
  // 	paging.value.complete(res.data.list);
  // }).catch(res => {
  // 	// 如果请求失败写this.$refs.paging.complete(false);
  // 	// 注意，每次都需要在catch中写这句话很麻烦，z-paging提供了方案可以全局统一处理
  // 	// 在底层的网络请求抛出异常时，写uni.$emit('z-paging-error-emit');即可
  // 	paging.value.complete(false);
  // })
}

function change(e) {
  current.value = e.detail.current;
}
function changeRecommended(e) {
  currentRecommended.value = e.detail.current;
}
function tabChange(index) {
  tabIndex.value = index;
  paging.value.reload();
}

function goPersonalCenter() {
  uni.navigateTo({
    url: "/pages/personalCenter/index",
  });
}

const customServiceUrl = ref([]);
function getCustomerService() {
  tui
    .request("/app-api/system/config/service-url-list", "GET")
    .then(({ data }) => {
      customServiceUrl.value = data;
    });
}

function goCustomerService(url) {
  close();
  if (url) {
    window.open(url);
    // uni.navigateTo({
    //   url: "/pages/webview/index?url=" + url,
    // });
  }
}

function navJump(item){
  // 链接类型，1内部，2外部
  if(item.linkType ==1 && item.linkUrl){
    uni.navigateTo({
      url:item.linkUrl
    })
  }

  if(item.linkType ==2 && item.linkUrl){
    window.open(item.linkUrl)
  }

}

onLoad(e=>{
  if(e.code){
    ceche.set(SHARE_CODE, e.code)
  }
})
</script>
<style scoped lang="scss">
.custom-service-popup-warp {
  background-color: #fff;
  .custom-service-content {
    padding: 26rpx 30rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    column-gap: 80rpx;
    row-gap: 20rpx;
    .custom-service-item {
      width: 216rpx;
      height: 216rpx;
      border-radius: 32rpx;
      background: rgba(245, 245, 245, 1);
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      row-gap: 20rpx;

      font-size: 24rpx;
      font-weight: 400;
      letter-spacing: 0rpx;
      line-height: 28.12rpx;
      color: rgba(0, 0, 0, 1);

      .img-class {
        width: 50rpx;
        height: 50rpx;
      }
    }
  }

  .title-class {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 26rpx 30rpx;
    box-sizing: border-box;
    .title-text {
      font-size: 32rpx;
      font-weight: bolder;
      letter-spacing: 0rpx;
      line-height: 46.4rpx;
      color: rgba(0, 0, 0, 1);
    }
    .close-img {
      width: 20rpx;
      height: 20rpx;
    }
  }
}
.home-page-class {
  // width: 750rpx;
  height: 456rpx;
  background-image: url("@/static/images/index/1.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  .sticky {
    position: fixed !important;
    top: 0;
  }
  .list-warp-class {
    padding-bottom: 100rpx;
    position: relative;
    z-index: 0;
  }
  .tabs-line-class {
    // border-bottom: 2rpx solid #f9f9f9;
    box-sizing: border-box;
    position: sticky;
    top: 0;
    z-index: 10;
  }
  .currency-list {
    // padding-top: 36rpx;
    position: relative;

    .currency-warp-class {
      display: flex;
      justify-items: center;
      flex-direction: column;
      row-gap: 48rpx;
      padding: 24rpx 36rpx;
    }
  }
  .recommended-class {
    margin: 32rpx 0;
    .swiper-box {
      padding: 0 32rpx;
      height: 204rpx;
    }
    .recommended-item-warp {
      border-radius: 20rpx;
      background: #fff;
      padding: 42rpx 0;

      box-shadow: 0rpx 4rpx 30rpx 0rpx rgba(0, 0, 0, 0.06);
      // border:1rpx solid rgba(0, 0, 0, 0.06);
      box-sizing: border-box;
      display: flex;
      justify-content: space-around;
      align-items: center;
      column-gap: 60rpx;
    }
  }
  .carousel-class {
    margin-top: 80rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 32rpx;
    box-sizing: border-box;
    column-gap: 26rpx;
    .warp {
      width: 330rpx;
      height: 360rpx;
      border-radius: 20rpx;
      flex-shrink: 0;
      ::v-deep .uni-swiper__dots-box {
        justify-content: flex-start;
        padding-left: 32rpx;
      }
      .swiper-box {
        width: 330rpx;
        height: 360rpx;
      }
    }
    .swiper-item-wrapper{
      width: 330rpx;
      height: 360rpx;
      flex-shrink: 0;
      border-radius: 20rpx;
      display: flex;
      flex-direction: column;
      position: relative;
      .info-wrapper{
        width: 100%;
        color: white;
        position: absolute;
        bottom: 0;
        background: rgba(0,0,0,0.5);
        border-radius: 20rpx !important;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        padding: 32rpx 32rpx 40rpx 30rpx;
        .label{
          font-size: 28rpx;
          font-weight: 600;
        }
        .desc{
          color: rgba(255, 255, 255, 0.62);
          font-size: 20rpx;
          margin-top: 20rpx;

        }
      }
    }
    .img-class {
      width: 330rpx;
      height: 360rpx;
      flex-shrink: 0;
      border-radius: 20rpx;
    }
  }
  .nav-class {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 32rpx;
    padding-top: 36rpx;
    .left-class {
      color: #000;
      text-align: center;
      font-family: "PingFang SC";
      font-size: 36rpx;
      font-style: normal;
      font-weight: 600;
      line-height: 32rpx; /* 88.889% */
    }
    .right-class {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      column-gap: 48rpx;

      .avatar-class {
        width: 56rpx;
        height: 56rpx;
        flex-shrink: 0;
        border-radius: 100rpx;
      }
    }
  }
}
.mask-wrapper{
  background: rgba(0,0,0,0.5);
}
</style>