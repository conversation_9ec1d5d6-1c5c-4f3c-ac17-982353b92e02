<template>
  <view class="transaction-container">
    <!-- 头部导航   -->
    <TransactionHead :currency="currency" @handleCurrencySelect="handleCurrencySelect"></TransactionHead>
    <!-- 合约/期权tab   -->
    <view class="tabs-wrapper-class">
      <view @click="handleTab(constant.TRAN_HY)">
        <view :class="{ 'tab-active': tabsActive == constant.TRAN_HY }">{{ $t("合约") }} </view>
      </view>
      <view @click="handleTab(constant.TRAN_QQ)">
        <view :class="{ 'tab-active': tabsActive == constant.TRAN_QQ }">{{ $t("期权") }}</view>
      </view>
    </view>
    <!-- 数据信息   -->
    <TransactionInfo :currency="currency"></TransactionInfo>
    <!-- 图表   -->
    <Klinechart :currency="currency"></Klinechart>
    <!-- 委托订单   -->
    <TransactionCommissionOrder :currency="currency"></TransactionCommissionOrder>
    <!-- 买卖按钮   -->
    <view class="btn-wrapper-class">
      <view class="bg1" @click="handleBuy(constant.BUY_UP)">{{ tabsActive == constant.TRAN_HY ? $t("买入/做多") : $t("买涨") }}
      </view>
      <view class="bg2" @click="handleBuy(constant.BUY_DOWN)">{{ tabsActive == constant.TRAN_HY ? $t("卖出/做空") : $t("买跌") }}
      </view>
    </view>
    <OrderSure :orderType="buyType" :currency="currency" ref="orderSureRef" @confirm="OrderCofrim"></OrderSure>
    <TransactionCountDown class="transaction-count-down" :orderType="buyType" :currency="currency"
      ref="transactionCountDownRef"></TransactionCountDown>
      <CurrencySelect ref="currencySelect" @currencyChange="currencyChange" ></CurrencySelect>

  </view>
</template>
<script setup>
import config from "@/common/config"
import constant from "@/common/constant";
import { inject, ref } from 'vue'
import { EventBus } from '@/common/eventBus';
import { onLoad } from "@dcloudio/uni-app";
// import TransactionHead from "@/components/TransactionHead/TransactionHead";
// import TransactionInfo from "@/components/transaction/TransactionInfo";
// import TransactionTimeTab from "@/components/transaction/TransactionTimeTab";
// import TransactionChartView from "@/components/transaction/TransactionChartView";
// import TransactionCommissionOrder from "@/components/transaction/TransactionCommissionOrder";
// import OrderSure from "@/components/transaction/OrderSure";
// import TransactionCountDown from "@/components/TransactionCountDown/TransactionCountDown";
// import Klinechart from "@/components/Klinechart/Klinechart";
const currency = ref({ name: "GOLD", code: "XAUUSD" }) //币种
var tabsActive = ref(constant.TRAN_QQ)

const buyType = ref(1) //1 买涨 2买跌
//限时秒合约生成订单
const orderSureRef = ref(null)
const currencySelect = ref(null)

const transactionCountDownRef = ref(null)

const websocketClient = inject("websocketClient");
//点击合约/期权tab
const handleTab = (tab) => {
  tabsActive.value = tab
}
//货币切换
const currencyChange = (e) => {
  currency.value = e
  getTransactionData()
  //一些处理数据
}

const handleCurrencySelect=()=>{
     currencySelect.value.open();
   }

onLoad(({ code, name }) => {
  if (code && name) {
    currency.value.code = code
    currency.value.name = name
  } else {
    currency.value = config.defaultTradePair || { name: "GOLD", code: "XAUUSD" }
  }
  getTransactionData();
})
//获取交易数据
const getTransactionData = (code) => {
  const transaction = {
    type: "cmd",
    cmd: 1000,
    data: {
      code: currency.value.code
    }
  };
  uni.setStorageSync("WS-TRAN-CURRENCY-CODE", currency.value.code)
  websocketClient.sendMessage(JSON.stringify(transaction));
}

//购买限时
function handleBuy(type) {
  if (tabsActive.value == constant.TRAN_QQ) {
    buyType.value = type
    orderSureRef.value.open()
  } else {
    uni.redirectTo({ url: "/pages/orderCreate/index?type=" + tabsActive.value + '&code=' + currency.value.code + '&name=' + currency.value.name })
  }

}

function OrderCofrim(orderId) {
  transactionCountDownRef.value.init(orderId)
}
</script>


<style scoped lang="scss">
.transaction-count-down {
  position: fixed;
  z-index: 9999;
  right: 30rpx;
  bottom: 300rpx;
}

.transaction-container {
  width: 100%;
  height: 100%;
  padding-top: 130rpx;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 220rpx;
  box-sizing: border-box;
}

.tabs-wrapper-class {
  width: 686rpx;
  height: 80rpx;
  flex-shrink: 0;
  border-radius: 16rpx;
  border: 1rpx solid #F9F9F9;
  background: #F8F8F8;
  display: flex;
  flex-direction: row;
  align-items: center;
  box-sizing: border-box;
  padding-left: 7rpx;
  padding-right: 7rpx;

  view {
    flex: 1;
    color: #999;
    font-size: 28rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.tab-active {
  border-radius: 8rpx !important;
  width: 300rpx;
  height: 65rpx;
  background: #FFF !important;
  color: #000000 !important;
  font-weight: bold !important;
  display: flex;
  justify-content: center;
  align-items: center;
}

.btn-wrapper-class {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  background: white;
  padding-bottom: 50rpx;
  padding-top: 20rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 2;

  view {
    display: flex;
    width: 330rpx;
    height: 76rpx;
    justify-content: center;
    align-items: center;
    color: white;
    font-size: 28rpx;
    font-weight: bold;
    border-radius: 38rpx;
  }

  .bg1 {
    background: var(--common-positive-color)
  }

  .bg2 {
    background: var(--common-negative-color);
    margin-left: 26rpx;
  }
}
</style>