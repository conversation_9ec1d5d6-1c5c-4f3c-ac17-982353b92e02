<template>
  <view class="market-class">
    <Skeleton v-if="skeletonShow"></Skeleton>
    <view class="tui-skeleton">
      <view class="tui-skeleton-rect" style="width: 100%;height: 88rpx;"></view>
      <view v-for="(item,index) in 6" :key="index" style="margin-top: 20rpx;display: flex;justify-content: space-around;align-items: center;column-gap: 20rpx;padding: 20rpx;">
        <view class="tui-skeleton-rect" style="width: 100%;height:120rpx;"></view>
        <view class="tui-skeleton-rect" style="width: 100%;height:120rpx;"></view>
        <view class="tui-skeleton-rect" style="width: 100%;height:120rpx;"></view>
      </view>
    </view>
    <z-paging  ref="paging" v-model="dataList" @query="queryList" :loading-more-enabled="false">
      <!-- 需要固定在顶部不滚动的view放在slot="top"的view中，如果需要跟着滚动，则不要设置slot="top" -->
      <!-- 注意！此处的z-tabs为独立的组件，可替换为第三方的tabs，若需要使用z-tabs，请在插件市场搜索z-tabs并引入，否则会报插件找不到的错误 -->
      <template #top>
        <view class="head-class">
          <view class="tabs-lin-warp-class">
            <z-tabs
              :list="tabList"
              @change="tabChange"
              class="tabs-line-class"
              bar-height="0"
            >
            </z-tabs>
            <view class="search-warp-icon-class">
              <image
                src="@/static/images/market/1.png"
                class="search-icon-class"
                @click="searchFun"
              ></image>
            </view>
          </view>
          <z-tabs
            :list="tabContractList"
             bar-height="0"
            @change="changeTabContract"
            class="tabs-contract-line-class"
          >
          </z-tabs>
          <view class="table-td-class">
            <SortIcon class="item1-class" :labelName="$t('名称')" sorting="name" @sort-changed="sortChanged"/>
            <SortIcon class="item2-class" :labelName="$t('最新价格')" sorting="currentPrice" @sort-changed="sortChanged"/>
            <SortIcon :labelName="$t('涨跌')" sorting="percentage" @sort-changed="sortChanged"/>
          </view>
        </view>
      </template>
      <!-- 如果希望其他view跟着页面滚动，可以放在z-paging标签内 -->
      <view class="list-warp-class">
        <CurrencyItem v-for="item in dataList" :key="item.id" :item="item"></CurrencyItem>
      </view>
    </z-paging>
  </view>
</template>

<script setup>
import { computed, ref } from "vue";
import config from "@/common/config";
import tui from "@/common/httpRequest.js";
import { t } from "@/hooks/useI18n";
import { sortArrayByKey } from "@/common/utils";
import { onShow } from "@dcloudio/uni-app";
import { TabStatus } from "@/common/enum";
const skeletonShow = ref(true);
const dataList = ref([]);
const paging = ref(null);

// const tabList = ref(config.assetType);
const tabList = ref([]);


const tabIndex = ref(0);
function tabChange(index) {
  tabIndex.value = index;
  paging.value.reload();
}
//排序
function sortChanged(key,sort){
  const sortedList = sortArrayByKey(JSON.parse(JSON.stringify(oldDataList.value)), key, sort);
  paging.value.resetTotalData(sortedList);
}

const oldDataList = ref([]);

function queryList(pageNo, pageSize) {
  // 组件加载时会自动触发此方法，因此默认页面加载时会自动触发，无需手动调用
  // 这里的pageNo和pageSize会自动计算好，直接传给服务器即可
  // 模拟请求服务器获取分页数据，请替换成自己的网络请求
  if(tabList.value[tabIndex.value].value == -1){
  // 获取会员收藏的交易对
    tui.request('/app-api/member/favorite/trade-pair/list','GET',).then(res => {
      paging.value.complete(res.data);
      oldDataList.value = res.data;
    }).finally(() =>{
        skeletonShow.value = false;
      });
  }else{
    const params = {
      assetType:tabList.value[tabIndex.value].value,
      tradeType:tabContractIndex.value==0?'3':'1'
    }
    tui.request('/app-api/exchange/trade-pair/all','GET',params).then(res => {
      paging.value.complete(res.data);
      oldDataList.value = res.data;
    }).finally(() =>{
        skeletonShow.value = false;
      });
  }
  // resetTotalData  排序后用这个
  // tui.request.queryList(params).then(res => {
  // 	// 将请求的结果数组传递给z-paging
  // 	paging.value.complete(res.data.list);
  // }).catch(res => {
  // 	// 如果请求失败写this.$refs.paging.complete(false);
  // 	// 注意，每次都需要在catch中写这句话很麻烦，z-paging提供了方案可以全局统一处理
  // 	// 在底层的网络请求抛出异常时，写uni.$emit('z-paging-error-emit');即可
  // 	paging.value.complete(false);
  // })
}

// const tabContractList = ref([t("限时"), t("合约")]);
const tabContractList = computed(()=>{
  // return [t("限时"), t("合约")]
  return [t("限时")]
})
const tabContractIndex = ref(0);
function changeTabContract(index) {
  tabContractIndex.value = index;
  paging.value.reload();
}
function searchFun() {
  // console.log(111);
  uni.navigateTo({
    url:'/pages/search/index'
  })
}


onShow(()=>{
  tabList.value = TabStatus();
  // tabIndex.value = 0;
})
</script>

<style scoped lang="scss">
.market-class {
  .list-warp-class {
    padding: 24rpx 32rpx;
    display: flex;
    justify-content: center;
    flex-direction: column;
    row-gap: 48rpx;
  }
  .head-class {
    position: sticky;
    top: 0;
    width: 750rpx;
    margin: 0 auto;
    background-color: #fff;
    .table-td-class {
      display: flex;
      justify-content: space-between;
      box-sizing: border-box;
      padding: 0 32rpx;
      align-items: flex-end;
      height: 62rpx;
      .item1-class{
        width: 33%;
      }
      .item2-class{
        // width: 23%;
      }
    }
    .tabs-lin-warp-class {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 96rpx;
      position: relative;
      .tabs-line-class {
        padding-right: 154rpx;
      }
      .search-warp-icon-class {
        position: absolute;
        right: 0;
        display: flex;
        width: 154rpx;
        padding: 34rpx 32rpx 34rpx 0rpx;
        margin-left: 32rpx;
        justify-content: flex-end;
        align-items: center;
        gap: 20rpx;
        background: linear-gradient(
          270deg,
          #fff 68.18%,
          rgba(255, 255, 255, 0) 118.83%
        );
        .search-icon-class {
          width: 32rpx;
          height: 32rpx;
          flex-shrink: 0;
        }
      }
    }
    .tabs-contract-line-class {
      border-bottom: 2rpx solid #f9f9f9;
      ::v-deep .z-tabs-list-container {
        justify-content: flex-start;
        .z-tabs-list {
          flex: 0 !important;
        }
        .z-tabs-item-title {
          text-align: center;
          font-family: "PingFang SC";
          font-size: 28rpx;
          font-style: normal;
          line-height: 32rpx; /* 114.286% */
        }
      }
    }
  }
}
</style>