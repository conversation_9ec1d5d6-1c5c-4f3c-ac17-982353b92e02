<template>
  <view class="assets-class">
    <z-paging
      ref="paging"
      v-model="dataList"
      @query="queryList"
      use-page-scroll
      :loading-more-enabled="false"
    >
      <AssetHead shuoDetail :user="user"/>
      <z-tabs
        :list="tabList"
        @change="tabChange"
        class="tabs-contract-line-class"
      />
      <view class="income-class" v-if="tabIndex==0">
        <view class="item-class">
          <view class="label-class" @click="shopTip">
            <view>{{$t('今日收益')}}</view>
            <image src="@/static/images/index/15.png" class="img-class"></image>
          </view>
          <view class="value-class">{{ useFormatMoney(today.profit )}}</view>
        </view>
        <view  class="item-class">
          <view class="label-class">{{$t('今日下单总数')}}</view>
          <view class="value-class">{{ useFormatMoney(today.tradeCount) }}</view>
        </view>
      </view>
      <view class="income-class" v-if="tabIndex==1">
        <view  class="item-class">
          <view class="label-class">{{$t('总盈亏')}}</view>
          <view class="value-class">{{ useFormatMoney(today.totalProfitLoss) }}</view>
        </view>
        <view  class="item-class">
          <view class="label-class">{{$t('持仓估值')}}</view>
          <view class="value-class">{{ useFormatMoney(today.contractValue) }}</view>
        </view>
        <view  class="item-class">
          <view class="label-class">{{$t('原始保证金')}}</view>
          <view class="value-class">{{ useFormatMoney(today.marginTotal) }}</view>
        </view>
        <view  class="item-class">
          <view class="label-class">{{$t('可用本金总额')}}</view>
          <view class="value-class">{{ useFormatMoney(today.validBalance) }}</view>
        </view>
      </view>

      <view class="hold-a-position">
        {{$t('持有仓位',[dataList.length])}}
      </view>
      <template v-if="tabIndex==0">
        <PositionItem v-for="(item,index) in dataList" :item="item" @reload="reload" :key="index"></PositionItem>
      </template>
      <template v-if="tabIndex==1">
        <OrderXjList :orderList="dataList" @revokeItem="revokeItem" :ispEmptyImg="false"></OrderXjList>
      </template>


    </z-paging>
  </view>
</template>
<script setup>
// import { useI18n } from 'vue-i18n';
// const { t } = useI18n();
import { useFormatMoney } from "@/common/utils";
import { ref, nextTick, onMounted, computed } from "vue";
import {
  onLoad,
  onPageScroll,
  onReachBottom,
  onReady,
  onShow,
} from "@dcloudio/uni-app";
import useZPaging from "@/uni_modules/z-paging/components/z-paging/js/hooks/useZPaging.js";
import { t } from "@/hooks/useI18n";
import tui from "@/common/httpRequest.js";
import OrderXjList from "@/pages/orderCreate/weight/OrderXjList";
import { EventBus } from '@/common/eventBus';


const paging = ref(null);
useZPaging(paging);
const dataList = ref([]);
const contractList = ref([]);
const tabIndex = ref(0);
const tabList = computed(()=>{
  return [t("限时"), t("合约")]
  // return [t("限时")]
});

function tabChange(index) {
  tabIndex.value = index;
  if(tabIndex.value==0){
    getToday();
  }else{
    getContract()
  }
    paging.value.reload();
}

function revokeItem(item){
  tui
    .request("/app-api/trade/contract/order-cancel", "POST", {id:item.id})
    .then((res) => {
      if (res.code == 0) {
        uni.showToast({
          title: t("撤单成功"),
          icon: "none",
        });
        paging.value.reload();
      }
    });
}

function queryList(pageNo, pageSize) {
  // 组件加载时会自动触发此方法，因此默认页面加载时会自动触发，无需手动调用
  // 这里的pageNo和pageSize会自动计算好，直接传给服务器即可
  // 模拟请求服务器获取分页数据，请替换成自己的网络请求
  const params = {
    pageNo: 1,
    pageSize: 100,
    type: tabIndex.value ,
  };

  if(tabIndex.value==0){
      tui.request('/app-api/trade/order/time-contract/page','GET',params,false,false,true).then(res => {
      // 将请求的结果数组传递给z-paging
        paging.value.complete(res.data.list);
      }).catch(res => {
        paging.value.complete(false);
      })
  }
  if(tabIndex.value==1){
      tui.request('/app-api/trade/order/contract/order-page','GET',params,false,false,true).then(res => {
      // 将请求的结果数组传递给z-paging
        paging.value.complete(res.data.list);
      }).catch(res => {
        paging.value.complete(false);
      })
    }
}

function shopTip(){
  uni.showModal({
    title: t("说明"),
    content: t("tip1"),
    showCancel: false,
    confirmText: t("知道了"),
  });
}

const user = ref({
  usdtBalance:0,
  usdtFrozenBalance:0,
})
function getUser() {
  tui.request('/app-api/member/user/get','GET','',false,false,true).then(res => {
    user.value  = res.data
  })
}
const today = ref({
  profit:0,
  tradeCount:0,

  contractValue:0,
  totalProfitLoss:0,
  marginTotal:0,
  validBalance:0,
})

function getToday() {
  tui.request('/app-api/trade/total/get-today','GET','',false,false,true).then(({data}) => {
    today.value.profit = data.profit;
    today.value.tradeCount = data.tradeCount;
  })
}

function getContract() {
  tui.request('/app-api/trade/total/contract','GET','',false,false,true).then(({data}) => {
    today.value.contractValue = data.contractValue;
    today.value.totalProfitLoss = data.totalProfitLoss;
    today.value.marginTotal = data.marginTotal;
    today.value.validBalance = data.validBalance;
  })
}



function reload(item){
  // if( paging.value){
  //   paging.value.reload();
  // }
  const list = dataList.value.filter((i)=>i.id!=item.id)
  if(paging.value){
    paging.value.resetTotalData(list);
  }
  getUser();
  getToday();
}


function getCommission(){
  EventBus.on('commission', ({orderNo}) => {
      const list = dataList.value.filter(item => item.orderNo != orderNo)
      paging.value.resetTotalData([...list]);
      getContract()
  });
}

onLoad(()=>{
  getCommission();
})
onShow(()=>{
  getUser();
  if(tabIndex.value==0){
    getToday();
  }else{
    getContract()
  }

  if( paging.value){
    paging.value.reload();
  }

})
</script>

<style scoped lang="scss">
.assets-class {
  .tabs-contract-line-class {
    box-sizing: border-box;
    padding: 0 32rpx;
    margin-top: 40rpx;
    // border-bottom: 2rpx solid #f9f9f9;
    ::v-deep .z-tabs-list-container {
      justify-content: flex-start;
      .z-tabs-list {
        flex: 0 !important;
      }
      .z-tabs-item-title {
        text-align: center;
        font-family: "PingFang SC";
        font-size: 28rpx;
        font-style: normal;
        font-weight: 600;
        line-height: 32rpx; /* 114.286% */
      }
    }
  }
  .hold-a-position{
    margin-left: 32rpx;
    margin-bottom: 14rpx;
    color: #000;
    font-family: "PingFang SC";
    font-size: 28rpx;
    font-style: normal;
    font-weight: bold;
    line-height: 140%; /* 39.2rpx */
    letter-spacing: 0.4rpx;
  }
  .income-class{
    display: grid;
    grid-template-columns: repeat(2,1fr);
    gap: 32rpx;
      justify-content: space-around;
      align-items: center;
      padding: 76rpx 28rpx 72rpx 28rpx;
      
    .item-class{
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      row-gap: 23rpx;
     .label-class{
      display: flex;
      justify-content: center;
      align-items: center;
      column-gap: 8rpx;
      color: #979797;
      text-align: center;
      font-family: "Noto Sans";
      font-size: 22rpx;
      font-style: normal;
      font-weight: 400;
      line-height: 140%; /* 30.8rpx */
      letter-spacing: 0.4rpx;
      .img-class{
        width: 24rpx;
        height: 24rpx;
      }
     }
     .value-class{
      color: #000;
      text-align: center;
      font-family: "Noto Sans";
      font-size: 32rpx;
      font-style: normal;
      font-weight: 700;
      line-height: 140%; /* 44.8rpx */
      letter-spacing: 0.4rpx;
    }
    }

   
  }
}
</style>