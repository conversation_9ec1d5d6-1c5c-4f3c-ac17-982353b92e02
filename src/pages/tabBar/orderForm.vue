<template>
  <view class="order-page-class">
    <z-paging ref="paging" v-model="dataList" @query="queryList">
      <template #top>
        <z-tabs
          :list="tabList"
          @change="tabChange"
          class="tabs-contract-line-class"
          bar-height="0"
        />
      </template>
      <view class="grid-container">
        <view v-for="item in dataList" :key="item" class="grid-item">
          <view class="item-class">
            <view class="label-class">{{ $t("交易对") }}</view>
            <view class="value-class">{{ item.name }}</view>
          </view>
          <view class="item-class">
            <view class="label-class">{{ $t("方向") }}</view>
            <view
              class="value-class"
              :style="
                item.shortLong == constant.BUY_DOWN
                  ? 'color:var(--common-negative-color)'
                  : 'color:var(--common-positive-color)'
              "
              >{{
                item.shortLong == constant.BUY_UP ? $t("买涨") : t("买跌")
              }}</view
            >
          </view>
          <view class="item-class">
            <view class="label-class">{{ $t("购买价") }}</view>
            <view class="value-class">{{
              useFormatMoney(item.orderPrice)
            }}</view>
          </view>
          <view class="item-class" v-if="item.tradeStatus !=0">
            <view class="label-class">{{ $t("平仓价") }}</view>
            <view class="value-class">{{
              useFormatMoney(item.settlePrice)
            }}</view>
          </view>
          <view class="item-class">
            <view class="label-class">{{ $t("买入金额") }}</view>
            <view class="value-class">{{ useFormatMoney(item.amount) }}</view>
          </view>
          <view class="item-class">
            <view class="label-class">{{ $t("订单时长") }}</view>
            <view class="value-class">{{ item.duration }}s</view>
          </view>
          <view class="item-class" v-if="tabIndex == 1">
            <view class="label-class">{{ $t("卖出时间") }}</view>
            <view class="value-class">{{ useFormatTime(item.sendTime) }}</view>
          </view>
          <view class="item-class" v-if="tabIndex == 1">
            <view class="label-class">{{ $t("实际盈亏") }}</view>
            <view class="value-class" :style="getStyleForValue(item.profit)">{{
              useFormatMoney(item.profit)
            }}</view>
          </view>
        </view>
      </view>
    </z-paging>
  </view>
</template>
<script setup>
import { ref, computed } from "vue";
import constant from "@/common/constant.js";
import tui from "@/common/httpRequest.js";
import { t } from "@/hooks/useI18n";
import { useFormatMoney } from "@/common/utils";
import { useFormatTime } from "@/common/utils";

// const tabList = ref([t("持仓"), t("历史")]);
const tabList = computed(() => {
  return [t("持仓"), t("历史")];
});

const tabIndex = ref(0);
const paging = ref(null);
const dataList = ref([]);
function tabChange(index) {
  tabIndex.value = index;
  // 当切换tab或搜索时请调用组件的reload方法，请勿直接调用：queryList方法！！
  paging.value.reload();
}
function queryList(pageNo, pageSize) {
  // 组件加载时会自动触发此方法，因此默认页面加载时会自动触发，无需手动调用
  // 这里的pageNo和pageSize会自动计算好，直接传给服务器即可
  // 模拟请求服务器获取分页数据，请替换成自己的网络请求
  const params = {
    pageNo: pageNo,
    pageSize: pageSize,
    type: tabIndex.value,
  };

  tui
    .request("/app-api/trade/order/time-contract/page", "GET", params)
    .then((res) => {
      // 将请求的结果数组传递给z-paging
      paging.value.complete(res.data.list);
    })
    .catch((res) => {
      // 如果请求失败写this.$refs.paging.complete(false);
      // 注意，每次都需要在catch中写这句话很麻烦，z-paging提供了方案可以全局统一处理
      // 在底层的网络请求抛出异常时，写uni.$emit('z-paging-error-emit');即可
      paging.value.complete(false);
    });
}

function getStyleForValue(value) {
  let color;
  if (value > 0) {
    color = "var(--common-positive-color)";
  } else if (value < 0) {
    color = "var(--common-negative-color)";
  } else {
    color = "var(--zero-color)";
  }
  return { color };
}
</script>
  
  <style scoped lang="scss">
.order-page-class {
  .grid-container {
    padding: 20rpx 0;
    .grid-item {
      display: flex;
      flex-direction: column;
      row-gap: 18rpx;
      margin-bottom: 30rpx;
      padding-bottom: 30rpx;
      border-bottom: 1rpx solid #f7f7f7;
      .item-class {
        padding: 0 30rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .label-class {
          color: #999;
          font-family: "Noto Sans";
          font-size: 26rpx;
          font-style: normal;
          font-weight: 500;
          line-height: 145%; /* 37.7rpx */
        }
        .value-class {
          color: #000;
          text-align: right;
          font-family: "PingFang SC";
          font-size: 26rpx;
          font-style: normal;
          font-weight: 500;
          line-height: 145%; /* 37.7rpx */
        }
      }
    }
  }
  .tabs-contract-line-class {
    box-sizing: border-box;
    padding: 0 24rpx;
    // border-bottom: 2rpx solid #f9f9f9;
    ::v-deep .z-tabs-list-container {
      justify-content: flex-start;
      .z-tabs-list {
        flex: 0 !important;
      }
      .z-tabs-item-title {
        text-align: center;
        font-family: "PingFang SC";
        font-size: 36rpx;
        font-style: normal;
        line-height: 32rpx; /* 114.286% */
      }
    }
  }
}
</style>
  