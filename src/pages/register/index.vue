<template>
  <view class="register-class">
    <z-tabs
      :list="tabList"
      @change="tabChange"
      class="tabs-line-class"
      bar-height="0"
    >
    </z-tabs>

    <view class="input-information-class">
      <view class="item-class">
        <view class="label-class">{{ $t("国家/地区") }}</view>

        <InputSelect
            :placeholder="$t('请选择国家/地区')"
            :popupTitle="$t('国家/地区')"
            :list="country"
            keyLable="code"
             keyValue="areaId"
            :value="form.areaId"
            v-model:value="form.areaId"
        ></InputSelect>
      </view>

      <view class="item-class">
        <view class="label-class">{{ $t("姓名") }}</view>
        <view class="value-class">
          <uni-easyinput  v-model="form.nickname" :placeholder="$t('请输入姓名')" />
        </view>
      </view>

      <view class="item-class" v-if="form.accountType==2">
        <view class="label-class">{{ $t("邮箱") }}</view>
        <view class="value-class">
          <uni-easyinput trim="all"  v-model="form.email" :placeholder="$t('请输入邮箱')" />
        </view>
      </view>
      <view class="item-class" v-if="form.accountType==2">
        <view class="label-class">{{ $t("邮箱验证码") }}</view>
        <view class="value-class">
          <uni-easyinput  v-model="form.verifyCode" trim="all" :placeholder="$t('请输入验证码')">
            <template #right>
              <span class="send-class" @click="handleEmailVerifyCode">{{ sendEmailText }}</span>
            </template>
          </uni-easyinput>
        </view>
      </view>

      <view class="item-class" v-if="form.accountType==3">
        <view class="label-class">{{ $t("手机号") }}</view>
        <view class="value-class">
          <uni-easyinput v-model="form.mobile" trim="all" :placeholder="$t('请输入手机号')">
            <template #left>
              <span class="mobile-prefix">+81</span>
            </template>
          </uni-easyinput>
        </view>
      </view>
      <view class="item-class" v-if="form.accountType==3">
        <view class="label-class">{{ $t("手机验证码") }}</view>
        <view class="value-class">
          <uni-easyinput v-model="form.verifyCode" trim="all" :placeholder="$t('请输入验证码')">
            <template #right>
              <span class="send-class" @click="handleMobileVerifyCode">{{ sendMobileText }}</span>
            </template>
          </uni-easyinput>
        </view>
      </view>

      <view class="item-class" v-if="form.accountType==1">
        <view class="label-class">{{ $t("用户名") }}</view>
        <view class="value-class">
          <uni-easyinput v-model="form.username" trim="all" :placeholder="$t('请输入用户名')" />
        </view>
      </view>

      <view class="item-class">
        <view class="label-class">{{ $t("密码") }}</view>
        <view class="value-class">
          <uni-easyinput  v-model="form.password" type="password"  trim="all" :placeholder="$t('请输入密码')" />
        </view>
      </view>

      <view class="item-class">
        <view class="label-class">{{ $t("确认密码") }}</view>
        <view class="value-class">
          <uni-easyinput  v-model="password2"  type="password"  trim="all" :placeholder="$t('请输入请确认密码')" />
        </view>
      </view>

      <view class="item-class" v-if="form.agentCode">
        <view class="label-class">{{ $t("邀请码(非必填)") }}</view>
        <view class="value-class">
          <uni-easyinput  v-model="form.agentCode"   trim="all" :placeholder="$t('请输入邀请码')" disabled/>
        </view>
      </view>
    </view>

    <view class="register-class" @click="handleSubmit">
      {{ $t("注册") }}
    </view>
    <view class="existing-account-go-log-in" @click="navLogin">{{ $t("已有账号？去登录") }}</view>
  </view>
</template>
<script setup>
import config from "@/common/config";
import { t } from "@/hooks/useI18n";
import { ref,reactive, computed } from "vue";
import {onLoad,onUnload} from "@dcloudio/uni-app";
import tui from "@/common/httpRequest.js";
import {SHARE_CODE} from "@/common/cacheEnums";
import ceche from "@/common/cache";


const tabList = computed(()=>{
  return [
    { name: t("用户名"), value: 1 },
    { name: t("邮箱"), value: 2 },
    // { name: t("手机号"), value: 3 },
  ]
});
const sendEmailText = ref(t("发送"))

const sendMobileText = ref(t("发送"));

// const sendMobileText = ref(t("发送"));
const emailTimer = ref();
const mobileTimer = ref();
function tabChange(index) {
  // form.accountType.value = index;
  form.accountType=tabList.value[index].value
  form.verifyCode = ''
}
//请求表单
const form  =reactive({
  "accountType": 1,//	账号类型,1账号，2邮箱，3手机号,示例值(1)
  "username": "",
  "email": "",
  "mobile": "",
  "password": "",
  "nickname": "",
  "areaId": "", //areaId
  "verifyCode": "",
  "agentCode":""  //邀请码
})
//二次输入密码
const password2=ref('')


function navLogin(){
    uni.redirectTo({
        url:'/pages/signIn/index'
    })
}
onLoad(()=>{
  getCountry()
  const  code = ceche.get(SHARE_CODE)
  if(code){
    form.agentCode=code
  }
})
const country = ref([]);
function getCountry(){
  tui.request("/app-api/system/config/list-country",'GET').then(res=>{
    country.value = res.data
  })
}

//提交
const handleSubmit=async ()=>{
  try {
    if(!form.areaId){
      tui.toast(t('请选择国家/地区'))
      return
    }
    if(form.accountType==1) {
      if (!form.username) {
        tui.toast(t('请输入用户名'))
        return
      }
    }
    if(form.accountType==1&&!form.nickname){
        tui.toast(t('请输入姓名'))
        return
    }
    if(form.accountType==2){
      if(!form.email){
        tui.toast(t('请输入邮箱'))
        return
      }
      if(!form.verifyCode){
        tui.toast(t('请输入验证码'))
        return
      }
    }
    if(form.accountType==3){
      if(!form.mobile){
        tui.toast(t('请输入手机号'))
        return
      }
      if(!form.verifyCode){
        tui.toast(t('请输入验证码'))
        return
      }
    }
    if(!form.password){
      tui.toast(t('请输入密码'))
      return
    }
    if(!password2.value){
      tui.toast(t('请再次输入密码'))
      return
    }
    if(form.password!=password2.value){
      tui.toast(t('密码不一致'))
      return
    }
   let {data} =  await tui.request("/app-api/member/auth/register",'POST',form)
   uni.setStorageSync("account",form.accountType==1?form.username:form.accountType==2?form.email:form.accountType==3?form.mobile:'')
    tui.toast(t('注册成功'),1500,true)
    setTimeout(()=>{
      uni.reLaunch({
        url: "/pages/signIn/index"
      })
  
    },1500)
  }catch (e) {

  }
}


const handleEmailVerifyCode= async ()=>{
  if(!form.email){
    tui.toast(t('请输入邮箱'))
    return;
  }
  if (sendEmailText.value==t("发送")) {
      await tui.request('/app-api/member/auth/send-mail-code','POST',{  "email": form.email, "scene": 1})
      let count = 60;
    sendEmailText.value= ''+count+'s';
    emailTimer.value= setInterval(() => {
      if (count > 0 && count <= 60) {
        count--;
        sendEmailText.value= ''+count+'s';
      } else {
        emailRest();
      }
    }, 1000)
  }
}
const handleMobileVerifyCode= async ()=>{
  if(!form.mobile){
    tui.toast(t('请输入手机号'))
    return;
  }
  if (sendMobileText.value==t("发送")) {
    await tui.request('/app-api/member/auth/send-sms-code','POST',{  "mobile": form.mobile, "scene": 1})
    let count = 60;
    sendMobileText.value= ''+count+'s';
    mobileTimer.value= setInterval(() => {
      if (count > 0 && count <= 60) {
        count--;
        sendMobileText.value= ''+count+'s';
      } else {
        mobileRest();
      }
    }, 1000)
  }
}
const emailRest=()=>{
  sendEmailText.value= t("发送");
  if(emailTimer.value){
    clearInterval(emailTimer.value);
    emailTimer.value = null;
  }
}

const mobileRest=()=>{
  sendMobileText.value= t("发送");
  if(mobileTimer.value){
    clearInterval(mobileTimer.value);
    mobileTimer.value = null;
  }
}


onUnload(()=>{
  if(mobileTimer.value){
    clearInterval(mobileTimer.value);
  }
  if(emailTimer.value) {
    clearInterval(emailTimer.value);
  }
})
</script>

<style scoped lang="scss">
.register-class {
  .register-class {
    width: 690rpx;
    height: 96rpx;
    flex-shrink: 0;
    color: #fff;
    text-align: center;
    font-family: "PingFang SC";
    font-size: 32rpx;
    font-style: normal;
    font-weight: bolder;
    line-height: 145%; /* 46.4rpx */
    border-radius: 20000rpx;
    background: #3248f4;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
    margin-top: 32rpx;
    margin-bottom: 48rpx;
  }
  .existing-account-go-log-in {
    color: #2752e7;
    text-align: center;
    font-family: "PingFang SC";
    font-size: 28rpx;
    font-style: normal;
    font-weight: 400;
    line-height: 32rpx; /* 114.286% */
    padding-bottom: 100rpx;
  }
  .input-information-class {
    padding: 40rpx 32rpx;
    display: flex;
    flex-direction: column;
    row-gap: 32rpx;
    .item-class {
      .label-class {
        color: #999;
        font-family: "Noto Sans";
        font-size: 24rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 42rpx; /* 175% */
        letter-spacing: -0.64rpx;
        margin-bottom: 8rpx;
      }
      .value-class {
        .send-class {
          color: #3248f4;
          font-family: "PingFang SC";
          font-size: 26rpx;
          font-style: normal;
          font-weight: bold;
          line-height: 44rpx; /* 169.231% */
          letter-spacing: -0.64rpx;
          padding: 0 20rpx;
        }
        .mobile-prefix {
          color: #000;
          font-family: "PingFang SC";
          font-size: 30rpx;
          font-style: normal;
          font-weight: bolder;
          line-height: 44rpx; /* 146.667% */
          letter-spacing: -0.64rpx;
          padding: 0 22rpx;
        }
      }
    }
  }
  .tabs-line-class {
    padding-left: 48rpx;

    box-sizing: border-box;
    padding: 0 32rpx;
    // border-bottom: 2rpx solid #f9f9f9;
    ::v-deep .z-tabs-list-container {
      justify-content: flex-start;
      .z-tabs-list {
        flex: 0 !important;
      }
      .z-tabs-item-title {
        color: #94959a;
        text-align: center;
        font-family: "PingFang SC";
        font-size: 36rpx;
        font-style: normal;
        font-weight: bolder;
        line-height: 32rpx; /* 88.889% */
      }
    }
  }
}
</style>