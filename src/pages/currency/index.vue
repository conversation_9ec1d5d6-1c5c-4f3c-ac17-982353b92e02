<template>
  <view class="currency-class">
    <uni-forms>
      <radio-group @change="radioChange">
        <uni-forms-item v-for="(item, index) in localeList" :key="item.quoteCurrency">
          <label>
            <view class="row-class">
              <view class="right-class">{{ item.quoteCurrency }}
                <radio :value="item.quoteCurrency"  :checked="index === current" color="#3248F4"/>
              </view>
            </view>
          </label>
        </uni-forms-item>
      </radio-group>
    </uni-forms>
  </view>
</template>
  <script setup>
import { ref, computed } from "vue";
import config from '@/common/config';
const { exchangeRates,currencyUnit,currencySymbol,currency } = config;
const localeList = ref(exchangeRates);
//根据本地返回勾选
const current = computed(() => {
  return exchangeRates.findIndex((item) => item.quoteCurrency === config.currencyUnit );
});

function radioChange(evt) {
  uni.setStorageSync("currencyUnit", evt.detail.value);
  config.currencyUnit = evt.detail.value;
  config.currencySymbol = currency.find((item) => item.code === config.currencyUnit).symbol;
  uni.setStorageSync("currencySymbol", config.currencySymbol);
  uni.navigateBack();
}
</script>
  <style scoped lang="scss">
.currency-class {
  padding: 0 30rpx;
  padding-top: 30rpx;
  .row-class {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    column-gap: 20rpx;
    .right-class {
      flex: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;

      color: #000;
      font-family: "PingFang SC";
      font-size: 32rpx;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      letter-spacing: -0.64rpx;
    }
    .logo-class {
      width: 60rpx;
      height: 40rpx;
      // border-radius: 100%;
    }
  }
}
</style>