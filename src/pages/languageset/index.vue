<template>
  <view class="languageset">
    <uni-forms>
      <radio-group @change="radioChange">
        <uni-forms-item v-for="(item, index) in localeList" :key="item.code">
          <label>
            <view class="row-class">
              <image :src="item.ico" class="logo-class"></image>
              <view class="right-class">{{ item.localName}}
                <radio
                  :value="item.code"
                  :checked="index === current"
                  color="#3248F4"
              /></view>
            </view>
          </label>
        </uni-forms-item>
      </radio-group>
    </uni-forms>
  </view>
</template>
<script setup>
import { ref, computed } from "vue";
import { i18n } from "@/locale";
import config from '@/common/config';
import { messages } from "@/locale";
const langItems = config.language;


//对比本地语言文件
const localeList = computed(() => {
  const list = Object.keys(messages);
  const filteredItems = langItems.filter((item) => list.includes(item.code));
  return filteredItems;
});
//根据本地返回勾选
const current = computed(() => {
  const rowIndex = localeList.value.findIndex((i) => {
    return i.code === uni.getLocale();
  });
  return rowIndex;
});

function radioChange(evt) {
  // console.log(evt.detail.value);
  const locale = evt.detail.value;
  uni.setLocale(locale);
  i18n.locale = locale;
  i18n.global.locale = locale;
  // uni.navigateBack();

}
</script>
<style scoped lang="scss">
.languageset {
  padding: 0 30rpx;
  padding-top: 30rpx;
  .row-class {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    column-gap: 20rpx;
    .right-class {
      flex: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;

      color: #000;
      font-family: "PingFang SC";
      font-size: 32rpx;
      font-style: normal;
      font-weight: bold;
      line-height: normal;
      letter-spacing: -0.64rpx;
    }
    .logo-class {
      width: 60rpx;
      height: 40rpx;
      // border-radius: 100%;
    }
  }
}
</style>