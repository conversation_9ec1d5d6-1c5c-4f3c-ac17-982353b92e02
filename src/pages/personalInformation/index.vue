<template>
  <view class="personalInformation">
    <view class="personalInformation-content">
      <image :src="userInfo.avatar||'/static/images/index/18.png'" class="user-img" @click="chooseImage"></image>
      <view  @click="open">
        <span class="user-name-class">{{ userInfo.nickname||$t('还未设置昵称') }} </span>
        <image src="@/static/images/common/4.png" class="edit-img"></image>
      </view>
    </view>

    <view class="change-password-class">
      <view>
        <view class="title-class">
          {{ $t("账号") }}
        </view>
        <uni-easyinput v-model="userInfo.username" trim="all" :placeholder="$t('请输入账号')" disabled/>
      </view>
    </view>

    <view class="submit-class" v-if="false">{{ $t("提交") }}</view>

    <uni-popup
      ref="refPopup"
      type="bottom"
      borderRadius="20rpx 20rpx 0 0"
      background-color="#fff"
    >
      <view class="popup-class">
        <view class="popup-header-class">
          <view class="popup-title-class">{{ $t("修改昵称") }}</view>
          <view class="popup-cancel-class" @click="close">
            <image
              src="@/static/images/common/2.png"
              mode=""
              class="close-icon"
            ></image>
          </view>
        </view>

        <view class="change-password-class">
          <view>
            <view class="title-class">
              {{ $t("修改昵称") }}
            </view>
            <uni-easyinput v-model="nickname" :placeholder="$t('修改昵称')" />
          </view>
        </view>

        <view class="cancel-sure-class">
          <view class="cancel" @click="close">{{ $t("取消") }}</view>
          <view class="sure" @click="updateNickname()">{{ $t("确定") }}</view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>
<script setup>
import {ref} from 'vue'
import { t } from "@/hooks/useI18n";
import tui from "@/common/httpRequest.js";
import {onShow} from "@dcloudio/uni-app";

const nickname = ref("");
const refPopup = ref(null);
const userInfo = ref({})
const open = () => {
  refPopup.value.open();
};

const close = () => {
  refPopup.value.close();
};

onShow(()=>{
  getUserInfo()
})

async function getUserInfo(){
  const {data} =  await tui.request('/app-api/member/user/get','GET')
  userInfo.value= data;
  nickname.value = userInfo.value.nickname
}


async function updateNickname(){
  await tui.request('/app-api/member/user/update','POST',{nickname:nickname.value})
  userInfo.value.nickname=nickname.value
  close()
  tui.toast(t("修改成功"))
}

async function updateAvatar(avatar){
  await tui.request('/app-api/member/user/update','POST',{avatar:avatar})
  userInfo.value.avatar=avatar
  tui.toast(t("修改成功"))
}

function chooseImage(){
  uni.chooseImage({
    count:1,
    crop:{
      width:100,
      height:100
    },
  async  success(e){
    console.log(e)
    userInfo.value.avatar=e.tempFilePaths[0]
      const {data} =  await tui.uploadFile('/app-api/infra/file/upload',e.tempFilePaths[0])

     updateAvatar(data);
    }
  })
}

</script>
<style scoped lang="scss">
.personalInformation {
  .popup-class {
    .cancel-sure-class {
      display: flex;
      justify-content: space-between;
      column-gap: 22.74rpx;
      padding: 0 30rpx;
      margin-bottom: 40rpx;
      .cancel {
        flex: 1;
        text-align: center;
        border-radius: 20000rpx;
        background: #efefef;
        color: #979797;
        text-align: center;
        font-family: "Noto Sans";
        font-size: 28rpx;
        font-style: normal;
        font-weight: bolder;
        line-height: 145%; /* 40.6rpx */
        height: 80rpx;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .sure {
        flex: 1;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 20000rpx;
        background: #3248f4;
        height: 80rpx;

        color: #fff;
        text-align: center;
        font-family: "Noto Sans";
        font-size: 28rpx;
        font-style: normal;
        font-weight: bolder;
        line-height: 145%; /* 40.6rpx */
      }
    }
    .change-password-class {
      margin-top: 16rpx;
      margin-bottom: 100rpx;
    }
    .popup-header-class {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 32rpx 30rpx;
      .popup-title-class {
        color: #000;
        font-family: "Noto Sans";
        font-size: 32rpx;
        font-style: normal;
        font-weight: bold;
        line-height: 145%; /* 46.4rpx */
      }
      .popup-cancel-class {
        .close-icon {
          width: 40rpx;
          height: 40rpx;
          flex-shrink: 0;
        }
      }
    }
  }
  .submit-class {
    width: 690rpx;
    height: 96rpx;
    flex-shrink: 0;
    border-radius: 20000rpx;
    background: #3248f4;
    color: #fff;
    text-align: center;
    font-family: "Noto Sans";
    font-size: 32rpx;
    font-style: normal;
    font-weight: bolder;
    line-height: 145%; /* 46.4rpx */
    margin: 0 auto;
    line-height: 96rpx;
    position: absolute;
    bottom: 100rpx;
    left: 0;
    right: 0;
  }
  .change-password-class {
    background-color: #fff;
    border-radius: 20rpx 20rpx 0rpx 0rpx;
    box-sizing: border-box;
    margin-top: -40rpx;
    z-index: 1;
    position: relative;
    padding: 32rpx;
    .title-class {
      color: #999;
      font-family: "Noto Sans";
      font-size: 24rpx;
      font-style: normal;
      font-weight: 400;
      line-height: 42rpx; /* 175% */
      letter-spacing: -0.64rpx;
      padding-bottom: 10rpx;
    }
  }
  .personalInformation-content {
    height: 342rpx;
    background: #3248f4;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-direction: column;
    row-gap: 40rpx;
    padding-top: 40rpx;
    box-sizing: border-box;
    .user-img {
      width: 128rpx;
      height: 128rpx;
      flex-shrink: 0;
      border-radius: 50%;
    }
    .edit-img {
      width: 24rpx;
      height: 24rpx;
      margin-left: 10rpx;
    }
    .user-name-class {
      color: #fff;
      font-family: "Noto Sans";
      font-size: 36rpx;
      font-style: normal;
      font-weight: bolder;
      line-height: 40rpx; /* 111.111% */
      letter-spacing: -0.64rpx;
    }
  }
}
</style>