<template>
  <view class="changepwd">
    <view class="item-class">
      <view class="label-class">{{ $t("账户密码") }}</view>
      <uni-easyinput v-model="old_password" type="password" trim="all" :placeholder="$t('请输入账户密码')" />
    </view>
    <view class="item-class">
      <view class="label-class">{{ $t("新登录密码") }}</view>
      <uni-easyinput v-model="new_password" type="password"  trim="all" :placeholder="$t('请输入新登录密码')" />
    </view>
    <view class="item-class">
      <view class="label-class">{{ $t("确认新密码") }}</view>
      <uni-easyinput v-model="new_password_confirm" type="password"  trim="all" :placeholder="$t('请输入确认新密码')" />
    </view>


    <view class="submit" @click="sub">
      {{ $t("提交") }}
    </view>
  </view>
</template>
<script setup>

import { ref } from "vue";
import tui from "@/common/httpRequest.js";
import { t } from "@/hooks/useI18n";
import {onLoad} from "@dcloudio/uni-app";

const old_password = ref("");
const new_password = ref("");
const new_password_confirm = ref("");
onLoad(({type})=>{
})



function sub() {
  // old_password
  // new_password
  if(!old_password.value){
    tui.toast(t('请输入旧登录密码'))
    return
  }
  if(!new_password.value){
    tui.toast(t('请输入新登录密码'))
    return
  }
  if(!new_password_confirm.value){
    tui.toast(t('请再次输入新密码'))
    return
  }
  if(new_password.value!==new_password_confirm.value){
    tui.toast(t('两次输入的密码不一致'))
    return
  }

  const url = '/app-api/member/user/update-password'
  const data = {
    oldPassword: old_password.value,
    password: new_password.value,
  }
  tui.request(url,'post',data ).then(res => {
    tui.toast(t("修改成功"))
    setTimeout(() => {
      uni.navigateBack({
        delta: 1
      })
    }, 1000);
  })

}
</script>
<style scoped lang="scss">

.changepwd{
  padding: 20rpx 31rpx;
  display: flex;
  flex-direction: column;
  row-gap: 32rpx;
  .submit {
    width: 690rpx;
    height: 96rpx;
    flex-shrink: 0;
    border-radius: 20000rpx;
    background: #3248f4;
    color: #fff;
    text-align: center;
    font-family: "Noto Sans";
    font-size: 32rpx;
    font-style: normal;
    font-weight: 600;
    line-height: 145%; /* 46.4rpx */
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
    margin-bottom: 100rpx;
  }
  .item-class {
    .label-class {
      margin-bottom: 20rpx;
      color: #999;
      font-family: "Noto Sans";
      font-size: 24rpx;
      font-style: normal;
      font-weight: 400;
      line-height: 42rpx; /* 175% */
      letter-spacing: -0.64rpx;
    }
  }
}

</style>
