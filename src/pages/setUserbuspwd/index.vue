<template>
  <view class="changeUserbuspwd">
    <view class="item-class">
      <view class="label-class">{{ $t("新交易密码") }}</view>
      <uni-easyinput v-model="new_paypass" type="password" trim="all" :placeholder="$t('请输入新交易密码')" />
    </view>
    <view class="item-class">
      <view class="label-class">{{ $t("确认交易密码") }}</view>
      <uni-easyinput  v-model="new_paypass_confirm" type="password"  trim="all" :placeholder="$t('请确认交易密码')" />
    </view>

    <view class="submit" @click="sub">
      {{ $t("提交") }}
    </view>
  </view>
</template>
<script setup>

import { ref } from "vue";
import tui from "@/common/httpRequest.js";
import { t } from "@/hooks/useI18n";

const new_paypass = ref("");
const new_paypass_confirm = ref("");
function validateTransactionPassword(password) {
  const regex = /^\d{6}$/;
  return regex.test(password);
}

function sub() {
  if (!new_paypass.value) {
    tui.toast(t('请输入新交易密码'))
    return
  }
  // if (!validateTransactionPassword(new_paypass.value)) {
  //   tui.toast(t('交易密码只能为6位数字'))
  //   return
  // }

  if (!new_paypass_confirm.value) {
    tui.toast(t('请输入确认交易密码'))
    return
  }
  if (new_paypass.value != new_paypass_confirm.value) {
    tui.toast(t('两次输入的密码不一致'))
    return
  }
  // /api/change_paypassword
  const url = '/app-api/member/user/set-fund-password'
  const data = {
    password: new_paypass.value,
  }
  tui.request(url,'POST', data).then(res => {
    tui.toast(t("设置成功"))
    setTimeout(() => {
      uni.navigateBack({
        delta: 1
      })
    }, 1000);
  })


}
</script>
<style scoped lang="scss">
.changeUserbuspwd{
  padding: 20rpx 31rpx;
  display: flex;
  flex-direction: column;
  row-gap: 32rpx;
  .submit {
    width: 690rpx;
    height: 96rpx;
    flex-shrink: 0;
    border-radius: 20000rpx;
    background: #3248f4;
    color: #fff;
    text-align: center;
    font-family: "Noto Sans";
    font-size: 32rpx;
    font-style: normal;
    font-weight: 600;
    line-height: 145%; /* 46.4rpx */
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
    margin-bottom: 100rpx;
  }
  .item-class {
    .label-class {
      margin-bottom: 20rpx;
      color: #999;
      font-family: "Noto Sans";
      font-size: 24rpx;
      font-style: normal;
      font-weight: 400;
      line-height: 42rpx; /* 175% */
      letter-spacing: -0.64rpx;
    }
  }
}
</style>
