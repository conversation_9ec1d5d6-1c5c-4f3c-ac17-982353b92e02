<template>
  <view class="identity-authentication-class">
    <view class="content-class">
      <view class="item-class">
        <view class="left-class">
          <image
            src="@/static/images/index/13.png"
            mode=""
            class="item-image-class"
          ></image>
          <span>{{ $t('Lv1.基础认证') }}</span>
        </view>
        <view class="right-class">
          <span class="certified"> {{ $t('已认证') }} </span>
          <image
            src="@/static/images/index/11.png"
            mode=""
            class="go-image-class"
          ></image>
        </view>
      </view>

      <view class="item-class" @click="navRouting('/pages/authenticateIdentity/index')">
        <view class="left-class">
          <image
            src="@/static/images/index/14.png"
            mode=""
            class="item-image-class"
          ></image>
          <span>{{ $t('Lv2.身份认证') }}</span>
        </view>
        <view class="right-class">
          <span :class="userInfo?userInfo.certificationStatus?'certified':'go-for-authentication':'go-for-authentication' "> {{userInfo?userInfo.certificationStatus?userInfo.certificationStatusStr:$t('去认证'):''}}  </span>
          <image
            src="@/static/images/index/11.png"
            mode=""
            class="go-image-class"
          ></image>
        </view>
      </view>
    </view>
    <view class="submit-class" v-if="false">
      {{ $t("提交") }}
    </view>
  </view>
</template>
<script setup>
import {ref} from "vue";
import {onShow} from "@dcloudio/uni-app";
import tui from "@/common/httpRequest.js";
const userInfo = ref({})
onShow(()=>{
  getUserInfo()
})
function navRouting(url){
  //0未认证 || 3失败
  if(userInfo.value.certificationStatus==0 ||  userInfo.value.certificationStatus== 3){
      uni.navigateTo({
      url:url+'?status='+userInfo.value.certificationStatus
    })
  }
 
}
async function getUserInfo(){
  const {data} =  await tui.request('/app-api/member/user/get','GET')
  userInfo.value=data
}
</script>
<style scoped lang="scss">
.identity-authentication-class {
  .submit-class {
    width: 690rpx;
    height: 96rpx;
    flex-shrink: 0;
    border-radius: 20000rpx;
    background: #3248f4;
    color: #fff;
    text-align: center;
    font-family: "Noto Sans";
    font-size: 32rpx;
    font-style: normal;
    font-weight: bold;
    line-height: 145%; /* 46.4rpx */
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    bottom: 100rpx;
    left: 50%;
    transform: translateX(-50%);
  }
  .content-class {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
    padding: 32rpx;
    
    .item-class {
      border-radius: 18rpx;
      background: #fafafa;
      display: flex;
      padding: 32rpx 20rpx;
      align-items: center;
      justify-content: space-between;
      gap: 20rpx;
      .certified {
        color: var(--common-positive-color);
      }
      .go-for-authentication {
        color: #3248f4;
      }
      .right-class {
        font-family: "Noto Sans";
        font-size: 28rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 44rpx; /* 157.143% */
        letter-spacing: -0.64rpx;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        column-gap: 20rpx;
        .go-image-class {
          width: 28rpx;
          height: 28rpx;
          flex-shrink: 0;
        }
      }
      .left-class {
        color: #000;
        font-family: "Noto Sans";
        font-size: 32rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 44rpx; /* 137.5% */
        letter-spacing: -0.64rpx;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        column-gap: 20rpx;
        .item-image-class {
          width: 56rpx;
          height: 56rpx;
          flex-shrink: 0;
        }
      }
    }
  }
}
</style>