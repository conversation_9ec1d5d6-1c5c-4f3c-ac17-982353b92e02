<template>
  <view class="order-page-class">
    <z-paging ref="paging" v-model="dataList" @query="queryList">
      <template #top>
        <view class="all-position-wrapper">
          <!-- <view class="left-wrapper">
            <view class="item-wrapper">
              <span class="label-class">{{$t("风险率")}}：</span>
              <span class="value-class">0.00%</span>
            </view>
            <view class="item-wrapper">
              <span  class="label-class">{{$t("持仓总收益")}}：</span>
              <span  :class="totalMoney==0?'value-class':totalMoney>0?'value-class-up':'value-class-down'">{{totalMoney}}</span>
            </view>
          </view> -->
          <view class="btn-all-position" @click="handleAllOption">{{
            $t("一键平仓")
          }}</view>
        </view>
        <!-- 合约/期权tab   -->
        <view class="tabs-wrapper-class">
          <view @click="handleTab(1)"
            ><view :class="{ 'tab-active': tabsActive == 1 }"
              >{{ $t("持仓") }}
            </view>
          </view>
          <view @click="handleTab(2)"
            ><view :class="{ 'tab-active': tabsActive == 2 }">{{
              $t("平仓")
            }}</view></view
          >
        </view>
      </template>
      <view class="grid-container">
        <view v-for="item in dataList" :key="item.id" class="grid-item">
          <view class="title-wrapper">
            <span class="title"
              >{{ item.code }}/{{ item.marginCurrency }}
              {{ item.leverage }}x</span
            >
            <view :class="['tag', item.shortLong == 1 ? 'buy' : 'sell']">{{
              item.shortLong == 1 ? $t("做多") : $t("做空")
            }}</view>
          </view>
          <view class="info-wrapper">
            <view class="item-class">
              <view class="label-class">{{ $t("开仓价") }}</view>
              <view class="value-class">{{ useFormatMoney(item.openPrice) }}</view>
            </view>
            <view class="item-class">
              <view class="label-class">{{
                tabsActive == 1 ? $t("当前价") : $t("平仓价")
              }}</view>
              <view class="value-class" v-if="tabsActive == 1">
                <Volatility :code="item.code">
                  <template #default="{ currentDataPrice }">
                    {{ useFormatMoney(currentDataPrice) || item.currentPrice}}
                  </template>
                </Volatility>
              </view>
              <view class="value-class" v-else>
                {{ useFormatMoney(item.closePrice) }}
              </view>
            </view>
            <view class="item-class">
              <view class="label-class">{{ $t("止盈价") }}</view>
              <view class="value-class"
                >{{
                  item.stopProfitPrice
                    ? useFormatMoney(item.stopProfitPrice)
                    : "--"
                }}
              </view>
            </view>
            <view class="item-class">
              <view class="label-class">{{ $t("止损价") }}</view>
              <view class="value-class">{{
                item.stopLossPrice ? useFormatMoney(item.stopLossPrice) : "--"
              }}</view>
            </view>
            <view class="item-class">
              <view class="label-class">{{ $t("保证金") }}</view>
              <view class="value-class">{{ useFormatMoney(item.margin) }}</view>
            </view>
            <view class="item-class" v-if="tabsActive == 1">
              <view class="label-class">{{ $t("预估强平价") }}</view>
              <view class="value-class">{{ item.estimateLiquidatePrice<0?'--':useFormatMoney(item.estimateLiquidatePrice) }}</view>
            </view>
            <view class="item-class" v-if="tabsActive == 2">
              <view class="label-class">{{ $t("收益") }}</view>
              <view class="value-class">{{
                useFormatMoney(item.profitLoss)
              }}</view>
            </view>
            <view class="item-class" v-if="tabsActive == 1">
              <view class="label-class">{{ $t("收益") }}</view>
              <view class="value-class">
                <Volatility :code="item.code">
                  <template #default="{ currentDataPrice }">
                    {{ useFormatMoney(getIncome(item, currentDataPrice)) }}
                  </template>
                </Volatility>
              </view>
            </view>
         
            <!--view class="item-class">
              <view class="label-class">{{ $t("隔夜费") }}</view>
              <view class="value-class">{{
                useFormatMoney(2364.48789032)
              }}</view>
            </view-->
            <view class="item-class">
              <view class="label-class">{{ $t("手续费") }}</view>
              <view class="value-class">{{
                useFormatMoney(item.orderFee)
              }}</view>
            </view>
            <view class="item-class">
              <view class="label-class">{{ $t("持仓量") }}</view>
              <view class="value-class">{{
                useFormatMoney(item.volume)
              }}</view>
            </view>
            <view class="item-class" style="width: 50%" >
              <view class="label-class">{{ $t("开仓时间") }}</view>
              <view class="value-class">{{
                useFormatTime(item.openTime)
              }}</view>
            </view>
            <view class="item-class" style="width: 50%" v-if="tabsActive ==2">
              <view class="label-class">{{ $t("平仓时间") }}</view>
              <view class="value-class">{{
                useFormatTime(item.closeTime)
              }}</view>
            </view>
          </view>
          <view class="option-wrapper" v-if="tabsActive == 1">
            <view class="btn btn-class1" @click="handleSet(item)">{{
              $t("设置止盈止损")
            }}</view>
            <view class="btn btn-class2" @click="handleOption(item)">{{
              $t("平仓")
            }}</view>
          </view>
        </view>
      </view>
    </z-paging>

    <CommissionMoneySetPopup
      ref="commissionMoneySetPopupRef"
      @confirm="getDataList"
    ></CommissionMoneySetPopup>
  </view>
</template>
<script setup>
import { computed, ref } from "vue";
import tui from "@/common/httpRequest.js";
import { t } from "@/hooks/useI18n";
import { useFormatMoney, useFormatTime } from "@/common/utils";
import BigNumber from "bignumber.js";
import { onLoad } from "@dcloudio/uni-app";
import { EventBus } from '@/common/eventBus';

const type = ref(1);
const commissionMoneySetPopupRef = ref(null);
const tabsActive = ref(1);
const paging = ref(null);
const dataList = ref([]);

const stopProfitPrice = ref(undefined);
const stopLossPrice = ref(undefined);
const volume = ref(0);
const margin = ref(0);

//点击合约/期权tab
const handleTab = (tab) => {
  tabsActive.value = tab;
  paging.value.reload();
};

function getDataList() {
  paging.value.reload();
}
function queryList(pageNo, pageSize) {
  // 组件加载时会自动触发此方法，因此默认页面加载时会自动触发，无需手动调用
  // 这里的pageNo和pageSize会自动计算好，直接传给服务器即可
  // 模拟请求服务器获取分页数据，请替换成自己的网络请求
  const params = {
    pageNo: pageNo,
    pageSize: pageSize,
    type: tabsActive.value,
  };

  tui
    .request("/app-api/trade/order/contract/position-page", "GET", params)
    .then((res) => {
      // 将请求的结果数组传递给z-paging
      paging.value.complete(res.data.list);
    })
    .catch((res) => {
      // 如果请求失败写this.$refs.paging.complete(false);
      // 注意，每次都需要在catch中写这句话很麻烦，z-paging提供了方案可以全局统一处理
      // 在底层的网络请求抛出异常时，写uni.$emit('z-paging-error-emit');即可
      paging.value.complete(false);
    });
}
onLoad(({ fromType }) => {
  uni.setNavigationBarTitle({ title: t("委托订单") });
  eventBusList()
});

function eventBusList(){
  EventBus.on('strongParityOrLiquidation', ({recordNo,positionStatus,estimateLiquidatePrice}) => {
    //平仓
   if(positionStatus == 1){
    const list = dataList.value.filter(item => item.recordNo != recordNo);
    paging.value.resetTotalData([...list]);
   }
   //更新强平价
   if(positionStatus == 0){
    const list = dataList.value.map(item => {
      if(item.recordNo == recordNo){
        item.estimateLiquidatePrice = estimateLiquidatePrice
      }
      return item
    })
    paging.value.resetTotalData([...list]);
   }
  })
}

const handleOption = (item) => {
  uni.showModal({
    title: t("提示"),
    content: t("确定平仓?"),
    confirmText: t("确定"),
    cancelText: t("取消"),
    success: function (res) {
      if (res.confirm) {
        liquid(item);
      } else if (res.cancel) {
        console.log("用户点击取消");
      }
    },
  });
};

function liquid(item) {
  tui
    .request("/app-api/trade/contract/close", "POST", { id: item.id })
    .then((res) => {
      paging.value.reload();
    });
}

const handleAllOption = () => {
  uni.showActionSheet({
    itemList: [t("只平空单"), t("只平多单"), t("全部平仓")],
    success: function (res) {
      const type = res.tapIndex;
      tui
        .request("/app-api/trade/contract/close-batch", "POST", { type: type })
        .then((res) => {
          paging.value.reload();
        });
    },
    fail: function (res) {
      console.log(res.errMsg);
    },
  });
};

//设置
const handleSet = (item) => {
  commissionMoneySetPopupRef.value.open(item);
};
//计算收益
function getIncome(item, currentDataPrice) {
  // shortLong多仓1： （当前价格currentDataPrice-开仓价格openPrice） * 持仓量volume
  // shortLong空仓0： （开仓价格openPrice-当前价格currentDataPrice） * 持仓量volume
  const openPrice = new BigNumber(item.openPrice || 0);
  const currentPrice = new BigNumber(currentDataPrice || item.currentPrice || 0);
  const volume = new BigNumber(item.volume || 0);

  if (item.shortLong === 0) {
    return openPrice.minus(currentPrice).times(volume);
  } else {
    return currentPrice.minus(openPrice).times(volume);
  }
}

function getIncomeRate(item, currentDataPrice) {
  // shortLong多仓1： （当前价格currentDataPrice-开仓价格openPrice） * 持仓量volume
  // shortLong空仓0： （开仓价格openPrice-当前价格currentDataPrice） * 持仓量volume
  if (item.shortLong == 0) {
    return (currentDataPrice - item.openPrice) / item.openPrice;
  } else {
  }
}
</script>

<style scoped lang="scss">
.order-page-class {
  .all-position-wrapper {
    display: flex;
    width: 686rpx;
    padding: 32rpx;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    border-radius: 16px;
    background: #f8f8f8;
    margin: 20rpx auto;
    .left-wrapper {
      display: flex;
      flex-direction: column;
      .item-wrapper {
        display: flex;
        flex-direction: row;
        align-items: center;
        font-size: 24rpx;
        font-weight: bold;
        .label-class {
          color: #979797;
          margin-right: 10rpx;
        }
        .value-class {
          color: #000;
        }
        .value-class-up {
          color: var(--common-positive-color) !important;
        }
        .value-class-down {
          color: var(--common-negative-color) !important;
        }
      }
    }
    .btn-all-position {
      display: inline-flex;
      padding: 12rpx 32rpx;
      justify-content: center;
      align-items: center;
      color: #fff;
      font-family: "PingFang SC";
      font-size: 24rpx;
      font-style: normal;
      line-height: normal;
      background: var(--common-negative-color);
      border-radius: 40rpx;
    }
  }
  .grid-container {
    padding: 20rpx 0;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    .grid-item {
      display: flex;
      flex-direction: column;
      row-gap: 18rpx;
      margin-bottom: 30rpx;
      padding-bottom: 30rpx;
      border-bottom: 1rpx solid #f7f7f7;
      .title-wrapper {
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        box-sizing: border-box;
        padding: 20rpx;
        margin-left: 10rpx;
        .title {
          color: #000;
          text-align: center;
          font-family: "PingFang SC";
          font-size: 28rpx;
          font-style: normal;
          font-weight: bold;
          margin-right: 10rpx;
        }
        .tag {
          display: flex;
          padding: 6rpx 12rpx;
          justify-content: center;
          align-items: center;
          font-size: 22rpx;
          font-weight: bold;
        }
        .buy {
          border-radius: 6rpx;
          background: rgba(5, 192, 111, 0.1);
          color: var(--common-positive-color);
        }
        .sell {
          border-radius: 6rpx;
          background: rgba(251, 89, 114, 0.1);
          color: var(--common-negative-color);
        }
      }
      .info-wrapper {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        box-sizing: border-box;
        margin-left: 30rpx;
      }
      .item-class {
        display: flex;
        flex-direction: column;
        width: 33%;
        margin-bottom: 30rpx;
        .label-class {
          color: #999;
          font-family: "Noto Sans";
          font-size: 25rpx;
          line-height: 145%; /* 37.7rpx */
          font-weight: bold;
        }
        .value-class {
          color: #000;
          font-family: "PingFang SC";
          font-size: 25rpx;
          line-height: 145%; /* 37.7rpx */
          margin-top: 10rpx;
          white-space: nowrap;
        }
      }
      .option-wrapper {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;
        box-sizing: border-box;
        margin-right: 30rpx;
        .btn {
          color: #fff;
          font-family: "PingFang SC";
          font-size: 25rpx;
          font-style: normal;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .btn-class1 {
          border-radius: 1000rpx;
          background: #3248f4;
          height: 60rpx;
          width: 230rpx;
        }
        .btn-class2 {
          border-radius: 1000rpx;
          background: #fb5972;
          height: 60rpx;
          width: 180rpx;
          margin-left: 30rpx;
        }
      }
    }
  }
  .tabs-contract-line-class {
    box-sizing: border-box;
    padding: 0 24rpx;
    // border-bottom: 2rpx solid #f9f9f9;
    ::v-deep .z-tabs-list-container {
      justify-content: flex-start;
      .z-tabs-list {
        flex: 0 !important;
      }
      .z-tabs-item-title {
        text-align: center;
        font-family: "PingFang SC";
        font-size: 36rpx;
        font-style: normal;
        line-height: 32rpx; /* 114.286% */
      }
    }
  }
}
.tabs-wrapper-class {
  width: 686rpx;
  height: 80rpx;
  flex-shrink: 0;
  border-radius: 16rpx;
  border: 1rpx solid #f9f9f9;
  background: #f8f8f8;
  display: flex;
  flex-direction: row;
  align-items: center;
  box-sizing: border-box;
  padding-left: 7rpx;
  padding-right: 7rpx;
  margin: 0 auto;
  view {
    flex: 1;
    color: #999;
    font-size: 28rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
.tab-active {
  border-radius: 8rpx !important;
  width: 300rpx;
  height: 65rpx;
  background: #fff !important;
  color: #000000 !important;
  font-weight: bold !important;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>

<style>
.uni-actionsheet__menu .uni-actionsheet__cell {
  font-size: 28rpx !important;
  font-weight: bold;
}
.uni-actionsheet__action .uni-actionsheet__cell {
  color: #999999 !important;
  font-size: 28rpx !important;
  font-weight: bold;
}
.uni-modal__btn_default {
  font-weight: normal !important;
  font-size: 28rpx;
}
.uni-modal__btn_primary {
  font-weight: normal !important;
  font-size: 28rpx;
}

.uni-dialog-title-text {
  color: #000000 !important;
  font-size: 32rpx !important;
  font-weight: bold !important;
}
</style>
