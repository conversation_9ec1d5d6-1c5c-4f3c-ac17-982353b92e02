import {
	createSSRApp
} from "vue";
import { i18n } from './locale'
import '@/common/interceptor'
import websocketPlugin from './plugins/websocket';
import '@/static/styles/_variables.scss'
import App from "./App.vue";
import config from "@/common/config";
import tui from "@/common/httpRequest.js";
const app = createSSRApp(App);

  
// 全局跳转 url:跳转地址，type跳转类型
app.config.globalProperties.$jump = (url, type) => {
	if (type) {
		uni[type]({ url: url });
	} else {
		uni.navigateTo({ url: url });
	}
};

uni.onLocaleChange(({ locale }) => {
    // document.querySelector('html')?.setAttribute('lang', locale);
    // getCurrentPages().forEach(page => {
    //     if (page.$vm && page.$vm.$forceUpdate) {
    //         page.$vm.$forceUpdate()
    //     }
    // })
	location.reload(); // 刷新页面

    // window.location.reload();
})



export function createApp() {
	app.use(i18n);
	// 调用接口获取并设置平台配置
	app.use(websocketPlugin);
	return {
		app,
	};
}

