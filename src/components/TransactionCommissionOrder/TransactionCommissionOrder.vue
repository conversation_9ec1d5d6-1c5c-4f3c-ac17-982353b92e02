<!--委托订单-->
<template>
  <view class="transaction-commission-order">
    <view class="title">{{ $t('委托订单') }}</view>
    <view class="data-wrapper">
      <view class="box-warpper buy-warpper">
        <view class="label-class"> {{ $t('买') }}</view>
        <view class="item-class buy-item-class" v-for="item in buyList">
          <span class="num">{{ item.volume }}</span>
          <span class="money-positive-color"> {{ useFormatMoney(item.price) }}</span>
          <view class="percentage-buy-class" :style="getStyleForValue(item.volume, 'buy')"></view>
        </view>
      </view>
      <view class="box-warpper sell-wrapper">
        <view class="label-class"> {{ $t('卖') }}</view>
        <view class="item-class sell-item-class" v-for="item in sellList">
          <span class="money-negative-color">{{ useFormatMoney(item.price) }}</span>
          <span class="num">{{ item.volume }}</span>
          <view class="percentage-sell-class" :style="getStyleForValue(item.volume, 'sell')"></view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { useFormatMoney } from "@/common/utils";
const props = defineProps({
  currency: {
    type: Object,
    default: () => { },
  },
});
import { ref, onBeforeMount } from 'vue'
import { EventBus } from '@/common/eventBus';
import { onLoad, onUnload } from "@dcloudio/uni-app";
const buyList = ref([]) //买列表
const sellList = ref([]) //卖列表
const totalBuyVolume = ref(0) //买总交易量
const totalSellVolume = ref(0) // 卖总交易量


onLoad(() => {
  EventBus.on('transaction-buyseller-list', (data) => {
    if (props.currency.code == data.code) {
      buyList.value = data.bids
      sellList.value = data.asks
      //买总交易量
      totalBuyVolume.value = buyList.value.reduce((total, item) => {
        return total + Number(item.volume)
      }, 0);
      //卖总交易量
      totalSellVolume.value = sellList.value.reduce((total, item) => {
        return total + Number(item.volume)
      }, 0);
    }
  })
})
//获取交易量百分比
function getStyleForValue(volume, type) {
  let width = 0;
  try {
    if (type == 'buy') {
      width = Number(volume) / totalBuyVolume.value * 100 * 10
    } else if (type == 'sell') {
      width = Number(volume) / totalSellVolume.value * 100 * 10
    }
    if (width < 0) {
      width = 0 + "%"
    } else if (width > 100) {
      width = 100 + "%"
    } else {
      width = width + "%"
    }
    return { width };
  } catch (e) {
    return { width }
  }

}

onUnload(() => {
  EventBus.off('transaction-buyseller-list')
})

</script>

<style scoped lang="scss">
.transaction-commission-order {
  width: 100%;
  display: flex;
  flex-direction: column;
  margin-top: 50rpx;
  box-sizing: border-box;

  .title {
    color: #000;
    font-size: 28rpx;
    font-style: normal;
    font-weight: bold;
    padding-left: 30rpx;
    box-sizing: border-box;
  }

  .data-wrapper {
    width: 100%;
    padding: 30rpx;
    display: flex;
    flex-direction: row;
    box-sizing: border-box;
  }

  .box-warpper {
    display: flex;
    flex-direction: column;
    display: flex;
    flex: 1;
    box-sizing: border-box;
  }

  .item-class {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding-top: 5rpx;
    padding-bottom: 5rpx;
    font-size: 24rpx;
    box-sizing: border-box;
    font-weight: 500;
    position: relative;
  }

  .percentage-buy-class {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    opacity: 0.1 !important;
    background: var(--common-positive-color);
    z-index: 2;
    box-sizing: border-box;
  }

  .percentage-sell-class {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    opacity: 0.1 !important;
    background: var(--common-negative-color);
    z-index: 2;
    box-sizing: border-box;
  }

  .buy-item-class {
    padding-right: 15rpx;
  }

  .sell-item-class {
    padding-left: 15rpx;
  }

  .label-class {
    color: #999;
    font-size: 24rpx;
    margin-bottom: 30rpx;
  }

  .num {
    color: #000000;
  }

  .money-positive-color {
    color: var(--common-positive-color);
  }

  .money-negative-color {
    color: var(--common-negative-color);
  }
}
</style>