<template>
    <view class="transaction-info">
      <view class="left-info-class" @click="emitData(priceData.price)">
        <span class="data-class" :style="getStyleForValue()">{{useFormatMoney(priceData.price) ||'--'}}</span>
        <view class="bom-wrapper-class">
          <span class="s1">{{priceForCurrency()}}{{config.currencyUnit}}</span>
          <span class="s2" :style="getStyleForValue()" v-if="priceData.percentage" >{{priceData.percentage>0?`+${priceData.percentage}`:priceData.percentage}}%</span>
        </view>
      </view>
      <view class="right-info-class">
        <view>{{$t("今日最高")}} <span>{{useFormatMoney(priceData.highPrice)||'--'}}</span></view>
        <view class="mt20">{{$t("今日最低")}}  <span>{{useFormatMoney(priceData.lowPrice)||'--'}}</span></view>
      </view>
    </view>
</template>

<script setup>
import config from "@/common/config";
import { useFormatMoney } from "@/common/utils";
const props = defineProps({
  currency: {
    type: Object,
    default: () => {},
  },
});

import {computed, ref} from 'vue'
import { EventBus } from '@/common/eventBus';
import {onLoad, onUnload} from "@dcloudio/uni-app";
import BigNumber from "bignumber.js";

const priceData = ref({})
onLoad(()=>{
  console.log("infoBUS")
  EventBus.on('transaction-price-info', (data) => {
    if(data.code==props.currency.code){
      priceData.value =data;
      EventBus.emit('setHyPrice', data.price)
    }
  })
})
// 法币对应的价格
function  priceForCurrency(){
  try {
     return useFormatMoney( new BigNumber(priceData.value.price || 0).multipliedBy(config.currentRate.realRate))
  }catch (e) {
    return ''
  }
}

function getStyleForValue(){
   let color =""
  let price= priceData.value.percentage
  if ( price > 0) {
    color = "var(--positive-color)";
  } else if (price < 0) {
    color = "var(--negative-color)";
  } else {
    color = "#000000";
  }
   return {color}
}

function emitData(price){
  EventBus.emit('setPrice', price)
}

onUnload(() => {
  console.log("卸载INfo")
  EventBus.off('transaction-price-info');
});
</script>

<style scoped lang="scss">
.transaction-info{
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 30rpx;
   .left-info-class{
     display: flex;
     flex-direction: column;
     align-items: flex-start;
     .data-class{
       color: var(--common-positive-color);
       text-align: center;
       font-size: 48rpx;
       font-weight: bold;
       line-height: 145%; /* 34.8px */
     }
     .bom-wrapper-class{
       display: flex;
       flex-direction: row;
       align-content: center;
       font-size: 22rpx;
       font-weight: 600;
       font-family: "Noto Sans";
       color: #000000;
       .s1{
         color: #000000;
         margin-right: 5rpx;
       }
       .s2{
        color: #41AF72;
       }
     }
   }
  .right-info-class{
    display: flex;
    flex-direction: column;
    font-size: 24rpx;
    color: #000000;
    align-items: end;
    .mt20{
      margin-top: 20rpx;
    }
    span{
      font-weight: 600;
      margin-left: 20rpx;
    }
  }
}
</style>