<template>
  <scroll-view>
    <view class="content-wrapper">
      <view class="time-tab-warpper">
        <view v-for="(item,index) in timeTabs" :key="index"  :class=" tabAcitve == item.value ? 'tab-wrapper-active' : 'tab-wrapper' "  @click="handleTab(item)" >
          <view>{{ item.label }}</view>
        </view>
     </view>
    </view>
  </scroll-view>
</template>

<script setup>
import { computed, ref } from "vue";
const tabAcitve = ref("1m");
import tui from "@/common/httpRequest.js";
import { t } from "@/hooks/useI18n";
import { onLoad } from "@dcloudio/uni-app";
const timeTabs = computed(()=>{
  return [
  { label: t("1分"), value: "1m" ,duration:1*60 },
  { label: t("5分"), value: '5m' ,duration:5*60},
  { label: t("15分"), value: '15m' ,duration:15*60},
  { label: t("30分"), value: "30m" ,duration:30*60},
  { label: t("1小时"), value: "1H" ,duration:60*60},
  { label: t("2小时"), value:  "2H",duration:2*60*60},
  { label: t("4小时"), value: "4H" ,duration:4*60*60},
  { label: t("1天"), value: "1D" ,duration:24*60*60},
  { label: t("1周"), value: "1W" ,duration:7*24*60*60},
  { label: t("1月"), value: "1M" ,duration:30*24*60*60},
]
});

const handleTab = (item) => {
  tabAcitve.value = item.value;
  getData();
  
};
const getData=()=>{
  let param={
    code:"BNBUSDT",
    bar:tabAcitve.value,
    endTs:Math.round(new Date().getTime() / 1000),
    limit:500
  }
   tui.request("/app-api/candle/list","GET",param).then(({data})=>{
   
   })
}
onLoad(()=>{
  getData()
})
</script>

<style scoped lang="scss">
.content-wrapper {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  box-sizing: border-box;
  color: #5f5d6a;
  font-size: 22rpx;
  border-bottom: 1rpx solid #efefef;
  .time-tab-warpper {
    display: flex;
    flex-direction: row;
    color: #5f5d6a;
    .tab-wrapper {
      margin-left: 30rpx;
      padding-bottom: 20rpx;
    }
  }
  .more-tab {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  .arrow-class {
    width: 25rpx;
    height: 25rpx;
  }
}
.tab-wrapper-active {
  margin-left: 30rpx;
  color: black !important;
  font-weight: bold;
  //border-bottom: 1rpx solid #000;
}

.tab-wrapper-active::after {
  content: "";
  width: 20rpx;
  display: block;
  margin: 20rpx auto 0rpx;
  border-bottom: 4rpx solid #000;
}
</style>