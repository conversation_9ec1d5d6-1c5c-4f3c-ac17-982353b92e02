<template>
    <view class="transaction-head">
      <view class="left-wrapper-class">
        <uni-icons class="icon" type="left" size="25" @click="handleBack()"></uni-icons>
      </view>
        <view class="center-wrapper-class" @click="handleCurrencySelect">
          <image class="coin-image-class" v-if="false"  src="@/static/images/common/5.png">
          </image>
          <span class="coin-text-class">{{currency?currency.name:''}}</span>
        <image class="arrow-class" src="@/static/images/transaction/arrow-down.png"></image>
      </view>
      <view class="right-wrapper-class">
        <image  :src="isCollect?'/static/images/transaction/collect-active.png':'/static/images/transaction/collect.png'" @click="handleCollect"></image>
        <image  src="@/static/images/transaction/order.png" @click="$jump(fromType==2? '/pages/commissionOrders/index':'/pages/optionOrders/index')"></image>
      </view>
    </view>
</template>

<script setup>
import {ref} from 'vue'
import tui from "@/common/httpRequest.js";
import { t } from "@/hooks/useI18n";
import {onLoad} from "@dcloudio/uni-app";
const props = defineProps({
  fromType:{
    type:Number,
    default:1
  },
  currency:{
    type:Object,
    default:{name:''}
  }
})
const emit = defineEmits(["currencyChange","handleCurrencySelect"]);
var isCollect =ref(false)

onLoad(()=>{
  getIsCollect();
})

//点击
const  handleCollect=async ()=>{
  if(isCollect.value){
    await  tui.request("/app-api/member/favorite/trade-pair/remove",'POST',{code:props.currency.code})
    // tui.toast(t("添加自选成功"))
  }else{
    await  tui.request("/app-api/member/favorite/trade-pair/add",'POST',{code:props.currency.code})
    // tui.toast(t("移除自选成功"))
  }
  isCollect.value=!isCollect.value
}


  const handleBack=()=>{
    uni.navigateBack();
  }
   const handleCurrencySelect=()=>{
    //  currencySelect.value.open();
    emit("handleCurrencySelect")
   }
   const currencyChange=(e)=>{
     emit("currencyChange",e)
   }


 //是否搜藏
  function getIsCollect(){
     tui.request("/app-api/member/favorite/trade-pair/check",'GET',{code:props.currency.code},false,false,true).then(({data})=>{
      isCollect.value =data
     })


}

</script>

<style scoped lang="scss">
.transaction-head{
  width: 750rpx;
  box-sizing: border-box;
  height: 120rpx;
  background: white;
  position: fixed;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding-left: 20rpx;
  padding-right: 20rpx;
  background: white;
  z-index: 999;
  .left-wrapper-class{
    display: flex;
    flex: 1;
  }
  .center-wrapper-class{
    display: flex;
    flex:  4;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    .coin-image-class{
      width: 40rpx;
      height: 40rpx;
      border-radius: 50%;
    }
    .coin-text-class{
      color: #000;
      text-align: center;
      font-size: 32rpx;
      font-weight: bold;
      margin-left: 5rpx;
      margin-right: 5rpx;
    }
    .arrow-class{
      width: 30rpx;
      height: 30rpx;
    }
  }
  .right-wrapper-class{
    display: flex;
    flex:  1;
    image{
      width: 35rpx;
      height: 35rpx;
      margin-left: 20rpx;
    }
  }
}
</style>