<template>
  <view class="asset-head-class">
    <template v-if="isLogin">
      <view class="current-total-asset-label">
        <span class="label-class">
          {{ $t("当前总资产") }}
        </span>
        <image
          src="@/static/images/index/3.png"
          class="img-class"
          @click="isTheAmountDisplayed = !isTheAmountDisplayed"
        ></image>
      </view>

      <view class="amount-of-money-class">
        <span class="money-class">{{
          isTheAmountDisplayed ? useFormatMoney(userGeneralBalance) : "****"
        }}</span>
        <span class="currency-unit-class">
          {{ config.currencyUnit }}
        </span>
      </view>

      <view class="warp-today-s-earnings">
        <view class="today-s-earnings">
         <view>
          <view class="label-class">{{ $t("可用") }}</view>
          <view class="value-class"
            >{{ isTheAmountDisplayed ? useFormatMoney(userBalance) : "****" }}
            {{ config.currencyUnit }}</view
          >
         </view>
         <view>
          <view class="label-class">{{ $t("合约余额") }}</view>
          <view class="value-class"
            >{{ isTheAmountDisplayed ? useFormatMoney(userContractBalance) : "****" }}
            {{ config.currencyUnit }}</view
          >
         </view>
        </view>
        <view class="today-s-earnings">
          <view><view class="label-class" @click="displayDescription"
            >{{ $t("锁定")
            }}<image
              src="@/static/images/index/15.png"
              class="img-class"
            ></image
          ></view>
          <view class="value-class"
            >{{
              isTheAmountDisplayed ? useFormatMoney(userFrozenBalance) : "****"
            }}
            {{ config.currencyUnit }}</view
          ></view>
        </view>
      </view>

      <view class="recharge-or-withdraw">
        <button
          class="recharge-class"
          @click="navRouting('/pages/recharge/index')"
        >
          {{ $t("充值") }}
        </button>
        <button
          class="withdrawal-class"
          @click="navRouting('/pages/withdrawal/index')"
        >
          {{ $t("提现") }}
        </button>
        <button
          class="detail-class"
          v-if="shuoDetail"
          @click="navRouting('/pages/capitalDetails/index')"
        >
          {{ $t("明细") }}
        </button>
      </view>
    </template>
    <template v-else>
      <view class="login-class">
        <view class="login-label-class">{{ $t("如何开启合约交易？") }}</view>
        <view class="login-tip-class">{{ $t("掌握机会，开启财富之路") }}</view>
        <view
          class="login-button-class"
          @click="navRouting('/pages/loginOrRegistration/index')"
          >{{ $t("page.登录或注册") }}</view
        >
      </view>
    </template>
  </view>
</template>
<script setup>
import config from "@/common/config";
console.log(config);
import { useFormatMoney } from "@/common/utils";
import { computed, ref } from "vue";
import { t } from "@/hooks/useI18n";
import tui from "@/common/httpRequest.js";
import BigNumber from 'bignumber.js';

const props =  defineProps({
  user: {
    type: Object,
    default: () => ({
      usdtBalance: 0,
      usdtFrozenBalance: 0,
      avatar: "",
    }),
  },
  shuoDetail: {
    type: Boolean,
    default: false,
  },
});

const isLogin = computed(() => {
  return tui.getToken();
});

const userBalance = computed(()=>{
  return new BigNumber(props.user.usdtBalance || 0).multipliedBy(config.currentRate.realRate);
})

const userFrozenBalance = computed(()=>{
  return new BigNumber(props.user.usdtFrozenBalance || 0).multipliedBy(config.currentRate.realRate);
})

const userContractBalance = computed(()=>{
  return new BigNumber(props.user.contractBalance || 0).multipliedBy(config.currentRate.realRate);
})

const userGeneralBalance = computed(()=>{
  return new BigNumber(userBalance.value || 0).plus(userFrozenBalance.value || 0).plus(userContractBalance.value || 0);
})

const defaultAvatar = new URL("@/static/images/index/3.png", import.meta.url)
  .href;

const isTheAmountDisplayed = ref(true);

function navRouting(url) {
  uni.navigateTo({
    url: url,
  });
}

function displayDescription() {
  uni.showModal({
    title: t("说明"),
    content: t("tip2"),
    showCancel: false,
    confirmText: t("知道了"),
  });
}
</script>
<style scoped lang="scss">
.asset-head-class {
  .login-class {
    padding: 32rpx 32rpx 0 32rpx;
    .login-label-class {
      font-size: 48rpx;
      font-weight: bolder;
      letter-spacing: 0rpx;
      line-height: 69.5rpx;
      color: rgba(0, 0, 0, 1);
      text-align: left;
      vertical-align: top;
      margin-bottom: 8rpx;
    }
    .login-tip-class {
      font-size: 28rpx;
      font-weight: 400;
      letter-spacing: 0rpx;
      line-height: 40.54rpx;
      color: rgba(153, 153, 153, 1);
      text-align: left;
      vertical-align: top;
      margin-bottom: 32rpx;
    }
    .login-button-class {
      font-size: 32rpx;
      font-weight: 600;
      letter-spacing: 0rpx;
      line-height: 46.4rpx;
      color: rgba(255, 255, 255, 1);
      text-align: center;
      vertical-align: top;

      width: 100%;
      height: 96rpx;
      opacity: 1;
      border-radius: 20000rpx;
      background: rgba(50, 72, 244, 1);
      box-sizing: border-box;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 auto;
    }
  }
  .recharge-or-withdraw {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 32rpx;
    column-gap: 26rpx;
    .recharge-class {
      flex: 1;

      display: flex;
      height: 76rpx;
      padding: 12rpx 0;
      justify-content: center;
      align-items: center;
      gap: 20rpx;
      flex-shrink: 0;

      color: #fff;
      font-family: "PingFang SC";
      font-size: 28rpx;
      font-style: normal;
      font-weight: bold;
      line-height: normal;

      border-radius: 2000rpx;
      background: #3248f4;
    }
    .withdrawal-class {
      flex: 1;

      display: flex;
      height: 76rpx;
      padding: 12rpx 0;
      justify-content: center;
      align-items: center;
      gap: 20rpx;
      flex-shrink: 0;

      border-radius: 2000rpx;
      background: #000;

      color: #fff;
      font-family: "PingFang SC";
      font-size: 28rpx;
      font-style: normal;
      font-weight: bold;
      line-height: normal;
    }
    .detail-class {
      flex: 1;

      display: flex;
      height: 76rpx;
      padding: 12rpx 0;
      justify-content: center;
      align-items: center;
      gap: 20rpx;
      flex-shrink: 0;

      border-radius: 2000rpx;
      background: #e8e8e8;

      color: #000;
      font-family: "PingFang SC";
      font-size: 28rpx;
      font-style: normal;
      font-weight: bold;
      line-height: normal;
    }
  }
  .warp-today-s-earnings {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    column-gap: 48rpx;
    flex-direction: column;
    box-sizing: border-box;
    margin-bottom: 32rpx;
    .today-s-earnings {
      padding: 0 32rpx;
      // padding-left: 32rpx;
      // margin-bottom: 24rpx;
      display: flex;
      justify-content: center;
      align-items: flex-start;
      column-gap: 14rpx;
      flex-direction: column;
      width: 100%;
      box-sizing: border-box;
      &>view{
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        box-sizing: border-box;
      }
      .label-class {
        color: #818181;
        font-family: "PingFang SC";
        font-size: 24rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 40rpx; /* 166.667% */
        letter-spacing: -0.64rpx;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        column-gap: 8rpx;
        .img-class {
          width: 24rpx;
          height: 24rpx;
        }
      }
      .value-class {
        color: #818181;
        font-family: "PingFang SC";
        font-size: 24rpx;
        font-style: normal;
        font-weight: bold;
        line-height: 40rpx; /* 166.667% */
        letter-spacing: -0.64rpx;
      }
    }
  }

  .amount-of-money-class {
    padding-left: 32rpx;
    margin-bottom: 20rpx;
    .money-class {
      color: #000;
      font-family: DIN;
      font-size: 56rpx;
      font-style: normal;
      font-weight: bolder;
      line-height: normal;
      margin-right: 10rpx;
    }
    .currency-unit-class {
      color: #000;
      font-family: "PingFang SC";
      font-size: 28rpx;
      font-style: normal;
      font-weight: bold;
      line-height: 40rpx; /* 142.857% */
      letter-spacing: -0.64rpx;
    }
  }
  .current-total-asset-label {
    padding-left: 32rpx;
    margin-top: 24rpx;
    margin-bottom: 4rpx;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    column-gap: 8rpx;
    .label-class {
      color: #818181;
      font-family: "PingFang SC";
      font-size: 24rpx;
      font-style: normal;
      font-weight: 400;
      line-height: 40rpx; /* 166.667% */
      letter-spacing: -0.64rpx;
    }
    .img-class {
      width: 36rpx;
      height: 36rpx;
    }
  }
}
</style>