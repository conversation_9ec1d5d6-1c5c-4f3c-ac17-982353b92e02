<template>
  <view class="swiper-item" @click="navRouting">
    <view class="content-class">
      <view class="title-class">{{ itemData.tradeName }}</view>
      <view class="label-class">
        <span class="current-index-class">{{
          useFormatMoney(itemData.currentPrice)
        }}</span>
        <span
          class="increase-in-price-class"
          :style="getStyleForValue(itemData.percentage)"
          >{{itemData.percentage<0?'':'+'}}{{ itemData.percentage }}%</span
        >
      </view>
    </view>
    <image :src="itemData.img" class="img-class"></image>
  </view>
</template>
<script setup>
import { useFormatMoney } from "@/common/utils";
import { ref,computed } from "vue";
import { onLoad, onUnload } from "@dcloudio/uni-app";
import { EventBus } from '@/common/eventBus';
const props = defineProps({
  item: Object,
});

const itemData = computed(()=> props.item);


onLoad(()=>{
  // uni.$on(props.item.code, (data) => {
  EventBus.on(props.item.tradeCode, (data) => {
    if(data.percentage){
      itemData.value.percentage = data.percentage
    }
    if(data.price){
      itemData.value.currentPrice = data.price
    }
  })
})
onUnload(()=>{
  EventBus.off(props.item.tradeCode)
})

function navRouting() {
  if(props.item.tradeCode && props.item.tradeName){
      uni.navigateTo({
      url: `/pages/tabBar/transaction?code=${props.item.tradeCode}&name=${props.item.tradeName}`,
    });
  }
}


function getStyleForValue(value) {
  let colors;
  if (value > 0) {
    colors = {
      color: "var(--positive-color)",
      background: "var(--positive-bg-color)",
    };
  } else if (value < 0) {
    colors = {
      color: "var(--negative-color)",
      background: "var(--negative-bg-color)",
    };
  } else {
    colors = "var(--zero-color)";

    colors = {
      color: "var(--zero-color)",
      background: "var(--zero-bg-color)",
    };
  }
  return colors;
}
</script>

<style scoped lang="scss">
.swiper-item {
  border-radius: 20rpx;
  position: relative;
  .content-class {
    width: 100%;
    position: absolute;
    padding-left: 32rpx;
    bottom: 0rpx;
    padding-bottom: 52rpx;
    padding-top: 20rpx;
    box-sizing: border-box;
    border-radius: 30rpx;
    z-index: 1;
    background: rgba(0,0,0,0.5);
    .title-class {
      color: rgba(255, 255, 255, 0.72);
      font-family: "PingFang SC";
      font-size: 22rpx;
      font-style: normal;
      font-weight: 400;
      line-height: 140%; /* 30.8rpx */
      letter-spacing: 0.4rpx;
      margin-bottom: 14rpx;
    }
    .label-class {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      column-gap: 14rpx;
      .current-index-class {
        color: #fff;
        text-align: right;
        font-family: DIN;
        font-size: 32rpx;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
      }
      .increase-in-price-class {
        font-family: DIN;
        font-size: 20.64rpx;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        border-radius: 2000rpx;
        // background: rgba(69, 182, 141, 0.28);

        display: flex;
        padding: 8rpx 14rpx;
        align-items: flex-start;
      }
    }
  }
}
.img-class {
  width: 330rpx;
  height: 360rpx;
  flex-shrink: 0;
  border-radius: 20rpx;
}
</style>