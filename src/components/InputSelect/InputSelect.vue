
<template>
  <view class="input-select">
    <view class="content-class" @click="open">
      <template v-if="currentLabel">
        <view class="current-label-class">
          {{ currentLabel }}
        </view>
      </template>
      <template v-else>
        <view class="placeholder-class">
          {{ placeholder }}
        </view>
      </template>

      <image
        src="@/static/images/common/1.png"
        mode=""
        class="right-icon"
      ></image>
    </view>
    <uni-popup
      ref="refPopup"
      type="bottom"
      borderRadius="20rpx 20rpx 0 0"
      background-color="#fff"
    >
      <view class="popup-class">
        <view class="popup-header-class">
          <view class="popup-title-class">{{ popupTitle }}</view>
          <view class="popup-cancel-class" @click="onCancel">
            <image
              src="@/static/images/common/2.png"
              mode=""
              class="close-icon"
            ></image>
          </view>
        </view>
        <view class="popup-content-class">
          <scroll-view scroll-y="true" class="scroll-Y">
            <radio-group @change="onConfirm">
              <uni-forms-item
                v-for="(item, index) in list"
                :key="item[keyValue]"
              >
                <label>
                  <view class="row-class">
                    <view class="right-class"
                      >{{ item[keyLable] }}
                      <radio
                        :value="String(item[keyValue])"
                        :checked="index === current"
                        color="#3248F4"
                    /></view>
                  </view>
                </label>
              </uni-forms-item>
            </radio-group>
          </scroll-view>
          <slot name="empty"></slot>
        </view>
      </view>
    </uni-popup>
  </view>
</template>
<script setup>
import { computed, ref, defineExpose} from "vue";

const props = defineProps({
  placeholder: {
    type: String,
    default: "",
  },
  list: {
    type: Array,
    default: () => [],
  },
  keyLable: {
    type: String,
    default: "label",
  },
  keyValue: {
    type: String,
    default: "value",
  },
  value: {
    type: [String, Number],
    default: "",
  },
  popupTitle: {
    type: String,
    default: "",
  },
});

const emit = defineEmits(["update:value","change"]);


const refPopup = ref(null);

const current = computed(() => {
  try {
    return props.list.findIndex((item) => item[props.keyValue] == props.value);
  }catch (e) {
    return  ''
  }

});

const currentLabel = computed(() => {
  try {
    return props.list.find((item) => item[props.keyValue] == props.value)[
      props.keyLable
    ];
  }catch (e) {
    return  ''
  }
});

const open = () => {
  refPopup.value.open();
};

const close = () => {
  refPopup.value.close();
};

defineExpose({
  open,
  close,
});


const onConfirm = (e) => {
  emit("update:value", e.detail.value);
  const item = props.list.find((item) => item[props.keyValue] == e.detail.value);
  if(e.detail.value!=props.value) emit("change", e.detail.value,item);

  close();
};

const onCancel = (e) => {
  console.log(e);
  close();
};
</script>
<style scoped lang="scss">
.input-select {
  padding: 0 30rpx;
  border-radius: 18rpx;
  border: 2rpx solid #d8d8d8;
  
  .popup-class {
    // background-color: #fff;
    .popup-content-class {
      min-height: 50vh;
      max-height: 70vh;
      ::v-deep .uni-forms-item {
        margin-bottom: 0;
        height: 108rpx;
        line-height: 108rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 30rpx;
        border-bottom: 2rpx solid #f8f8f8;
      }
      .scroll-Y {
        max-height: 70vh;
        .row-class {
          width: 100%;
          box-sizing: border-box;

          .right-class {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            color: #000;
            font-family: "PingFang SC";
            font-size: 32rpx;
            font-style: normal;
            font-weight: bold;
            line-height: normal;
            letter-spacing: -0.64rpx;
          }
        }
      }
    }
    .popup-header-class {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 32rpx 30rpx;
      .popup-title-class {
        color: #000;
        font-family: "Noto Sans";
        font-size: 32rpx;
        font-style: normal;
        font-weight: bold;
        line-height: 145%; /* 46.4rpx */
      }
      .popup-cancel-class {
        .close-icon {
          width: 40rpx;
          height: 40rpx;
          flex-shrink: 0;
        }
      }
    }
  }
  .content-class {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 88rpx;
    flex-shrink: 0;
    color: #d8d8d8;
    font-family: "PingFang SC";
    font-size: 30rpx;
    font-style: normal;
    font-weight: bold;
    line-height: 44rpx; /* 146.667% */
    letter-spacing: -0.64rpx;
  }

  
  .current-label-class {
    color: #000;
    font-family: "PingFang SC";
    font-size: 30rpx;
    font-style: normal;
    font-weight: bold;
    line-height: 44rpx; /* 146.667% */
    letter-spacing: -0.64rpx;
    min-width: 0;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .placeholder-class {
    color: #d8d8d8;
    font-family: "PingFang SC";
    font-size: 30rpx;
    font-style: normal;
    font-weight: bold;
    line-height: 44rpx; /* 146.667% */
    letter-spacing: -0.64rpx;
    min-width: 0;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .right-icon {
    width: 28rpx;
    height: 28rpx;
    flex-shrink: 0;
  }
}
</style>