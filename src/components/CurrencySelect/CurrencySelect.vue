<template >
  <uni-popup
      ref="refPopup"
      type="bottom"
      :show="true"
      background-color="#fff"
      @change="popupChange"
  >
    <view class="popup-class">
      <z-paging ref="paging" v-model="dataList" @query="queryList" :loading-more-enabled="false">
        <template #top>
          <view class="search-value-warp-class">
            <uni-easyinput
                class="search-value-class"
                trim="all"
                prefixIcon="search"
                v-model="searchValue"
                :placeholder="$t('搜索币种、交易对名称')"
                confirmType="search"
                @input="searchFun"
            ></uni-easyinput>
            <view class="popup-close-wrapper" @click="close">
              <image class="popup-close-icon" src="@/static/images/common/2.png">
              </image>
            </view>

          </view>
          <z-tabs
              :list="tabList"
              @change="tabChange"
              class="tabs-contract-line-class"
          />
          <view class="table-td-class">
            <SortIcon class="item1-class" :labelName="$t('名称')" sorting="name" @sort-changed="sortChanged"/>
            <SortIcon class="item2-class" :labelName="$t('最新价格')" sorting="currentPrice" @sort-changed="sortChanged"/>
            <SortIcon :labelName="$t('涨跌')" sorting="percentage" @sort-changed="sortChanged"/>
          </view>
        </template>

        <view class="list-warp-class">
          <CurrencyItem hover-class="hover-currency-item" v-for="item in dataList" :key="item.id" :item="item" @click="handleItem(item)"></CurrencyItem>
        </view>
      </z-paging>

    </view>
  </uni-popup>
</template>

<script setup>
import {ref} from "vue"
import config from "@/common/config";
import { t } from "@/hooks/useI18n";
import tui from "@/common/httpRequest.js";
import { sortArrayByKey } from "@/common/utils";
import { TabStatus } from "@/common/enum";
const emit = defineEmits(["currencyChange"]);


// const tabList = ref(config.assetType);
const tabList = ref(TabStatus());

const tabIndex = ref(0);
const searchValue = ref(); //搜索的關鍵字
const paging = ref(null);
const dataList = ref([]); //顯示數據

const refPopup = ref(null);
const oldDataList = ref([]);//

const tabChange=(index)=>{
  tabIndex.value = index;
  // 当切换tab或搜索时请调用组件的reload方法，请勿直接调用：queryList方法！！
  paging.value.reload();
}
const queryList=(pageNo, pageSize)=> {
  if (tabList.value[tabIndex.value].value == -1) {
    tui.request('/app-api/member/favorite/trade-pair/list', 'GET',null,false,false,true).then(res => {
      paging.value.complete(res.data);
      oldDataList.value = res.data;
    })
  } else {
    const params = {
      assetType: tabList.value[tabIndex.value].value,
      tradeType:3
    }
    tui.request('/app-api/exchange/trade-pair/all', 'GET', params,false,false,true).then(res => {
      paging.value.complete(res.data);
      oldDataList.value = res.data;
    })
  }
}



const getStyleBgForValue=(value) =>{
  let background;
  if (value ==  timeActive.value) {
    if(props.orderType==2){
      background = "var(--common-negative-color)";
    }else {
      background = "var(--common-positive-color)";
    }
  }
  return { background };
}

const getStyleColorForValue=(value) => {
  let color;
  if (value ==  timeActive.value) {
    if(props.orderType==2){
      color = "#FFFFFF";
    }else {
      color = "#FFFFFF";
    }
  }
  return { color };
}
//搜索
const searchFun=(e)=>{
  console.log(e)
  const list = oldDataList.value.filter(item=>{
    return item.name.toLowerCase().indexOf(e.toLowerCase()) > -1;
  });
  paging.value.resetTotalData(list);
}

//排序
function sortChanged(key,sort){
  const sortedList = sortArrayByKey(JSON.parse(JSON.stringify(oldDataList.value)), key, sort);
  paging.value.resetTotalData(sortedList);
}
const open = () => {
  refPopup.value.open();
};

const close = () => {
  refPopup.value.close();
};

const popupChange=({show})=>{

}
const handleItem=(item)=>{
  emit("currencyChange",item)
  close();
}

defineExpose({
  open,close
})

</script>

<style scoped lang="scss">
.popup-class {
  width: 100%;
  height: 80vh;
  display: flex;
  flex-direction: column;
  padding: 32rpx 30rpx;
  box-sizing: border-box;
    .table-td-class {
      display: flex;
      justify-content: space-between;
      box-sizing: border-box;
      padding: 0 32rpx;
      align-items: flex-end;
      height: 62rpx;
      .item1-class{
        width: 33%;
      }
      .item2-class{
        // width: 23%;
      }
    }
    .list-warp-class {
      padding: 24rpx 32rpx;
      display: flex;
      justify-content: center;
      flex-direction: column;
      row-gap: 48rpx;
    }
    .search-value-warp-class {
      display: flex;
      justify-content: center;
      align-items: center;
      column-gap: 40rpx;
      padding: 48rpx 30rpx 30rpx;
      .search-value-class {
        ::v-deep .is-input-border{
          border-radius: 200rpx;
        }
        ::v-deep input{
          height: 80rpx;

        }
      }
     .popup-close-wrapper{
        image{
          width: 40rpx;
          height: 40rpx;
        }

      }
    }
    .tabs-contract-line-class {
      border-bottom: 2rpx solid #f9f9f9;
      ::v-deep .z-tabs-list-container {
        justify-content: flex-start;
        .z-tabs-list {
          flex: 0 !important;
        }
        .z-tabs-item-title {
          text-align: center;
          font-family: "PingFang SC";
          font-size: 28rpx;
          font-style: normal;
          line-height: 32rpx; /* 114.286% */
        }
      }
    }
}
.popup-class ::v-deep.close-icon {
  width: 40 rpx !important;
  height: 40 rpx !important;
}
.hover-currency-item {
  background: rgba(0,0,0,0.1);
}
</style>