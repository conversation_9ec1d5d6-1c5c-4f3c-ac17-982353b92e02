<template >
  <uni-popup
      ref="refPopup"
      type="bottom"
      :show="true"
      borderRadius="20rpx 20rpx 0 0"
      background-color="#fff"
      @change="popupChange"
  >
    <view class="popup-class">
      <view class="popup-header-class">
        <view class="popup-title-class">{{$t("订单确认")}}</view>
        <view class="popup-cancel-class" @click="close">
          <image
              src="@/static/images/common/2.png"
              mode=""
              class="close-icon"
          ></image>
        </view>
      </view>
    <!-- 订单确认内容     -->
      <view class="popup-content-class" v-if="step==1">
          <view class="cell-wrapper">
            <span class="label-class">{{$t("交易对")}}</span>
            <span class="value-class">{{ currency.name }}</span>
          </view>
        <view class="cell-wrapper">
          <span class="label-class">{{$t("方向")}}</span>
          <span class="value-class" :style="orderType==constant.BUY_DOWN?'color:var(--common-negative-color)':'color:var(--common-positive-color)'">{{orderType==constant.BUY_DOWN?$t('买跌'):$t('买涨')}}</span>
        </view>
        <view class="cell-wrapper">
          <span class="label-class">{{$t("当前价")}}</span>
          <span class="value-class">{{useFormatMoney(form.price)}}</span>
        </view>
        <view class="time-sel-wrapper">
          <span class="label-title-class">{{$t("当前到期时间")}}</span>
          <view class="radio-wrapper" >
            <view class="radio-class" :style="getStyleBgForValue(item.id)" v-for="(item,index) in timeList"  @click="handleTimeTag(item)" :key="index">
              <span class="radio-time-class"  :style="getStyleColorForValue(item.id)">{{item.duration}}S</span>
              <span class="radio-time-desc"  :style="getStyleColorForValue(item.id)">{{item.profitRate}}% {{ $t('收益率') }}</span>
            </view>
          </view>
        </view>

        <view class="input-wrapper">
          <span class="label-title-class">{{$t("买入金额")}}</span>
          <uni-easyinput v-model="form.amount" class="input" :styles="{}" placeholderStyle="color:#979797;font-size:26rpx"	 @input="amountInput" :inputBorder="false" trim="all" :placeholder="$t('买入金额(USD)')"  type="number"/>
          <view class="desc-wrapper">
            <span class="s1">{{$t("最低购买金额为")}}</span>
            <span class="s2">{{useFormatMoney(minBuy)}} USD</span>
          </view>
        </view>

        <view class="cell-wrapper">
          <span class="label-class">{{$t("预计收益")}}</span>
          <span class="value-class">{{useFormatMoney(profitAmount)}} USD</span>
        </view>
        <view class="cell-wrapper">
          <span class="label-class">{{$t("余额")}}</span>
          <span class="value-class">{{useFormatMoney(usdtBalance)}} USD</span>
        </view>
      </view>
      <!--  订单确认2     -->
      <view v-if="step==2">
        <view class="cell-wrapper">
          <span class="label-class">{{$t("交易对")}}</span>
          <span class="value-class">0 USD</span>
        </view>
        <view class="cell-wrapper">
          <span class="label-class">{{$t("方向")}}</span>
          <span class="value-class" :style="orderType==2?'color:var(--common-negative-color)':'color:var(--common-positive-color)'">{{ orderType==2?$t('买跌'):$t('买涨') }}</span>
        </view>
        <view class="cell-wrapper">
          <span class="label-class">{{$t("买入金额")}}</span>
          <span class="value-class">24</span>
        </view>
        <view class="cell-wrapper">
          <span class="label-class">{{$t("倍数")}}</span>
          <span class="value-class">100</span>
        </view>
        <view class="cell-wrapper">
          <span class="label-class">{{$t("手续费")}}</span>
          <span class="value-class">0.2323 BTC</span>
        </view>
        <view class="cell-wrapper">
          <span class="label-class">{{$t("保证金")}}</span>
          <span class="value-class">0.2323 BTC</span>
        </view>
      </view>
      <!-- 买卖按钮   -->
      <view class="btn-wrapper-class">
        <view class="cancel"  @click="close()">{{$t("取消")}}</view>
        <view :class="orderType==constant.BUY_DOWN?'bg2':'bg1'"  @click="handleSubmit()">{{$t("确定")}}</view>
      </view>
    </view>
  </uni-popup>
</template>

<script setup>
import {ref} from "vue"
import { useFormatMoney } from "@/common/utils";
import constant from "@/common/constant";
import config from "@/common/config";
import { t } from "@/hooks/useI18n";
import tui from "@/common/httpRequest";
import {onLoad, onShow, onUnload} from "@dcloudio/uni-app";
import { EventBus } from '@/common/eventBus';
import BigNumber from 'bignumber.js';
const props =  defineProps({
  orderType: {
    type: Number,
    default: 1,
  },
  currency:{
    type: Object,
    default: {},
  }
})

const emit =  defineEmits(['confirm'])

const usdtBalance =ref();//当前余额
const profitAmount =ref() //收益金额
const profitRate =ref("30") //收益金额

//请求表单
const form =ref({
  code:"",
  amount:"",
  shortLong:"",
  duration:30,
  durationId:'',
  sendTime:"",
  price:''
})

onLoad( ()=>{
  // 获取用户信息
  EventBus.on("transaction-price-info", (data) => {
    if(props.currency.code==data.code){
      form.value.price = data.price
    }
  })

})



//订单确认步数
const step=ref(1)

const refPopup = ref(null);
const minBuy = ref(0)

const timeList=ref([])





function getStyleBgForValue(value) {
  let background;
  if (value ==  form.value.durationId) {
    if(props.orderType==constant.BUY_DOWN){
      background = "var(--common-negative-color)";
    }else {
      background = "var(--common-positive-color)";
    }
  }
  return { background };
}

function getStyleColorForValue(value) {
  let color;
  if (value ==  form.value.durationId) {
      color = "#FFFFFF";
  }
  return { color };
}


const init=()=>{
  requestTimeContract();
  getBalance();
}

//选择时间
const handleTimeTag=(item)=>{
  form.value.durationId = item.id
  form.value.duration=item.duration
  profitRate.value = Number(item.profitRate)
  minBuy.value =   Number(item.minBuy)
  calcProfitAmount();
}

//监听金额输入框
const amountInput=(value)=>{
  setTimeout(()=>{
    calcProfitAmount()
  },100)

}
//计算收益金额
const calcProfitAmount=()=>{
    profitAmount.value =   new BigNumber(form.value.amount||0).multipliedBy(profitRate.value||0).div(100)
}
const open = () => {
  refPopup.value.open();
};

const close = () => {
  refPopup.value.close();
  reset()
};

function reset(){
  form.value.amount=''
}

const popupChange=({show})=>{
  //打开
  if(show){
    step.value=1
    init();
  }else{
 //关闭
  }
}
//获取当前金额
function getBalance(){
  tui.request('/app-api/member/user/get','GET',).then(res => {
    usdtBalance.value  = res.data.usdtBalance
  })
}
//获取时间
async function requestTimeContract(){
  const {data} = await  tui.request("/app-api/trade/config/time-contract",'GET',{code:props.currency.code})
  if(data&&data.length>0){
    timeList.value = data
    //默认第一个
    form.value.durationId=timeList.value[0].id
    form.value.duration=timeList.value[0].duration
    profitRate.value = timeList.value[0].profitRate
    minBuy.value =   timeList.value[0].minBuy
  }
}

//提交
const handleSubmit=async ()=>{

  form.value.amount=Number(form.value.amount)
  if(!form.value.amount){
    tui.toast( t("请输入有效金额"))
    return;
  }
  if(form.value.amount<Number(minBuy.value)){
    tui.toast(t("不得低于最小金额")+':'+minBuy.value+'USD')
    return;
  }
  if(form.value.amount>Number(usdtBalance.value)){
    tui.toast(t("余额不足"))
    return;
  }
  form.value.code=props.currency.code
  form.value.sendTime = Date.now()
  form.value.shortLong = props.orderType
  try {
    let {data} = await tui.request('/app-api/trade/time-contract/create','POST', form.value)
    // 弹出限时框
    close()
    emit('confirm',data)
  }catch (e) {

  }



}
onUnload(()=>{
  EventBus.off('transaction-buyseller-list')
})
defineExpose({
  open,close
})

</script>

<style scoped lang="scss">
.popup-class {
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 32rpx 30rpx;
  box-sizing: border-box;
  .popup-header-class {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    .popup-title-class {
      color: #000;
      font-family: "Noto Sans";
      font-size: 32rpx;
      font-style: normal;
      font-weight: bold;
      line-height: 145%; /* 46.4rpx */
    }
    .popup-cancel-class {
      .close-icon {
        width: 40rpx;
        height: 40rpx;
        flex-shrink: 0;
      }
    }
  }
  .cell-wrapper{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin-top: 20rpx;
    margin-bottom: 10rpx;
    .label-class{
      color: #999;
      font-family: "Noto Sans";
      font-size: 26rpx;
      font-style: normal;
    }
    .value-class{
      color: #000;
      font-family: "Noto Sans";
      font-size: 26rpx;
      font-style: normal;
      font-weight: bold;
    }
  }
  .popup-content-class{
     width: 100%;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    padding-top: 30rpx;
    .radio-wrapper{
      width: 100%;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      margin-top: 15rpx;
      gap:12rpx;
      .radio-class{
        display: flex;
        width: calc(25% - 15rpx);
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding-top: 20rpx;
        padding-bottom: 20rpx;
        border-radius: 8rpx;
        border: 2rpx solid #EFEFEF;
        .radio-time-class{
          font-family: "Noto Sans";
          font-size: 26rpx;
          font-weight: bold;
          color: #5F5D6A;
        }
        .radio-time-desc{
          color: #999;
          font-family: "Noto Sans";
          font-size: 20rpx;
          margin-top: 10rpx;
        }

      }
    }
  }
  .type-buy-color{
     background: var(--common-negative-color);
     color: white !important;
  }
  .type-seller-color{
     background: var(--common-positive-color);
     color: white !important;
  }
}
.label-title-class{
  display: flex;
  flex-direction: column;
  margin-top: 18rpx;
  color: #999;
  font-family: "Noto Sans";
  font-size: 26rpx;
  font-style: normal;
  font-weight: 500;
  line-height: 145%; /* 18.85px */
}
.input{
  width: 100%;
  height: 90rpx;
  border-radius: 10px;
  background: #F7F8FA !important;
  margin-top: 20rpx;
}
.input ::v-deep .uni-easyinput__content{
  background: #F7F8FA !important;
}
.input ::v-deep input{
  color: #333333 !important;
  font-size: 26rpx;
  font-weight: bold !important;
}
.input-wrapper{
  display: flex;
  flex-direction: column;
  padding-bottom: 30rpx;
  border-bottom: 2rpx dotted #D8D8D8;
  .desc-wrapper{
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-top: 10rpx;
    .s1{
      color: #5F5D6A;
      font-family: "Noto Sans";
      font-size: 22rpx;
      font-style: normal;
      font-weight: 500;
      line-height: 145%; /* 15.95px */
    }
    .s2{
      color: #B8B8B8;
      font-family: "Noto Sans";
      font-size: 22rpx;
      font-style: normal;
      font-weight: 500;
      line-height: 145%;
      margin-left: 10rpx;
    }
  }
}

.btn-wrapper-class{
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  background: white;
  padding-bottom: 60rpx;
  padding-top: 20rpx;
  margin-top: 20rpx;

  view{
    display: flex;
    width: 330rpx;
    height: 76rpx;
    justify-content: center;
    align-items: center;
    color: white;
    font-size: 28rpx;
    font-weight: bold;
    border-radius: 38rpx;
  }
  .cancel{
    background:#EFEFEF;
    color: #979797;
    margin-right: 25rpx;
  }
  .bg1{
    background:var(--common-positive-color)
  }
  .bg2{
    background:var(--common-negative-color);
  }
}
</style>