<!--仓位item-->
<template>
  <view class="postion-item-class">
    <view class="title-warpper-class">
      <image v-if="false" class="icon-class"></image>
      <span class="title-class">{{ item.name }}</span>
      <view
        class="tag-clsss"
        :style="
          item.shortLong == constant.BUY_DOWN
            ? 'background:var(--common-negative-color)'
            : 'background:var(--common-positive-color)'
        "
        >{{
          item.shortLong == constant.BUY_DOWN ? $t("买跌") : $t("买涨")
        }}</view
      >
    </view>
    <view class="info-wrapper-class">
      <view class="info-item-class flex-start">
        <view class="label-class">
          <span>{{ $t("交割时间") }} </span>
          <uni-icons class="icon" type="help"></uni-icons>
        </view>
        <view class="data-class">{{ item.duration }}s</view>
      </view>
      <view class="info-item-class">
        <view class="label-class">
          <span>{{ $t("下单金额") }}</span>
        </view>
        <view class="data-class">${{ useFormatMoney(item.amount) }}</view>
      </view>
      <view class="info-item-class">
        <view class="label-class">
          <span>{{ $t("成交价") }}</span>
        </view>
        <view class="data-class">{{ useFormatMoney(item.orderPrice) }}</view>
      </view>
      <view class="info-item-class">
        <view class="label-class">
          <span>{{ $t("现价") }}</span>
        </view>
        <view class="data-class" :style="getStyleForValue()">{{
          useFormatMoney(currentPrice || item.currentPrice)
        }}</view>
      </view>
    </view>
    <progress
      class="progress-class"
      :percent="countTimePercent"
      backgroundColor="#F8F8F8"
      activeColor="#3248f4"
      stroke-width="8"
    />
    <view class="date-time-class">{{ useFormatTime(item.sendTime) }}</view>
  </view>
</template>

<script setup>
import { useFormatMoney } from "@/common/utils";
import constant from "@/common/constant";
import { useFormatTime } from "@/common/utils";
import { onLoad, onUnload } from "@dcloudio/uni-app";
import { computed, ref } from "vue";
import { EventBus } from "@/common/eventBus";
const currentPrice = ref("");
const pricePercentage = ref(0); // 当前价格的涨幅
const countTimePercent = ref(0); //限时合约进度
const emit = defineEmits(["reload"]);

const props = defineProps({
  item: {
    type: Object,
    default: () => {},
  },
});
const timer = ref(null); //定时器

onLoad(() => {
  // uni.$on(props.item.code, (data) => {
  EventBus.on(props.item.code, (data) => {
    currentPrice.value = data.price;
    pricePercentage.value = data.pricePercentage;
  });
  countTime();
});
onUnload(() => {
  EventBus.off(props.item.code);
});

// 限时进度
function countTime() {
  let endTime = props.item.sendTime + props.item.duration * 1000;
  let nowTime = Date.now();
  if (endTime - nowTime < 0) {
    countTimePercent.value = 0;
  } else {
    timer.value = setInterval(() => {
      if (endTime < Date.now()) {
        countTimePercent.value = 0;
        clearInterval(timer.value);
        timer.value = null;
        emit("reload",props.item);
      } else {
        countTimePercent.value =
          ((props.item.duration - (endTime - Date.now()) / 1000) * 100) /
          props.item.duration;
      }
    }, 1000);
  }
}

function getStyleForValue() {
  let color;
  if (pricePercentage.value > 0) {
    color = "var(--common-positive-color)";
  } else if (pricePercentage.value < 0) {
    color = "var(--common-negative-color)";
  } else {
    color = "#000000";
  }
  return { color };
}
</script>

<style scoped lang="scss">
.postion-item-class {
  display: flex;
  flex-direction: column;
  padding: 30rpx 20rpx;
  box-sizing: border-box;
  border-bottom: 1rpx solid #f8f8f8;
  .title-warpper-class {
    display: flex;
    flex-direction: row;
    align-items: center;
    box-sizing: border-box;
    image {
      width: 40rpx;
      height: 40rpx;
      background: gold;
      border-radius: 50%;
    }
    .title-class {
      color: #000;
      text-align: center;
      font-size: 32rpx;
      font-style: normal;
      font-weight: bold;
      letter-spacing: 0.4rpx;
      margin-left: 16rpx;
    }
    .tag-clsss {
      border-radius: 4rpx;
      padding: 2rpx 8rpx;
      background: var(--common-positive-color);
      color: white;
      font-size: 20rpx;
      letter-spacing: 0.4rpx;
      margin-left: 12rpx;
    }
  }
  .info-wrapper-class {
    margin-top: 32rpx;
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    .info-item-class {
      width: 100%;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .label-class {
        color: #999;
        text-align: center;
        font-size: 22rpx;
        font-weight: 400;
        line-height: 140%; /* 15.4px */
        letter-spacing: 0.4rpx;
        display: flex;
        flex-direction: row;
        align-items: center;
        .icon {
          margin-left: 8rpx;
        }
      }
      .data-class {
        margin-top: 20rpx;
        color: #000;
        text-align: center;
        font-size: 26rpx;
        font-style: normal;
        line-height: 140%; /* 18.2px */
        letter-spacing: 0.4rpx;
        font-weight: bold;
      }
    }
  }
  .progress-class {
    margin-top: 32rpx;
    padding-left: 20rpx;
    padding-right: 20rpx;
  }
  .progress-class ::v-deep .uni-progress-bar {
    flex: 1;
    border-radius: 60rpx !important;
  }
  .progress-class ::v-deep .uni-progress-inner-bar {
    border-radius: 60px !important;
  }
  .date-time-class {
    color: #999999;
    font-size: 22rpx;
    margin-top: 22rpx;
    padding-left: 20rpx;
    margin-bottom: 15rpx;
  }
}
.up-text-color {
  color: var(--common-positive-color) !important;
}
.up-bg-color {
  background: var(--common-positive-color) !important;
}
.down-text-color {
  color: var(--common-negative-color) !important;
}
.up-bg-color {
  background: var(--common-negative-color) !important;
}
</style>

<style>
</style>