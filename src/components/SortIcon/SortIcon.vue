<template>
  <view class="sort-warp-class" @click="toggleSort">
    <view class="label-class">{{ labelName }}</view>
    <view class="sort-class">
      <image
        v-if="sortState === 0 || sortState === 2"
        class="sort-icon-class"
        src="@/static/images/market/2.png"
      ></image>
      <image
        v-if="sortState === 1"
        class="sort-icon-class"
        src="@/static/images/market/4.png"
      ></image>
      <image
        v-if="sortState === 0 || sortState === 1"
        class="sort-icon-class"
        src="@/static/images/market/3.png"
      ></image>

      <image
        v-if="sortState === 2"
        class="sort-icon-class"
        src="@/static/images/market/5.png"
      ></image>
    </view>
  </view>
</template>
  
<script setup>
import { ref } from "vue";
const props =  defineProps({
    labelName:String,
    sorting:String,
})
const emit = defineEmits(["sort-changed"]);
const sortState = ref(0);

const sortStateMap = ["none","asc","desc"];

function toggleSort() {
  sortState.value = (sortState.value + 1) % 3;
  emit("sort-changed", props.sorting,sortStateMap[sortState.value]);
}
</script>

<style scoped lang="scss">
.sort-warp-class {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  column-gap: 9rpx;
  .label-class {
    color: #adb2be;
    text-align: center;
    font-family: "PingFang SC";
    font-size: 24rpx;
    font-style: normal;
    font-weight: 400;
    line-height: 36rpx; /* 150% */
  }

  .sort-class {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    row-gap: 4rpx;
    .sort-icon-class {
      width: 12rpx;
      height: 6rpx;
      flex-shrink: 0;
    }
  }
}
</style>