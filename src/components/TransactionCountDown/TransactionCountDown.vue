<template>
  <view class="count-wrapper">
    <!--    <CmdProgress v-if="(orderInfo.tradeStatus==constant.ORDER_STATUS_WAIT)&&unsideShow" class="cmd-progress" :percent="getPercent()" type="circle" :width="60"  :stroke-color="orderInfo.shortLong==constant.BUY_DOWN?'#FB5972':'#05C06F'" @click="open()">-->
    <!--        <template #center>-->
    <!--          <view class="progress-center-wrapper" :style="orderInfo.shortLong==constant.BUY_DOWN?'color:var(&#45;&#45;common-negative-color)':'color:var(&#45;&#45;common-positive-color)'">-->
    <!--            <span class="time">  {{countDownTime}} s</span>-->
    <!--          </view>-->
    <!--        </template>-->
    <!--    </CmdProgress>-->

    <uni-popup ref="refPopup" type="center" borderRadius="20rpx 20rpx 0 0" background-color="#fff"
      @change="popupChange">
      <view class="popup-class">
        <view>
          <view class="trade-success" v-if="(orderInfo.tradeStatus == constant.ORDER_STATUS_SUCCESS)">
            <span :style="getStyleForValue(orderInfo.profit)">{{ orderInfo.profit > 0 ? '+' : '' }}{{
              useFormatMoney(orderInfo.profit)
              }}</span>
          </view>
          <CmdProgress v-else :percent="getPercent()" type="circle"
            :stroke-color="orderType == constant.BUY_DOWN ? '#FB5972' : '#05C06F'">
            <template #center>
              <view class="progress-center-wrapper"
                :style="orderType == constant.BUY_DOWN ? 'color:var(--common-negative-color)' : 'color:var(--common-positive-color)'">
                <span class="time"> {{ orderInfo.duration }} s</span>
                <span class="progress" v-if="countDownTime!=0"> {{ countDownTime }}</span>
                <uni-icons class="spinner-cycle" v-if="countDownTime==0" type="spinner-cycle"  size="30"></uni-icons>
              </view>
            </template>
          </CmdProgress>
        </view>
        <!-- 订单确认内容     -->
        <view class="popup-content-class">
          <view class="cell-wrapper">
            <span class="label-class">{{ $t("交易对") }}</span>
            <span class="value-class">{{ orderInfo.name }}</span>
          </view>
          <view class="cell-wrapper">
            <span class="label-class">{{ $t("方向") }}</span>
            <span class="value-class"
              :style="orderInfo.shortLong == constant.BUY_DOWN ? 'color:var(--common-negative-color)' : 'color:var(--common-positive-color)'">{{
                orderInfo.shortLong == constant.BUY_DOWN ?
                  $t('买跌') : $t('买涨')}}</span>
          </view>
          <view class="cell-wrapper">
            <span class="label-class">{{ $t("购买价") }}</span>
            <span class="value-class">{{ useFormatMoney(orderInfo.orderPrice) }}</span>
          </view>
          <view class="cell-wrapper">
            <span class="label-class">{{orderInfo.tradeStatus == constant.ORDER_STATUS_SUCCESS?$t("平仓价"):  $t("当前价")}}</span>
            <span class="value-class">{{orderInfo.tradeStatus == constant.ORDER_STATUS_SUCCESS?useFormatMoney(orderInfo.settlePrice) : useFormatMoney(currenyPrice) }}</span>
          </view>

          <view class="cell-wrapper">
            <span class="label-class">{{ $t("买入金额") }}</span>
            <span class="value-class">{{ useFormatMoney(orderInfo.amount) }}</span>
          </view>

          <view class="cell-wrapper">
            <span class="label-class">{{ orderInfo.tradeStatus == constant.ORDER_STATUS_SUCCESS ? t("实际盈亏") : $t("预计盈亏")
              }}</span>
            <span class="value-class" :style="getStyleForValue(orderInfo.profit)">{{ useFormatMoney(orderInfo.profit) }}
              USD</span>
          </view>
        </view>
        <!-- 买卖按钮   -->
        <view class="btn-wrapper-class"
          :style="orderType == constant.BUY_DOWN ? 'color:var(--common-negative-color)' : 'color:var(--common-positive-color)'"
          @click="close()">
          {{ $t("继续交易") }}
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref } from "vue"
import { useFormatMoney } from "@/common/utils";
import constant from "@/common/constant";
import { t } from "@/hooks/useI18n";
import tui from "@/common/httpRequest";
import { onLoad, onShow, onUnload ,onHide} from "@dcloudio/uni-app";
import { EventBus } from '@/common/eventBus';
import CmdProgress from "@/components/CmdProgress/CmdProgress";
const props = defineProps({
  orderType: {
    type: Number,
    default: 1,
  },
  currency: {
    type: Object,
    default: {},
  }
})
let unsideShow = ref(false) //外层倒计时
const refPopup = ref()
const balance = ref();//当前余额
const currenyPrice = ref() //当前价格
const profitAmount = ref() //收益金额
const timer = ref(null) //定时器

//请求表单
const orderInfo = ref({
  id: "",
  code: "",
  name: "",
  amount: "",
  shortLong: 1,
  duration: 30,
  sendTime: "",
  profit: "",
  profitRate: "",
  orderPrice: '',
  winProfit:"",
  loseProfit:"",
  tradeStatus: -1
})

const countDownTime = ref(60) //倒计时

onLoad(() => {
  // 获取用户信息
  EventBus.on('transaction-price-info', (data) => {
    if(orderInfo.value.tradeStatus==constant.ORDER_STATUS_WAIT) {
      if(props.currency.code==data.code){
        currenyPrice.value = data.price
        calcProfit()
      }
    }
  })

})
//动态计算盈亏 盈亏 = 买入金额*利润率*（正负1）
function calcProfit() {
  //还在开奖中才计算
  if (orderInfo.value.tradeStatus === constant.ORDER_STATUS_WAIT) {
    if ((orderInfo.value.shortLong == constant.BUY_DOWN &&(Number(currenyPrice.value) < Number(orderInfo.value.orderPrice)) ) || (orderInfo.value.shortLong == constant.BUY_UP && (Number(currenyPrice.value) > Number(orderInfo.value.orderPrice)))) {
      //正1  = 用户做空且现价小于买入价 或 用户做多且现价大于买入价    
      orderInfo.value.profit = orderInfo.value.winProfit
    } else if ((orderInfo.value.shortLong == constant.BUY_DOWN &&(Number(currenyPrice.value) > Number(orderInfo.value.orderPrice)))|| (orderInfo.value.shortLong == constant.BUY_UP && (Number(currenyPrice.value) < Number(orderInfo.value.orderPrice)))) {
      //负1  = 用户做空且现价大于买入价 或  用户做多且现价小于买入价 
      orderInfo.value.profit = orderInfo.value.loseProfit
    }else{
    }
  }
}

function getStyleForValue(value) {
  let color;
  if (value > 0) {
    color = "var(--common-positive-color)";
  } else if (value < 0) {
    color = "var(--common-negative-color)";
  } else {
    color = "var(--zero-color)";
  }
  return { color };
}

const getPercent = () => {
  try {
    let duration = Number(orderInfo.value.duration)
    return (duration - countDownTime.value) * 100 / duration
  } catch (e) {
    console.log(e)
  }
}
const init = async (orderId) => {
  let { data } = await tui.request("/app-api/trade/time-contract/order-detail", 'GET', { id: orderId })
  orderInfo.value = data
  open();
  if (orderInfo.value.tradeStatus == constant.ORDER_STATUS_WAIT) {
    let { duration } = orderInfo.value
    setCountDownTime(duration);
  }
};

const setCountDownTime = (duration) => {
  //开始倒计时
  countDownTime.value = duration
  if (timer.value) clearInterval(timer.value)
  timer.value = setInterval(() => {
    if (countDownTime.value >= 1) {
      countDownTime.value--
    } else {
      countDownTime.value = 0;
      clearInterval(timer.value)
      timer.value = null
      // 获取交易结果
      requestOrderDetail()
    }
  }, 1000)
}

const requestOrderDetail = () => {
  tui.request("/app-api/trade/time-contract/order-detail", 'GET', { id: orderInfo.value.id }, false, false, true).then(({ data }) => {
    orderInfo.value = data
  })
}


const open = () => {
  refPopup.value.open();
};

const close = () => {
  refPopup.value.close();
  if (timer.value) clearInterval(timer.value)
};


const popupChange = ({ show }) => {
  try {
    unsideShow.value = show
  } catch (e) {
    console.log(e)
  }

}
onUnload(() => {
  if (timer.value) clearInterval(timer.value)
})
//倒计时
onShow(() => {
  //关闭状态
  if(!unsideShow.value){
    return;  
  }
  if (!orderInfo.value.id) {
    return;
  }
  if (orderInfo.value.tradeStatus == constant.ORDER_STATUS_WAIT) {
    console.log("onShow倒计时")
    if (timer.value) clearInterval(timer.value)
    let endTime = orderInfo.value.sendTime + orderInfo.value.duration * 1000;
    let nowTime = Date.now();
    if (endTime - nowTime < 0) {
      countDownTime.value = 0;
      requestOrderDetail()
    } else {
      setCountDownTime(Math.ceil((endTime - nowTime) /1000))
    }
  }
})
onHide(() => {
  if (timer.value) clearInterval(timer.value)
})
defineExpose({
  open, close, init
})

</script>

<style scoped lang="scss">
.count-wrapper {
  position: relative;
}

.popup-class {
  width: 650rpx;
  display: flex;
  flex-direction: column;
  padding: 32rpx 30rpx 0rpx 30rpx;
  box-sizing: border-box;
  justify-content: center;
  align-items: center;

  .popup-header-class {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;

    .popup-title-class {
      color: #000;
      font-family: "Noto Sans";
      font-size: 32rpx;
      font-style: normal;
      font-weight: bold;
      line-height: 145%;
      /* 46.4rpx */
    }

    .popup-cancel-class {
      .close-icon {
        width: 40rpx;
        height: 40rpx;
        flex-shrink: 0;
      }
    }
  }

  .cell-wrapper {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin-top: 20rpx;
    margin-bottom: 10rpx;

    .label-class {
      color: #999;
      font-family: "Noto Sans";
      font-size: 26rpx;
      font-style: normal;
    }

    .value-class {
      color: #000;
      font-family: "Noto Sans";
      font-size: 26rpx;
      font-style: normal;
      font-weight: bold;
    }
  }

  .popup-content-class {
    width: 100%;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    padding-top: 30rpx;

    .radio-wrapper {
      width: 100%;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: space-between;
      margin-top: 15rpx;

      .radio-class {
        display: flex;
        width: calc(25% - 15rpx);
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding-top: 20rpx;
        padding-bottom: 20rpx;
        border-radius: 8rpx;
        border: 2rpx solid #EFEFEF;

        .radio-time-class {
          font-family: "Noto Sans";
          font-size: 26rpx;
          font-weight: bold;
          color: #5F5D6A;
        }

        .radio-time-desc {
          color: #999;
          font-family: "Noto Sans";
          font-size: 20rpx;
          margin-top: 10rpx;
        }

      }
    }
  }

  .type-buy-color {
    background: var(--common-negative-color);
    color: white !important;
  }

  .type-seller-color {
    background: var(--common-positive-color);
    color: white !important;
  }
}

.label-title-class {
  display: flex;
  flex-direction: column;
  margin-top: 18rpx;
  color: #999;
  font-family: "Noto Sans";
  font-size: 26rpx;
  font-style: normal;
  font-weight: 500;
  line-height: 145%;
  /* 18.85px */
}

.input {
  width: 100%;
  height: 90rpx;
  border-radius: 10px;
  background: #F7F8FA !important;
  margin-top: 20rpx;
}

.input ::v-deep .uni-easyinput__content {
  background: #F7F8FA !important;
}

.input ::v-deep input {
  color: #333333 !important;
  font-size: 26rpx;
  font-weight: bold !important;
}

.input-wrapper {
  display: flex;
  flex-direction: column;
  padding-bottom: 30rpx;
  border-bottom: 2rpx dotted #D8D8D8;

  .desc-wrapper {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-top: 10rpx;

    .s1 {
      color: #5F5D6A;
      font-family: "Noto Sans";
      font-size: 22rpx;
      font-style: normal;
      font-weight: 500;
      line-height: 145%;
      /* 15.95px */
    }

    .s2 {
      color: #B8B8B8;
      font-family: "Noto Sans";
      font-size: 22rpx;
      font-style: normal;
      font-weight: 500;
      line-height: 145%;
      margin-left: 10rpx;
    }
  }
}

.btn-wrapper-class {
  width: 100%;
  height: 100rpx;
  border-top: 1rpx solid #E5E6EB;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  background: white;
  color: rgba(0, 188, 122, 1);
  font-size: 32rpx;
  font-weight: 500d;
}

::v-deep .uni-popup__wrapper {
  border-radius: 30rpx !important;
}

.cmd-progress {
  background: white;
  border-radius: 50%;
}

.progress-center-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: var(--common-positive-color);

  .time {
    font-size: 28rpx;
  }

  .progress {
    font-size: 56rpx;
    margin-top: 20rpx;
  }

  .font-size-m {
    font-size: 28rpx;
  }
}

.trade-success {
  font-size: 50rpx;

}


@keyframes loading {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.spinner-cycle {
  margin-top: 20rpx;
  animation: loading 2s linear infinite;
}

</style>