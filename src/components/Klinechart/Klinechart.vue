<template>
  <view class="chart-wrapper">
    <!--   时间选择   -->
    <scroll-view>
      <view class="content-wrapper">
        <view class="time-tab-warpper">
          <view
            v-for="(item, index) in timeTabs"
            :key="index"
            :class="
              tabTimeAcitve.value == item.value
                ? 'tab-wrapper-active'
                : 'tab-wrapper'
            "
            @click="handleTimeTab(item)"
          >
            <view>{{ item.label }}</view>
          </view>
        </view>
      </view>
    </scroll-view>

    <view id="klinechart" class="klinechart-wrapper"> </view>
    <uni-icons
      v-if="isLoading"
      class="spinner-cycle"
      type="spinner-cycle"
      size="30"
    ></uni-icons>
  </view>
</template>

<script setup>
import { computed, onMounted, onUnmounted, ref, watch, nextTick } from "vue";
import { init, dispose } from "klinecharts";
import { EventBus } from "@/common/eventBus";
import tui from "@/common/httpRequest.js";
import { t } from "@/hooks/useI18n";
import { getPrecision, useFormatMoney } from "@/common/utils";
import { onHide, onLoad, onShow, onUnload } from "@dcloudio/uni-app";
import BigNumber from 'bignumber.js';

const props = defineProps({
  currency: {
    type: Object,
    default: { code: "BNBUSDT" },
  },
});
const lastKlineData = ref(); // 最后一根K线数据
const tabTimeAcitve = ref({ label: t("1分"), value: "1m", duration: 1 * 60 });
const timeTabs = computed(() => {
  return [
    { label: t("1分"), value: "1m", duration: 1 * 60 },
    { label: t("15分"), value: "15m", duration: 15 * 60 },
    { label: t("30分"), value: "30m", duration: 30 * 60 },
    { label: t("1小时"), value: "1H", duration: 60 * 60 },
    { label: t("4小时"), value: "4H", duration: 4 * 60 * 60 },
    { label: t("1天"), value: "1D", duration: 24 * 60 * 60 },
    { label: t("1周"), value: "1W", duration: 7 * 24 * 60 * 60 },
  ];
});

const LIMIT_COUNT = 100; // 一次性加载的数据
const isLoading = ref(true);
let dataCache = []; // 用于缓存 WebSocket 推送的数据
let isChartInitialized = false; // 用于标记 K 线图是否初始化完成
let isSwitchingTimeTab = false; // 标记是否正在切换时间周期


// 获取数据
const getData = async (timestamp) => {
  const endTs = timestamp
    ? timestamp / 1000
    : Math.round(new Date().getTime() / 1000);
  const param = {
    code: props.currency.code,
    bar: tabTimeAcitve.value.value,
    endTs: endTs,
    limit: LIMIT_COUNT,
  };
  const { data } = await tui.request(
    "/app-api/candle/list",
    "GET",
    param,
    false,
    false,
    true
  );
  const historyData = data.map((item) => {
    return {
      timestamp: Number(item.timestamp) * 1000,
      open: Number(item.openPrice),
      high: Number(item.highPrice),
      low: Number(item.lowPrice),
      close: Number(item.closePrice),
      volume: Number(item.volume),
      turnover: Number(item.turnover),
    };
  });
  const moreDataAvailable = data.length >= LIMIT_COUNT;
  return { loadedData: historyData.reverse(), moreDataAvailable };
};

let chart = null;
const buffer = ref([]);

onShow(() => {
  nextTick(() => {
    initKlineChart();
  });
});

onLoad(() => {
  // nextTick(()=>{
  //   initKlineChart();
  // })
});

isChartInitialized = true; // 标记图表已经初始化
   // 检查是否有缓存数据，并在图表初始化后处理
   if (dataCache.length > 0) {
    dataCache.forEach((cachedData) => handleWebSocketData(cachedData));
    dataCache = []; // 清空缓存，避免重复处理
  }

async function initKlineChart() {
  isLoading.value = true;
  
  // 初始化 K 线图
  chart = init("klinechart", { decimalFoldThreshold: 10 });

  // 请求数据并应用到图表
  const { loadedData, moreDataAvailable } = await getData();
  chart.applyNewData(loadedData,loadedData.length>=100?true:false); // 这里确保数据已经应用到图表

  // 设置其他样式和指标
  chart.setStyles({
    candle: {
      tooltip: {
        showRule: "follow_cross",
        showType: "rect",
        rect: { color: "rgba(0, 0, 0, 0.5)" },
        text: { color: "#ffffff" },
      },
    },
  });

  chart.createIndicator({ name: "VOL", calcParams: [20, 30] }, false, {
    height: 100,
    dragEnabled: false,
  });
  chart.createIndicator("MACD", false, { height: 100, dragEnabled: false });
  chart.overrideIndicator({ name: "MACD" });
  chart.setOffsetRightDistance(20);
  chart.setMaxOffsetLeftDistance(10);

  // 加载更多数据的回调
  chart.setLoadDataCallback(async ({ type, data, callback }) => {
    const forwardMore = type === "forward";
    if (forwardMore && data?.timestamp) {
      const { loadedData, moreDataAvailable } = await getData(data.timestamp);
      callback(loadedData, moreDataAvailable);
    } else {
      callback([], false);
    }
  });

  // 这里设置为 true，确保数据已加载到图表后
  isChartInitialized = true; // 标记图表已经初始化
  isLoading.value = false;
}

function handleWebSocketData(data) {
    // //
    // if(buffer.value.length==1&&lastKlineData.value){
    //   buffer.value[0].price =  lastKlineData.value.close
    // }
    // const open =  Number(buffer.value[0].price);
    // const close = Number(buffer.value[buffer.value.length - 1].price);
    // const high = Math.max(...buffer.value.map(data => data.price));
    // const low = Math.min(...buffer.value.map(data => data.price));
    // const volume = buffer.value.reduce((sum, data) => sum + Number(data.volume || 0), 0);

    // const newData = {
    //   timestamp: buffer.value[0].timestamp,
    //   open,
    //   close,
    //   high,
    //   low,
    //   volume
    // };
    // console.log("-------新数据",newData)
    // chart.updateData(newData);
    // if ((buffer.value[buffer.value.length - 1].timestamp - buffer.value[0].timestamp) / 1000 > tabTimeAcitve.value.duration) {
    //   console.log("-------闭    盘")
    //   lastKlineData.value = newData //最后一根数据
    //   buffer.value = [];
    //   console.log("-------重新开盘")
    // }

    if (!isChartInitialized || isSwitchingTimeTab) {
    // 如果图表未初始化或者正在切换时间周期，将数据缓存
    dataCache.push(data);
    return;
  }

  const dataList = chart.getDataList();
  if (dataList.length === 0) {
    console.log("图表还没有数据，跳过数据更新");
    return;
  }

  const lastData = dataList.at(-1); // 获取最后一根K线数据
  if (!lastData || !lastData.timestamp) {
    console.log("没有找到最后一根K线数据，跳过数据更新");
    return;
  }

  if (data.timestamp < lastData.timestamp + tabTimeAcitve.value.duration * 1000) {
    lastData.close = data.price;
    lastData.high = Math.max(lastData.high, data.price); // 更新最高价
    lastData.low = Math.min(lastData.low, data.price);   // 更新最低价
    lastData.volume = new BigNumber(lastData.volume).plus(data.volume || 0).toNumber(); // 更新成交量
    console.log("-------更新数据", lastData)
    chart.updateData(lastData);
  } else {
    const newData = {
      timestamp: data.timestamp,
      open: data.price,
      close: data.price,
      high: data.price,
      low: data.price,
      volume: new BigNumber(data.volume || 0).toNumber(),
    };
    console.log("-------新数据", newData)
    chart.updateData(newData);
  }
}

onMounted(() => {
  EventBus.on("transaction-price-info", (data) => {
    //设置页面标题
    // if(props.currency.name && data.price){
    //   uni.setNavigationBarTitle({
    //     title:useFormatMoney(data.price) + ' | ' + props.currency.name
    //   });
    // }

    try {
      if (data.code == props.currency.code) {
        //pass时间轴一样
        // if(lastKlineData.value && data.timestamp == lastKlineData.value.timestamp){
        //   return;
        // }
        // buffer.value.push(data);
        //  console.log("-------buffer",buffer.value)
        console.log("ws-------", data)
        handleWebSocketData(data);
      }
    } catch (e) {
      console.log(e);
    }
  });
});

onUnload(() => {
  console.log("卸载kline");
  EventBus.off("transaction-price-info");
  dispose("klinechart");
});

onHide(() => {
  dispose("klinechart");
});

const handleTimeTab = async (item) => {
  tabTimeAcitve.value = item;
  
  // 暂停 WebSocket 数据处理并缓存数据
  isSwitchingTimeTab = true;
  
  // 请求新的时间周期的数据
  const { loadedData, moreDataAvailable } = await getData();

  // 将新的数据应用到图表
  chart.applyNewData(loadedData,loadedData.length>=100?true:false);

  // 恢复 WebSocket 数据处理，并处理缓存的数据
  isSwitchingTimeTab = false;

  if (dataCache.length > 0) {
    dataCache.forEach((cachedData) => handleWebSocketData(cachedData));
    dataCache = []; // 清空缓存
  }
  // initData();
};

//设置精度 buff 最后一根K图
function initData() {
  // 初始化非更多加载
  buffer.value = []; //清空
  lastKlineData.value = null;
  let kListData = chart.getDataList();
  if (kListData.length > 0) {
    lastKlineData.value = kListData[kListData.length - 1]; //最后一根K线
    try {
      //获取精度
      let pricePrecision = getPrecision(kListData[kListData.length - 1].high);
      chart.setPriceVolumePrecision(pricePrecision > 2 ? pricePrecision : 2, 2);
    } catch (e) {
      console.log(e);
    }
  }
}
</script>



<style lang="scss">
.chart-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  position: relative;
}

.content-wrapper {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  box-sizing: border-box;
  color: #5f5d6a;
  font-size: 22rpx;
  border-bottom: 1rpx solid #efefef;

  .time-tab-warpper {
    display: flex;
    flex-direction: row;
    color: #5f5d6a;

    .tab-wrapper {
      margin-left: 30rpx;
      padding-bottom: 20rpx;
    }
  }

  .more-tab {
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  .arrow-class {
    width: 25rpx;
    height: 25rpx;
  }
}

.tab-wrapper-active {
  margin-left: 30rpx;
  color: black !important;
  font-weight: bold;
  //border-bottom: 1rpx solid #000;
}

.tab-wrapper-active::after {
  content: "";
  width: 20rpx;
  display: block;
  margin: 20rpx auto 0rpx;
  border-bottom: 4rpx solid #000;
}

.klinechart-wrapper {
  height: 900rpx;
}

@keyframes loading {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.spinner-cycle {
  margin-top: 20rpx;
  animation: loading 2s linear infinite;
  position: absolute;
  color: #5f5d6a;
  top: 200rpx;
  left: 350rpx;
}
</style>
