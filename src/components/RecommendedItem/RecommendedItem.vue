<template>
  <view class="recommended-item-class" @click="navTransaction">
    <view class="title-class">{{itemData.name}}</view>
    <view class="amount-of-money-class">{{ useFormatMoney(itemData.currentPrice) }}</view>
    <view class="interest-rate-class" :style="getStyleForValue(itemData.percentage)"
      >{{itemData.percentage<0?'':'+'}}{{itemData.percentage}}%</view
    >
  </view>
</template>
<script setup>
import { useFormatMoney } from "@/common/utils";
import { onLoad, onUnload } from "@dcloudio/uni-app";
import { computed } from "vue";
import { EventBus } from '@/common/eventBus';
const props =  defineProps({
  item: {
    type: Object,
    default: ()=>{},
  },
});

//自定义出来
const itemData = computed(()=> props.item);

function navTransaction(){
  uni.navigateTo({
    url: `/pages/tabBar/transaction?code=${props.item.code}&name=${props.item.name}`,
  });
}

onLoad(()=>{
  // uni.$on(props.item.code, (data) => {
  EventBus.on(props.item.code, (data) => {
    if(data.percentage){
      itemData.value.percentage = data.percentage
    }
    if(data.price){
      itemData.value.currentPrice = data.price
    }
  })
})

onUnload(()=>{
  // uni.$off(props.item.code);
  EventBus.off(props.item.code);
})

function getStyleForValue(value) {
  let color;
  if (value > 0) {
    color = "var(--positive-color)";
  } else if (value < 0) {
    color = "var(--negative-color)";
  } else {
    color = "var(--zero-color)";
  }
  return { color };
}
</script>
<style scoped lang="scss">
.recommended-item-class {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    row-gap: 8rpx;
  .title-class {
    color: #aeaeae;
    font-family: "PingFang SC";
    font-size: 22rpx;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }
  .amount-of-money-class {
    color: #000;
    font-family: DIN;
    font-size: 32rpx;
    font-style: normal;
    font-weight: bolder;
    line-height: normal;
  }
  .interest-rate-class {
    color: #45b68d;
    text-align: center;
    font-family: DIN;
    font-size: 26rpx;
    font-style: normal;
    font-weight: bold;
    line-height: normal;
  }
}
</style>