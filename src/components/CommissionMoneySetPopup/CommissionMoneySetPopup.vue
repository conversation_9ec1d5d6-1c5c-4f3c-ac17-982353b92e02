<template>
  <uni-popup
    ref="refPopup"
    type="center"
    :show="true"
    borderRadius="20rpx"
    background-color="#fff"
    @change="popupChange"
    class="commission-money-set-popup"
  >
    <uni-popup-dialog
      class="uni-popup-dialog-class"
      mode="base"
      :title="$t('设置止盈止损')"
      :duration="2000"
      :before-close="true"
      @close="close"
      @confirm="confirm"
    >
      <template #default>
        <view class="popup-content-wrapper">
          <view class="item-wrapper gap">
            <view class="label-value">{{ $t("止盈价") }}</view>
            <view class="value-wrapper">
              <!-- <uni-number-box :min="0" :max="999999999999" v-model="profitMakingPrice" :step="0.000001" ></uni-number-box> -->
              <uni-easyinput
                trim="all"
                :min="0"
                v-model="stopProfitPrice"
                :placeholder="$t('止盈价')"
                type="number"
              />
              <span class="desc">{{ $t("预期盈利") }}：{{ useFormatMoney(expectedProfitability) }}</span>
            </view>
          </view>
          <view class="item-wrapper">
            <view class="label-value">{{ $t("止损价") }}</view>
            <view class="value-wrapper">
              <!-- <uni-number-box :min="0" :max="999999999999" v-model="stopLoss"  :step="0.000001" ></uni-number-box> -->
              <uni-easyinput
                trim="all"
                :min="0"
                v-model="stopLossPrice"
                :placeholder="$t('止损价')"
                type="number"
              />
              <span class="desc">{{ $t("预期亏损") }}：{{useFormatMoney(expectedLoss)}}</span>
            </view>
          </view>
        </view>
      </template>
    </uni-popup-dialog>
  </uni-popup>
</template>

<script setup>
import BigNumber from "bignumber.js";
import { ref,computed } from "vue";
import { useFormatMoney,useFormatTime} from "@/common/utils";
import tui from "@/common/httpRequest.js";
const refPopup = ref(null);
const emit = defineEmits(["confirm"]);

// const props =  defineProps({
//   stopProfitPrice:{
//     type:[Number,String],
//     default:undefined
//   },
//   stopLossPrice:{
//     type:[Number,String],
//     default:undefined
//   },
//   volume:{
//     type:[Number,String],
//     default:0
//   },
//   margin:{
//     type:[Number,String],
//     default:0
//   },
// });

const stopProfitPrice = ref(0)
const stopLossPrice =  ref(0)
const volume =  ref(0)
const margin =  ref(0)
const shortLong =  ref(0)
const openPrice =  ref(0)


// // 多仓公式： (结束价格 - 开仓价格) * 持仓量
// 空仓公式： (结束价格 - 开仓价格) * 持仓量 * -1
// 预期盈利
const expectedProfitability = computed(()=>{
  // return new BigNumber(volume.value || 0).times(stopProfitPrice.value || 0).minus(margin.value).toFixed(2)
    const stopProfitPriceBN = new BigNumber(stopProfitPrice.value || 0); // 空单的结束价格 (止盈)
    const openPriceBN = new BigNumber(openPrice.value || 0); // 开仓价格
    const volumeBN = new BigNumber(volume.value || 0); // 持仓量

    let positionProfit;
    if (shortLong.value === 1) {
        // 多仓公式： (结束价格 - 开仓价格) * 持仓量
        positionProfit = stopProfitPriceBN.minus(openPriceBN).times(volumeBN);
    } else if (shortLong.value === 0) {
        // 空仓公式： (结束价格 - 开仓价格) * 持仓量 * -1
        positionProfit = stopProfitPriceBN.minus(openPriceBN).times(volumeBN).times(-1);
    }
    if(stopProfitPriceBN==0){
      return ''
    }
    return positionProfit.toFixed(2); // 保留两位小数
})

// 预期亏损
const expectedLoss = computed(()=>{
  // return new BigNumber(volume.value || 0).times(stopLossPrice.value || 0).minus(margin.value).toFixed(2)

    const stopLossPriceBN = new BigNumber(stopLossPrice.value || 0); // 多仓的结束价格 (止损)
    const openPriceBN = new BigNumber(openPrice.value || 0); // 开仓价格
    const volumeBN = new BigNumber(volume.value || 0); // 持仓量
    console.log(stopLossPrice.value,openPrice.value,volume.value)
    let positionProfit;
    if (shortLong.value === 1) {
        // 多仓公式： (结束价格 - 开仓价格) * 持仓量
        positionProfit = stopLossPriceBN.minus(openPriceBN).times(volumeBN);
    } else if (shortLong.value === 0) {
        // 空仓公式： (结束价格 - 开仓价格) * 持仓量 * -1
        positionProfit = stopLossPriceBN.minus(openPriceBN).times(volumeBN).times(-1);
    }
    if(stopLossPriceBN==0){
      return ''
    }
    return positionProfit.toFixed(2); // 保留两位小数
})


const propsItem = ref({})

const open = (item) => {
  stopProfitPrice.value = item.stopProfitPrice || item.openPrice;
  stopLossPrice.value = item.stopLossPrice || item.openPrice;
  volume.value = item.volume;
  margin.value = item.margin;
  shortLong.value = item.shortLong;
  openPrice.value = item.openPrice;
  propsItem.value = item;
  refPopup.value.open();
};

const close = () => {
  refPopup.value.close();
};

const popupChange = ({ show }) => {
  //打开
};

const confirm = () => {
  const params = {
    "id": propsItem.value.id,
  "stopLossPrice": stopLossPrice.value,
  "stopProfitPrice": stopProfitPrice.value
  };

  tui.request("/app-api/trade/contract/close-condition", "POST", params).then((res) => {
      emit("confirm")
    })
  close();
};

defineExpose({
  open,
  close,
});
</script>

<style scoped lang="scss">
::v-deep .popup__wrapper {
  width: 80% !important;
}
.popup-content-wrapper {
  display: flex;
  flex-direction: column;
  width: 550rpx;
  .item-wrapper {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 20rpx;
    ::v-deep .uni-numbox__value {
      width: 100% !important;
    }
    .label-value {
      color: #000;
      font-family: "PingFang SC";
      font-size: 28rpx;
      font-weight: bold;
    }
    .value-wrapper {
      display: flex;
      flex-direction: column;
      flex: 1;
      .desc {
        margin-top: 5rpx;
        color: #979797;
        font-family: "PingFang SC";
        font-size: 24rpx;
      }
    }
  }
  .gap {
    margin-bottom: 45rpx;
  }
}
</style>

<style>
</style>