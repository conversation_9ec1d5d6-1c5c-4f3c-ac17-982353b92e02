<template>
  <view class="empty-container">
    <image :src="image"></image>
    <span>{{$t(placeholder)}}</span>
  </view>
</template>

<script setup>
defineProps({
    placeholder: {
      type: String,
      default: "zp.emptyView.title",
    },
    image:{
      type: String,
      default: '/static/images/common/empty.png',
    }
  }
)

</script>

<style scoped lang="scss">
  .empty-container{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    image{
      width: 128rpx;
      height: 128rpx;
    }
    span{
      font-size: 24rpx;
      color: #5F5D6A;
      margin-top: 30rpx;
    }
  }


</style>