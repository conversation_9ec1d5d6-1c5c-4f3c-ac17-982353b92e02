
<template>
  <span class="volatility">
    <slot :currentDataPrice="currentPrice">{{ currentPrice || '--' }}</slot>
  </span>
</template>

<script setup>
import { EventBus } from "@/common/eventBus";
import { onLoad,onUnload } from "@dcloudio/uni-app";
import { ref } from "vue";
const props = defineProps({
  code: String,
});

const currentPrice = ref(0);

onLoad(() => {
  EventBus.on(props.code, (data) => {
    if (data.percentage) {
      currentPrice.value = data.price;
    }
  });
});
onUnload(() => {
  EventBus.off(props.code);
});
</script>