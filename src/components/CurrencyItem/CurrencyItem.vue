<template>
  <view class="currency-item-class" @click="navTransaction">
    <view class="left-class">
      <view>
        <image class="icon-class" :src="itemData.icon"></image>
      </view>
      <view>
        <view class="label-class">{{ itemData.name }}</view>
        <view class="value-class"
          >24H:{{ useFormatMoney(item.volume) }}</view
        ></view
      >
    </view>
    <view class="center-class">
      <view class="label-class">{{
        useFormatMoney(itemData.currentPrice)
      }}</view>
      <view class="value-class"
        >{{ config.currencySymbol }}{{ useFormatMoney(rateCurrentPrice) }}</view
      >
    </view>
    <view class="right-class" :style="getStyleForValue(itemData.percentage)"
      >{{ itemData.percentage < 0 ? "" : "+" }}{{ itemData.percentage }}%</view
    >
  </view>
</template>
<script setup>
import { useFormatMoney } from "@/common/utils";
import config from "@/common/config";
import { computed } from "vue";
import { onLoad, onUnload } from "@dcloudio/uni-app";
import { EventBus } from "@/common/eventBus";
import BigNumber from "bignumber.js";

const { exchangeRates } = config;
const props = defineProps({
  item: {
    type: Object,
    default: () => {},
  },
});
//自定义出来
const itemData = computed(() => props.item);

// const rate = computed(()=>{
//     return exchangeRates.find(item=>item.quoteCurrency===config.currencyUnit)
// })

const rateCurrentPrice = computed(() => {
  return new BigNumber(itemData.value.currentPrice || 0).multipliedBy(
    config.currentRate.realRate
  );
});

function navTransaction() {
  const hash = window.location.hash;
  if (hash.indexOf("/pages/tabBar/transaction") != -1) {
    uni.redirectTo({
      url: `/pages/tabBar/transaction?code=${props.item.code}&name=${props.item.name}&router=redirectTo`,
    });
  } else {
    uni.navigateTo({
      url: `/pages/tabBar/transaction?code=${props.item.code}&name=${props.item.name}`,
    });
  }
}

onLoad(() => {
  // uni.$on(props.item.code, (data) => {
  EventBus.on(props.item.code, (data) => {
    if (data.percentage) {
      itemData.value.percentage = data.percentage;
    }
    if (data.price) {
      itemData.value.currentPrice = data.price;
    }
    if (data.volume24H) {
      itemData.value.volume = data.volume24H;
    }
  });
});
onUnload(() => {
  EventBus.off(props.item.code);
});
function getStyleForValue(value) {
  let background;
  if (value > 0) {
    background = "var(--common-positive-color)";
  } else if (value < 0) {
    background = "var(--common-negative-color)";
  } else {
    background = "var(--zero-color)";
  }
  return { background };
}
</script>
<style scoped lang="scss">
.currency-item-class {
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;

  .left-class {
    text-align: left;
    width: 33%;
    display: flex;
    align-items: center;
    gap: 20rpx;
    .icon-class{
      width: 48rpx;
      height: 48rpx;
      border-radius: 50%;
    }
    .label-class {
      color: #111;
      text-align: left;
      font-family: DIN;
      font-size: 28rpx;
      font-style: normal;
      font-weight: 700;
      line-height: 140%; /* 39.2rpx */
      letter-spacing: 0.4rpx;
    }
    .value-class {
      color: #979797;
      text-align: left;
      font-family: "PingFang SC";
      font-size: 22rpx;
      font-style: normal;
      font-weight: 400;
      line-height: 140%; /* 30.8rpx */
      letter-spacing: 0.4rpx;
    }
  }

  .center-class {
    text-align: right;
    width: 23%;
    .label-class {
      color: #111;
      text-align: right;
      font-family: DIN;
      font-size: 28rpx;
      font-style: normal;
      font-weight: bolder;
      line-height: 140%; /* 39.2rpx */
      letter-spacing: 0.4rpx;
    }
    .value-class {
      color: #94959a;
      text-align: right;
      font-family: DIN;
      font-size: 22rpx;
      font-style: normal;
      font-weight: 500;
      line-height: 140%; /* 30.8rpx */
      letter-spacing: 0.4rpx;
    }
  }

  .right-class {
    color: #fff;
    text-align: right;
    font-family: "PingFang SC";
    font-size: 26rpx;
    font-style: normal;
    font-weight: bold;
    line-height: 140%; /* 36.4rpx */
    letter-spacing: 0.4rpx;

    display: flex;
    width: 144rpx;
    padding: 12rpx 0;
    justify-content: center;
    align-items: center;
    gap: 20rpx;
    flex-shrink: 0;
    border-radius: 14rpx;
  }
}
</style>