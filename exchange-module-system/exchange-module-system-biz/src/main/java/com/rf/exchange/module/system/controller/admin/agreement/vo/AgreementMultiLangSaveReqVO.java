package com.rf.exchange.module.system.controller.admin.agreement.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static com.rf.exchange.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 管理后台 - 多语言协议管理新增/修改 Request VO
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
@Schema(description = "管理后台 - 多语言协议管理新增/修改 Request VO")
@Data
public class AgreementMultiLangSaveReqVO {

    @Schema(description = "主键ID", example = "1")
    private Long id;

    @Schema(description = "租户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "租户ID不能为空")
    private Long tenantId;

    @Schema(description = "协议类型：1-隐私协议 2-用户准则 3-服务条款 4-免责声明 5-关于我们", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "协议类型不能为空")
    private Integer type;

    @Schema(description = "协议版本号", example = "1.0")
    private String version;

    @Schema(description = "状态：0-禁用 1-启用", example = "1")
    private Integer status;

    @Schema(description = "生效时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime effectiveTime;

    @Schema(description = "备注", example = "这是一个备注")
    private String remark;

    @Schema(description = "多语言内容列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "多语言内容不能为空")
    @Valid
    private List<AgreementContentVO> contents;
}
