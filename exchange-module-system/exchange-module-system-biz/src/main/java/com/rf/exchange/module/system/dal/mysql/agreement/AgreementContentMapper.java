package com.rf.exchange.module.system.dal.mysql.agreement;

import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.mybatis.core.mapper.BaseMapperX;
import com.rf.exchange.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.rf.exchange.module.system.dal.dataobject.agreement.AgreementContentDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 协议多语言内容 Mapper
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
@Mapper
public interface AgreementContentMapper extends BaseMapperX<AgreementContentDO> {

    /**
     * 根据协议ID获取所有语言版本
     */
    default List<AgreementContentDO> selectByAgreementId(Long agreementId) {
        return selectList(new LambdaQueryWrapperX<AgreementContentDO>()
                .eq(AgreementContentDO::getAgreementId, agreementId)
                .orderByAsc(AgreementContentDO::getLanguageCode));
    }

    /**
     * 根据协议ID和语言代码获取内容
     */
    default AgreementContentDO selectByAgreementIdAndLanguage(Long agreementId, String languageCode) {
        return selectOne(new LambdaQueryWrapperX<AgreementContentDO>()
                .eq(AgreementContentDO::getAgreementId, agreementId)
                .eq(AgreementContentDO::getLanguageCode, languageCode));
    }

    /**
     * 根据协议ID和语言代码删除内容
     */
    default int deleteByAgreementIdAndLanguage(Long agreementId, String languageCode) {
        return delete(new LambdaQueryWrapperX<AgreementContentDO>()
                .eq(AgreementContentDO::getAgreementId, agreementId)
                .eq(AgreementContentDO::getLanguageCode, languageCode));
    }

    /**
     * 根据协议ID删除所有语言版本
     */
    default int deleteByAgreementId(Long agreementId) {
        return delete(new LambdaQueryWrapperX<AgreementContentDO>()
                .eq(AgreementContentDO::getAgreementId, agreementId));
    }

    /**
     * 获取协议的可用语言列表
     */
    default List<String> selectLanguagesByAgreementId(Long agreementId) {
        return selectObjs(new LambdaQueryWrapperX<AgreementContentDO>()
                .select(AgreementContentDO::getLanguageCode)
                .eq(AgreementContentDO::getAgreementId, agreementId)
                .orderByAsc(AgreementContentDO::getLanguageCode));
    }
}
