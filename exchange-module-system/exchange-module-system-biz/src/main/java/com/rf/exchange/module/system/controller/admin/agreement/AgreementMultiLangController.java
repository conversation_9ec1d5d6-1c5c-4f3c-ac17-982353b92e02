package com.rf.exchange.module.system.controller.admin.agreement;

import com.rf.exchange.framework.common.pojo.CommonResult;
import com.rf.exchange.framework.common.pojo.PageResult;
import com.rf.exchange.framework.common.util.object.BeanUtils;
import com.rf.exchange.framework.excel.core.util.ExcelUtils;
import com.rf.exchange.framework.operatelog.core.annotations.OperateLog;
import com.rf.exchange.module.system.controller.admin.agreement.vo.*;
import com.rf.exchange.module.system.dal.dataobject.agreement.AgreementDO;
import com.rf.exchange.module.system.dal.dataobject.agreement.AgreementContentDO;
import com.rf.exchange.module.system.service.agreement.AgreementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.rf.exchange.framework.common.pojo.CommonResult.success;
import static com.rf.exchange.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;

/**
 * 管理后台 - 多语言协议管理
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
@Tag(name = "管理后台 - 多语言协议管理")
@RestController
@RequestMapping("/system/agreement-multilang")
@Validated
public class AgreementMultiLangController {

    @Resource
    private AgreementService agreementService;

    @PostMapping("/create")
    @Operation(summary = "创建多语言协议")
    @PreAuthorize("@ss.hasPermission('system:agreement:create')")
    public CommonResult<Long> createMultiLangAgreement(@Valid @RequestBody AgreementMultiLangSaveReqVO createReqVO) {
        return success(agreementService.createMultiLangAgreement(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新多语言协议")
    @PreAuthorize("@ss.hasPermission('system:agreement:update')")
    public CommonResult<Boolean> updateMultiLangAgreement(@Valid @RequestBody AgreementMultiLangSaveReqVO updateReqVO) {
        agreementService.updateMultiLangAgreement(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除多语言协议")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:agreement:delete')")
    public CommonResult<Boolean> deleteMultiLangAgreement(@RequestParam("id") Long id) {
        agreementService.deleteAgreement(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得多语言协议")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:agreement:query')")
    public CommonResult<AgreementRespVO> getMultiLangAgreement(@RequestParam("id") Long id) {
        AgreementDO agreement = agreementService.getAgreement(id);
        List<AgreementContentDO> contents = agreementService.getAgreementContents(id);
        
        AgreementRespVO respVO = BeanUtils.toBean(agreement, AgreementRespVO.class);
        respVO.setContents(BeanUtils.toBean(contents, AgreementContentVO.class));
        return success(respVO);
    }

    @GetMapping("/page")
    @Operation(summary = "获得多语言协议分页")
    @PreAuthorize("@ss.hasPermission('system:agreement:query')")
    public CommonResult<PageResult<AgreementRespVO>> getMultiLangAgreementPage(@Valid AgreementPageReqVO pageReqVO) {
        PageResult<AgreementDO> pageResult = agreementService.getAgreementPage(pageReqVO);
        
        // 转换为响应VO并加载多语言内容
        List<AgreementRespVO> respVOList = BeanUtils.toBean(pageResult.getList(), AgreementRespVO.class);
        for (AgreementRespVO respVO : respVOList) {
            List<AgreementContentDO> contents = agreementService.getAgreementContents(respVO.getId());
            respVO.setContents(BeanUtils.toBean(contents, AgreementContentVO.class));
        }
        
        return success(new PageResult<>(respVOList, pageResult.getTotal()));
    }

    @GetMapping("/contents")
    @Operation(summary = "获取协议的多语言内容")
    @Parameter(name = "agreementId", description = "协议ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('system:agreement:query')")
    public CommonResult<List<AgreementContentVO>> getAgreementContents(@RequestParam("agreementId") Long agreementId) {
        List<AgreementContentDO> contents = agreementService.getAgreementContents(agreementId);
        return success(BeanUtils.toBean(contents, AgreementContentVO.class));
    }

    @GetMapping("/languages")
    @Operation(summary = "获取协议支持的语言列表")
    @Parameter(name = "agreementId", description = "协议ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('system:agreement:query')")
    public CommonResult<List<String>> getAgreementLanguages(@RequestParam("agreementId") Long agreementId) {
        List<String> languages = agreementService.getAgreementLanguages(agreementId);
        return success(languages);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出多语言协议 Excel")
    @PreAuthorize("@ss.hasPermission('system:agreement:export')")
    @OperateLog(type = EXPORT)
    public void exportMultiLangAgreementExcel(@Valid AgreementPageReqVO pageReqVO,
                                              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageResult.PAGE_SIZE_NONE);
        List<AgreementDO> list = agreementService.getAgreementPage(pageReqVO).getList();
        
        // 转换为导出VO
        List<AgreementRespVO> datas = BeanUtils.toBean(list, AgreementRespVO.class);
        for (AgreementRespVO data : datas) {
            List<AgreementContentDO> contents = agreementService.getAgreementContents(data.getId());
            data.setContents(BeanUtils.toBean(contents, AgreementContentVO.class));
        }
        
        // 导出 Excel
        ExcelUtils.write(response, "多语言协议.xls", "数据", AgreementRespVO.class, datas);
    }
}
