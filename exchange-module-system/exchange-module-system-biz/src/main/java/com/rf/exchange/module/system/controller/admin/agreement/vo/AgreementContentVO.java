package com.rf.exchange.module.system.controller.admin.agreement.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 协议多语言内容 VO
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
@Schema(description = "协议多语言内容 VO")
@Data
public class AgreementContentVO {

    @Schema(description = "内容ID", example = "1")
    private Long id;

    @Schema(description = "协议ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "协议ID不能为空")
    private Long agreementId;

    @Schema(description = "语言代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "zh-CN")
    @NotBlank(message = "语言代码不能为空")
    private String languageCode;

    @Schema(description = "语言名称", example = "简体中文")
    private String languageName;

    @Schema(description = "协议标题", requiredMode = Schema.RequiredMode.REQUIRED, example = "隐私协议")
    @NotBlank(message = "协议标题不能为空")
    private String title;

    @Schema(description = "协议内容（HTML格式）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "协议内容不能为空")
    private String content;
}
