package com.rf.exchange.module.system.dal.dataobject.agreement;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rf.exchange.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 协议多语言内容 DO
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
@TableName("system_agreement_content")
@KeySequence("system_agreement_content_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgreementContentDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    
    /**
     * 协议ID，关联system_agreement表
     */
    private Long agreementId;
    
    /**
     * 语言代码，如zh-CN, en, ja, ko等
     */
    private String languageCode;
    
    /**
     * 协议标题
     */
    private String title;
    
    /**
     * 协议内容（HTML格式）
     */
    private String content;
}
