package com.rf.exchange.module.system.enums.agreement;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 协议类型枚举
 *
 * <AUTHOR>
 * @since 2025-01-26
 */
@Getter
@AllArgsConstructor
public enum AgreementTypeEnum {

    PRIVACY_POLICY(1, "AGREEMENT_TYPE_PRIVACY_POLICY"),
    USER_GUIDELINES(2, "AGREEMENT_TYPE_USER_GUIDELINES"),
    TERMS_OF_SERVICE(3, "AGREEMENT_TYPE_TERMS_OF_SERVICE"),
    DISCLAIMER(4, "AGREEMENT_TYPE_DISCLAIMER"),
    ABOUT_US(5, "AGREEMENT_TYPE_ABOUT_US");

    /**
     * 类型值
     */
    private final Integer type;
    
    /**
     * 类型名称
     */
    private final String name;

    /**
     * 根据类型值获取枚举
     */
    public static AgreementTypeEnum getByType(Integer type) {
        for (AgreementTypeEnum typeEnum : values()) {
            if (typeEnum.getType().equals(type)) {
                return typeEnum;
            }
        }
        return null;
    }
}
