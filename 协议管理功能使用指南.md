# 协议管理功能使用指南

## 功能概述

协议管理功能为多租户交易系统提供了完整的协议内容管理解决方案，支持隐私协议、用户准则、服务条款、免责声明等多种协议类型的管理。

## 功能特性

- ✅ **多租户支持**：每个租户可独立配置自己的协议内容
- ✅ **多协议类型**：支持隐私协议、用户准则、服务条款、免责声明
- ✅ **富文本内容**：支持HTML格式的协议内容
- ✅ **版本管理**：支持协议版本号管理
- ✅ **状态控制**：可启用/禁用协议
- ✅ **生效时间**：可设置协议生效时间
- ✅ **完整CRUD**：支持增删改查操作
- ✅ **数据导出**：支持Excel导出功能
- ✅ **权限控制**：基于RBAC的权限管理

## 部署步骤

### 1. 执行数据库脚本

```bash
# 执行SQL脚本创建表结构和初始数据
mysql -u username -p database_name < sql/mysql/20250126.sql
```

### 2. 重启应用

重启应用服务以加载新的代码和配置。

### 3. 配置菜单权限

在管理后台的菜单管理中，为相应角色分配协议管理相关权限：

- `system:agreement:query` - 协议查询权限
- `system:agreement:create` - 协议创建权限
- `system:agreement:update` - 协议更新权限
- `system:agreement:delete` - 协议删除权限
- `system:agreement:export` - 协议导出权限

## 使用说明

### 管理后台使用

#### 1. 创建协议

1. 登录管理后台
2. 进入"系统管理" -> "协议管理"
3. 点击"新增"按钮
4. 填写协议信息：
   - 选择租户
   - 选择协议类型
   - 输入协议标题
   - 编辑协议内容（支持HTML）
   - 设置版本号
   - 选择状态
   - 设置生效时间（可选）
   - 添加备注（可选）
5. 点击"确定"保存

#### 2. 编辑协议

1. 在协议列表中找到要编辑的协议
2. 点击"编辑"按钮
3. 修改相应字段
4. 点击"确定"保存

#### 3. 删除协议

1. 在协议列表中找到要删除的协议
2. 点击"删除"按钮
3. 确认删除操作

#### 4. 搜索协议

支持按以下条件搜索：
- 租户ID
- 协议类型
- 协议标题（模糊搜索）
- 状态
- 创建时间范围

#### 5. 导出协议

1. 设置搜索条件（可选）
2. 点击"导出"按钮
3. 下载Excel文件

### APP端使用

APP端提供了多个接口来获取不同类型的协议：

#### 1. 获取隐私协议
```javascript
GET /app-api/system/agreement/privacy-policy
```

#### 2. 获取用户准则
```javascript
GET /app-api/system/agreement/user-guidelines
```

#### 3. 获取服务条款
```javascript
GET /app-api/system/agreement/terms-of-service
```

#### 4. 获取免责声明
```javascript
GET /app-api/system/agreement/disclaimer
```

#### 5. 根据类型获取协议
```javascript
GET /app-api/system/agreement/get-by-type?type=1
```

## 协议类型说明

| 类型值 | 协议名称 | 用途说明 |
|--------|----------|----------|
| 1 | 隐私协议 | 说明如何收集、使用、保护用户个人信息 |
| 2 | 用户准则 | 规定用户在使用平台时应遵守的行为准则 |
| 3 | 服务条款 | 定义平台与用户之间的服务关系和责任 |
| 4 | 免责声明 | 声明平台在特定情况下的免责条款 |

## 最佳实践

### 1. 协议内容编写

- **使用HTML格式**：便于在前端展示丰富的格式
- **结构清晰**：使用标题、段落、列表等元素组织内容
- **语言简洁**：避免过于复杂的法律术语
- **定期更新**：根据法律法规变化及时更新协议内容

### 2. 版本管理

- **语义化版本**：建议使用如"1.0"、"1.1"、"2.0"的版本号
- **重大变更**：重要修改时更新主版本号
- **小幅修改**：文字调整时更新次版本号

### 3. 多租户配置

- **独立配置**：每个租户应配置自己的协议内容
- **本地化**：根据租户所在地区调整协议内容
- **合规性**：确保协议内容符合当地法律法规

### 4. 生效时间设置

- **提前通知**：重要协议变更应提前设置生效时间
- **用户告知**：协议更新时应通知用户
- **过渡期**：给用户适当的适应时间

## 注意事项

1. **唯一性约束**：同一租户下，每种协议类型只能有一个启用状态的协议
2. **权限控制**：确保只有授权用户才能修改协议内容
3. **备份重要**：协议内容修改前建议备份原内容
4. **法律合规**：协议内容应符合相关法律法规要求
5. **用户体验**：协议内容应易于理解，避免过于冗长

## 故障排除

### 1. 创建协议失败

**可能原因：**
- 该租户下已存在相同类型的协议
- 协议标题重复
- 必填字段未填写

**解决方案：**
- 检查是否已存在相同类型的协议
- 修改协议标题
- 确保所有必填字段都已填写

### 2. APP端获取不到协议

**可能原因：**
- 协议状态为禁用
- 租户ID不正确
- 协议类型不存在

**解决方案：**
- 检查协议状态是否为启用
- 确认租户ID正确
- 检查协议类型是否存在

### 3. 权限不足

**可能原因：**
- 用户角色未分配相应权限
- 菜单权限配置错误

**解决方案：**
- 为用户角色分配协议管理权限
- 检查菜单权限配置

## 技术支持

如遇到技术问题，请联系开发团队或查看相关技术文档。
