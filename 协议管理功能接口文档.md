# 协议管理功能接口文档

## 功能概述

协议管理功能支持多租户环境下的隐私协议、用户准则、服务条款、免责声明等协议内容的管理。包括后台管理接口和APP端获取接口。

## 数据库设计

### 表结构：system_agreement

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint | - | NO | AUTO_INCREMENT | 主键ID |
| tenant_id | bigint | - | NO | 0 | 租户ID |
| type | tinyint | - | NO | - | 协议类型：1-隐私协议 2-用户准则 3-服务条款 4-免责声明 |
| title | varchar | 200 | NO | - | 协议标题 |
| content | longtext | - | NO | - | 协议内容（HTML格式） |
| version | varchar | 50 | NO | '1.0' | 协议版本号 |
| status | tinyint | - | NO | 1 | 状态：0-禁用 1-启用 |
| effective_time | datetime | - | YES | NULL | 生效时间 |
| remark | varchar | 500 | YES | NULL | 备注 |
| creator | varchar | 64 | YES | '' | 创建者 |
| create_time | datetime | - | NO | CURRENT_TIMESTAMP | 创建时间 |
| updater | varchar | 64 | YES | '' | 更新者 |
| update_time | datetime | - | NO | CURRENT_TIMESTAMP | 更新时间 |
| deleted | bit | 1 | NO | 0 | 是否删除 |

### 索引设计

- PRIMARY KEY: `id`
- KEY `idx_tenant_type`: (`tenant_id`, `type`)
- KEY `idx_status`: (`status`)
- KEY `idx_create_time`: (`create_time`)

## 管理后台接口

### 1. 创建协议

**接口地址：** `POST /admin-api/system/agreement/create`

**权限要求：** `system:agreement:create`

**请求参数：**
```json
{
  "tenantId": 1,
  "type": 1,
  "title": "隐私协议",
  "content": "<h1>隐私协议</h1><p>协议内容...</p>",
  "version": "1.0",
  "status": 1,
  "effectiveTime": "2025-01-26 10:00:00",
  "remark": "备注信息"
}
```

**响应示例：**
```json
{
  "code": 0,
  "data": 1,
  "msg": "操作成功"
}
```

### 2. 更新协议

**接口地址：** `PUT /admin-api/system/agreement/update`

**权限要求：** `system:agreement:update`

**请求参数：**
```json
{
  "id": 1,
  "tenantId": 1,
  "type": 1,
  "title": "隐私协议（更新版）",
  "content": "<h1>隐私协议</h1><p>更新后的协议内容...</p>",
  "version": "1.1",
  "status": 1,
  "effectiveTime": "2025-01-26 10:00:00",
  "remark": "更新备注"
}
```

### 3. 删除协议

**接口地址：** `DELETE /admin-api/system/agreement/delete?id=1`

**权限要求：** `system:agreement:delete`

### 4. 获取协议详情

**接口地址：** `GET /admin-api/system/agreement/get?id=1`

**权限要求：** `system:agreement:query`

**响应示例：**
```json
{
  "code": 0,
  "data": {
    "id": 1,
    "tenantId": 1,
    "type": 1,
    "title": "隐私协议",
    "content": "<h1>隐私协议</h1><p>协议内容...</p>",
    "version": "1.0",
    "status": 1,
    "effectiveTime": "2025-01-26T10:00:00",
    "remark": "备注信息",
    "createTime": "2025-01-26T09:00:00",
    "updateTime": "2025-01-26T09:00:00"
  },
  "msg": "操作成功"
}
```

### 5. 协议分页查询

**接口地址：** `GET /admin-api/system/agreement/page`

**权限要求：** `system:agreement:query`

**请求参数：**
- `pageNo`: 页码（默认1）
- `pageSize`: 每页大小（默认10）
- `tenantId`: 租户ID（可选）
- `type`: 协议类型（可选）
- `title`: 协议标题（模糊查询，可选）
- `status`: 状态（可选）
- `createTime`: 创建时间范围（可选）

**响应示例：**
```json
{
  "code": 0,
  "data": {
    "total": 10,
    "list": [
      {
        "id": 1,
        "tenantId": 1,
        "type": 1,
        "title": "隐私协议",
        "content": "<h1>隐私协议</h1><p>协议内容...</p>",
        "version": "1.0",
        "status": 1,
        "effectiveTime": "2025-01-26T10:00:00",
        "remark": "备注信息",
        "createTime": "2025-01-26T09:00:00",
        "updateTime": "2025-01-26T09:00:00"
      }
    ]
  },
  "msg": "操作成功"
}
```

### 6. 导出协议Excel

**接口地址：** `GET /admin-api/system/agreement/export-excel`

**权限要求：** `system:agreement:export`

## APP端接口

### 1. 获取隐私协议

**接口地址：** `GET /app-api/system/agreement/privacy-policy`

**权限要求：** 无

**响应示例：**
```json
{
  "code": 0,
  "data": {
    "id": 1,
    "type": 1,
    "title": "隐私协议",
    "content": "<h1>隐私协议</h1><p>协议内容...</p>",
    "version": "1.0",
    "effectiveTime": "2025-01-26T10:00:00",
    "updateTime": "2025-01-26T09:00:00"
  },
  "msg": "操作成功"
}
```

### 2. 获取用户准则

**接口地址：** `GET /app-api/system/agreement/user-guidelines`

**权限要求：** 无

### 3. 获取服务条款

**接口地址：** `GET /app-api/system/agreement/terms-of-service`

**权限要求：** 无

### 4. 获取免责声明

**接口地址：** `GET /app-api/system/agreement/disclaimer`

**权限要求：** 无

### 5. 根据类型获取协议

**接口地址：** `GET /app-api/system/agreement/get-by-type?type=1`

**权限要求：** 无

**请求参数：**
- `type`: 协议类型（必填）
  - 1: 隐私协议
  - 2: 用户准则  
  - 3: 服务条款
  - 4: 免责声明

## 协议类型枚举

| 值 | 名称 | 说明 |
|----|------|------|
| 1 | 隐私协议 | Privacy Policy |
| 2 | 用户准则 | User Guidelines |
| 3 | 服务条款 | Terms of Service |
| 4 | 免责声明 | Disclaimer |

## 状态枚举

| 值 | 名称 | 说明 |
|----|------|------|
| 0 | 禁用 | 协议不可用 |
| 1 | 启用 | 协议可用 |

## 错误码

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 1_002_038_000 | 协议不存在 | AGREEMENT_NOT_EXISTS |
| 1_002_038_001 | 该租户下已存在相同类型的协议 | AGREEMENT_TYPE_DUPLICATE |
| 1_002_038_002 | 协议标题已存在 | AGREEMENT_TITLE_DUPLICATE |

## 使用说明

1. **多租户支持**：每个租户可以配置自己的协议内容
2. **协议类型唯一性**：同一租户下，每种协议类型只能有一个启用状态的协议
3. **HTML内容支持**：协议内容支持HTML格式，便于富文本展示
4. **版本管理**：支持协议版本号管理
5. **生效时间**：可设置协议的生效时间
6. **状态控制**：可通过状态字段控制协议的启用/禁用

## 部署说明

1. 执行SQL脚本：`sql/mysql/20250126.sql`
2. 重启应用服务
3. 在管理后台配置相应的菜单权限
4. 为不同租户配置对应的协议内容
