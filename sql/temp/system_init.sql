
-- 设置字符集和时区
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;



DROP TABLE IF EXISTS `candle_control_plan`;

CREATE TABLE `candle_control_plan` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '编号',
  `trade_pair_id` bigint unsigned NOT NULL COMMENT '交易对id',
  `trade_pair_code` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对代码',
  `start_time` bigint unsigned NOT NULL COMMENT '控盘开始时间',
  `tenant_id` bigint unsigned NOT NULL COMMENT '租户id',
  `refer_price` decimal(20,8) NOT NULL COMMENT '生成价格节点时的交易对价格',
  `exec_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '执行计划时的价格',
  `end_time` bigint unsigned NOT NULL COMMENT '控盘结束时间',
  `end_price` decimal(20,8) NOT NULL COMMENT '结束价格',
  `min_price` decimal(20,8) NOT NULL COMMENT '最低价',
  `max_price` decimal(20,8) NOT NULL COMMENT '最低价',
  `fluctuation` decimal(20,4) NOT NULL COMMENT '波动率',
  `duration_secs` int unsigned NOT NULL COMMENT '波动时长(秒)',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '控盘计划状态 0:等待中 1:关闭 2:执行中 3:执行完成',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '创建者',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '更新者',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否已经删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_idx_code_plan` (`trade_pair_code`,`start_time`,`tenant_id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='复制币和自发币的控盘计划参数';

/*Table structure for table `candle_control_plan_kline` */

DROP TABLE IF EXISTS `candle_control_plan_kline`;

CREATE TABLE `candle_control_plan_kline` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `plan_id` bigint unsigned NOT NULL COMMENT '控盘计划的id',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `refer_price` decimal(20,8) NOT NULL COMMENT '引用价格',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=404 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='控盘计划的k线';

/*Table structure for table `candle_control_price_point` */

DROP TABLE IF EXISTS `candle_control_price_point`;

CREATE TABLE `candle_control_price_point` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '编号',
  `plan_id` bigint unsigned NOT NULL COMMENT '控盘计划参数id',
  `trade_pair_code` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对代码',
  `refer_price` decimal(20,8) NOT NULL COMMENT '引用价格',
  `price_diff` decimal(20,8) NOT NULL COMMENT '价格差值',
  `volume` decimal(20,8) NOT NULL COMMENT '成交量',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_idx_plan_ts` (`plan_id`,`timestamp`)
) ENGINE=InnoDB AUTO_INCREMENT=21601 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='控盘计划的价格时间节点';

/*Table structure for table `data_candle_last_sync` */

DROP TABLE IF EXISTS `data_candle_last_sync`;

CREATE TABLE `data_candle_last_sync` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '编号',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对代码',
  `last_sync_date` datetime NOT NULL COMMENT '最后同步的日期',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uni_idx_code` (`code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=12778 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;

/*Table structure for table `data_candle_tradepair_aaveusdt` */

DROP TABLE IF EXISTS `data_candle_tradepair_aaveusdt`;

CREATE TABLE `data_candle_tradepair_aaveusdt` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='交易对aaveusdt';

/*Table structure for table `data_candle_tradepair_asdf` */

DROP TABLE IF EXISTS `data_candle_tradepair_asdf`;

CREATE TABLE `data_candle_tradepair_asdf` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对asdf';

/*Table structure for table `data_candle_tradepair_audcad` */

DROP TABLE IF EXISTS `data_candle_tradepair_audcad`;

CREATE TABLE `data_candle_tradepair_audcad` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对audcad';

/*Table structure for table `data_candle_tradepair_audjpy` */

DROP TABLE IF EXISTS `data_candle_tradepair_audjpy`;

CREATE TABLE `data_candle_tradepair_audjpy` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='交易对audjpy';

/*Table structure for table `data_candle_tradepair_avaxusdt` */

DROP TABLE IF EXISTS `data_candle_tradepair_avaxusdt`;

CREATE TABLE `data_candle_tradepair_avaxusdt` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='交易对avaxusdt';

/*Table structure for table `data_candle_tradepair_bchusdt` */

DROP TABLE IF EXISTS `data_candle_tradepair_bchusdt`;

CREATE TABLE `data_candle_tradepair_bchusdt` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='交易对bchusdt';

/*Table structure for table `data_candle_tradepair_bnbusdt` */

DROP TABLE IF EXISTS `data_candle_tradepair_bnbusdt`;

CREATE TABLE `data_candle_tradepair_bnbusdt` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`) USING BTREE,
  KEY `idx_code_time_range_type_timestamp` (`time_range`,`time_type`,`timestamp` DESC) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='交易对bnbusdt';

/*Table structure for table `data_candle_tradepair_bonkusdt` */

DROP TABLE IF EXISTS `data_candle_tradepair_bonkusdt`;

CREATE TABLE `data_candle_tradepair_bonkusdt` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='交易对bonkusdt';

/*Table structure for table `data_candle_tradepair_btcusdt` */

DROP TABLE IF EXISTS `data_candle_tradepair_btcusdt`;

CREATE TABLE `data_candle_tradepair_btcusdt` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='交易对btcusdt';

/*Table structure for table `data_candle_tradepair_btcusdt1` */

DROP TABLE IF EXISTS `data_candle_tradepair_btcusdt1`;

CREATE TABLE `data_candle_tradepair_btcusdt1` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对btcusdt1';

/*Table structure for table `data_candle_tradepair_btcusdt2` */

DROP TABLE IF EXISTS `data_candle_tradepair_btcusdt2`;

CREATE TABLE `data_candle_tradepair_btcusdt2` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对btcusdt2';

/*Table structure for table `data_candle_tradepair_cbtcaa` */

DROP TABLE IF EXISTS `data_candle_tradepair_cbtcaa`;

CREATE TABLE `data_candle_tradepair_cbtcaa` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对cbtcaa';

/*Table structure for table `data_candle_tradepair_ccusdt` */

DROP TABLE IF EXISTS `data_candle_tradepair_ccusdt`;

CREATE TABLE `data_candle_tradepair_ccusdt` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对ccusdt';

/*Table structure for table `data_candle_tradepair_copybtc` */

DROP TABLE IF EXISTS `data_candle_tradepair_copybtc`;

CREATE TABLE `data_candle_tradepair_copybtc` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对copybtc';

/*Table structure for table `data_candle_tradepair_copybtca` */

DROP TABLE IF EXISTS `data_candle_tradepair_copybtca`;

CREATE TABLE `data_candle_tradepair_copybtca` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对copybtca';

/*Table structure for table `data_candle_tradepair_copybtcab` */

DROP TABLE IF EXISTS `data_candle_tradepair_copybtcab`;

CREATE TABLE `data_candle_tradepair_copybtcab` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对copybtcab';

/*Table structure for table `data_candle_tradepair_copybtcac` */

DROP TABLE IF EXISTS `data_candle_tradepair_copybtcac`;

CREATE TABLE `data_candle_tradepair_copybtcac` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对copybtcac';

/*Table structure for table `data_candle_tradepair_copybtcad` */

DROP TABLE IF EXISTS `data_candle_tradepair_copybtcad`;

CREATE TABLE `data_candle_tradepair_copybtcad` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对copybtcad';

/*Table structure for table `data_candle_tradepair_copybtcae` */

DROP TABLE IF EXISTS `data_candle_tradepair_copybtcae`;

CREATE TABLE `data_candle_tradepair_copybtcae` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对copybtcae';

/*Table structure for table `data_candle_tradepair_copybtcak` */

DROP TABLE IF EXISTS `data_candle_tradepair_copybtcak`;

CREATE TABLE `data_candle_tradepair_copybtcak` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对copybtcak';

/*Table structure for table `data_candle_tradepair_copybtcal` */

DROP TABLE IF EXISTS `data_candle_tradepair_copybtcal`;

CREATE TABLE `data_candle_tradepair_copybtcal` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对copybtcal';

/*Table structure for table `data_candle_tradepair_copybtcc` */

DROP TABLE IF EXISTS `data_candle_tradepair_copybtcc`;

CREATE TABLE `data_candle_tradepair_copybtcc` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对copybtcc';

/*Table structure for table `data_candle_tradepair_copybtcda` */

DROP TABLE IF EXISTS `data_candle_tradepair_copybtcda`;

CREATE TABLE `data_candle_tradepair_copybtcda` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对copybtcda';

/*Table structure for table `data_candle_tradepair_copybtcdd` */

DROP TABLE IF EXISTS `data_candle_tradepair_copybtcdd`;

CREATE TABLE `data_candle_tradepair_copybtcdd` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对copybtcdd';

/*Table structure for table `data_candle_tradepair_copybtcff` */

DROP TABLE IF EXISTS `data_candle_tradepair_copybtcff`;

CREATE TABLE `data_candle_tradepair_copybtcff` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对copybtcff';

/*Table structure for table `data_candle_tradepair_copybtcfu` */

DROP TABLE IF EXISTS `data_candle_tradepair_copybtcfu`;

CREATE TABLE `data_candle_tradepair_copybtcfu` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对copybtcfu';

/*Table structure for table `data_candle_tradepair_copybtcgg` */

DROP TABLE IF EXISTS `data_candle_tradepair_copybtcgg`;

CREATE TABLE `data_candle_tradepair_copybtcgg` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对copybtcgg';

/*Table structure for table `data_candle_tradepair_copybtchh` */

DROP TABLE IF EXISTS `data_candle_tradepair_copybtchh`;

CREATE TABLE `data_candle_tradepair_copybtchh` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对copybtchh';

/*Table structure for table `data_candle_tradepair_copybtcla` */

DROP TABLE IF EXISTS `data_candle_tradepair_copybtcla`;

CREATE TABLE `data_candle_tradepair_copybtcla` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对copybtcla';

/*Table structure for table `data_candle_tradepair_copybtcll` */

DROP TABLE IF EXISTS `data_candle_tradepair_copybtcll`;

CREATE TABLE `data_candle_tradepair_copybtcll` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对copybtcll';

/*Table structure for table `data_candle_tradepair_copybtcoo` */

DROP TABLE IF EXISTS `data_candle_tradepair_copybtcoo`;

CREATE TABLE `data_candle_tradepair_copybtcoo` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对copybtcoo';

/*Table structure for table `data_candle_tradepair_copybtcuu` */

DROP TABLE IF EXISTS `data_candle_tradepair_copybtcuu`;

CREATE TABLE `data_candle_tradepair_copybtcuu` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对copybtcuu';

/*Table structure for table `data_candle_tradepair_copybtcxx` */

DROP TABLE IF EXISTS `data_candle_tradepair_copybtcxx`;

CREATE TABLE `data_candle_tradepair_copybtcxx` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对copybtcxx';

/*Table structure for table `data_candle_tradepair_copybtcyy` */

DROP TABLE IF EXISTS `data_candle_tradepair_copybtcyy`;

CREATE TABLE `data_candle_tradepair_copybtcyy` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对copybtcyy';

/*Table structure for table `data_candle_tradepair_copybtczz` */

DROP TABLE IF EXISTS `data_candle_tradepair_copybtczz`;

CREATE TABLE `data_candle_tradepair_copybtczz` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对copybtczz';

/*Table structure for table `data_candle_tradepair_copyeth` */

DROP TABLE IF EXISTS `data_candle_tradepair_copyeth`;

CREATE TABLE `data_candle_tradepair_copyeth` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对copyeth';

/*Table structure for table `data_candle_tradepair_cullen` */

DROP TABLE IF EXISTS `data_candle_tradepair_cullen`;

CREATE TABLE `data_candle_tradepair_cullen` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对cullen';

/*Table structure for table `data_candle_tradepair_cullen1` */

DROP TABLE IF EXISTS `data_candle_tradepair_cullen1`;

CREATE TABLE `data_candle_tradepair_cullen1` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对cullen1';

/*Table structure for table `data_candle_tradepair_culusdt` */

DROP TABLE IF EXISTS `data_candle_tradepair_culusdt`;

CREATE TABLE `data_candle_tradepair_culusdt` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对culusdt';

/*Table structure for table `data_candle_tradepair_dbtcusdt` */

DROP TABLE IF EXISTS `data_candle_tradepair_dbtcusdt`;

CREATE TABLE `data_candle_tradepair_dbtcusdt` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对dbtcusdt';

/*Table structure for table `data_candle_tradepair_dogeusdt` */

DROP TABLE IF EXISTS `data_candle_tradepair_dogeusdt`;

CREATE TABLE `data_candle_tradepair_dogeusdt` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='交易对dogeusdt';

/*Table structure for table `data_candle_tradepair_eth2usdt` */

DROP TABLE IF EXISTS `data_candle_tradepair_eth2usdt`;

CREATE TABLE `data_candle_tradepair_eth2usdt` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对eth2usdt';

/*Table structure for table `data_candle_tradepair_ethusd2` */

DROP TABLE IF EXISTS `data_candle_tradepair_ethusd2`;

CREATE TABLE `data_candle_tradepair_ethusd2` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对ethusd2';

/*Table structure for table `data_candle_tradepair_ethusdt` */

DROP TABLE IF EXISTS `data_candle_tradepair_ethusdt`;

CREATE TABLE `data_candle_tradepair_ethusdt` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='交易对ethusdt';

/*Table structure for table `data_candle_tradepair_eurjpy` */

DROP TABLE IF EXISTS `data_candle_tradepair_eurjpy`;

CREATE TABLE `data_candle_tradepair_eurjpy` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='交易对eurjpy';

/*Table structure for table `data_candle_tradepair_evonusdt` */

DROP TABLE IF EXISTS `data_candle_tradepair_evonusdt`;

CREATE TABLE `data_candle_tradepair_evonusdt` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对evonusdt';

/*Table structure for table `data_candle_tradepair_kiki` */

DROP TABLE IF EXISTS `data_candle_tradepair_kiki`;

CREATE TABLE `data_candle_tradepair_kiki` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对kiki';

/*Table structure for table `data_candle_tradepair_kikiusdt` */

DROP TABLE IF EXISTS `data_candle_tradepair_kikiusdt`;

CREATE TABLE `data_candle_tradepair_kikiusdt` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对kikiusdt';

/*Table structure for table `data_candle_tradepair_kk` */

DROP TABLE IF EXISTS `data_candle_tradepair_kk`;

CREATE TABLE `data_candle_tradepair_kk` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对kk';

/*Table structure for table `data_candle_tradepair_kkgold` */

DROP TABLE IF EXISTS `data_candle_tradepair_kkgold`;

CREATE TABLE `data_candle_tradepair_kkgold` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对kkgold';

/*Table structure for table `data_candle_tradepair_kkusdt` */

DROP TABLE IF EXISTS `data_candle_tradepair_kkusdt`;

CREATE TABLE `data_candle_tradepair_kkusdt` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对kkusdt';

/*Table structure for table `data_candle_tradepair_kkxusdt` */

DROP TABLE IF EXISTS `data_candle_tradepair_kkxusdt`;

CREATE TABLE `data_candle_tradepair_kkxusdt` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对kkxusdt';

/*Table structure for table `data_candle_tradepair_kuleusdt` */

DROP TABLE IF EXISTS `data_candle_tradepair_kuleusdt`;

CREATE TABLE `data_candle_tradepair_kuleusdt` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对kuleusdt';

/*Table structure for table `data_candle_tradepair_pepeusdt` */

DROP TABLE IF EXISTS `data_candle_tradepair_pepeusdt`;

CREATE TABLE `data_candle_tradepair_pepeusdt` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='交易对pepeusdt';

/*Table structure for table `data_candle_tradepair_robusta` */

DROP TABLE IF EXISTS `data_candle_tradepair_robusta`;

CREATE TABLE `data_candle_tradepair_robusta` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对robusta';

/*Table structure for table `data_candle_tradepair_test` */

DROP TABLE IF EXISTS `data_candle_tradepair_test`;

CREATE TABLE `data_candle_tradepair_test` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='交易对test';

/*Table structure for table `data_candle_tradepair_testcoin` */

DROP TABLE IF EXISTS `data_candle_tradepair_testcoin`;

CREATE TABLE `data_candle_tradepair_testcoin` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对testcoin';

/*Table structure for table `data_candle_tradepair_testusdt` */

DROP TABLE IF EXISTS `data_candle_tradepair_testusdt`;

CREATE TABLE `data_candle_tradepair_testusdt` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对testusdt';

/*Table structure for table `data_candle_tradepair_tttusdt` */

DROP TABLE IF EXISTS `data_candle_tradepair_tttusdt`;

CREATE TABLE `data_candle_tradepair_tttusdt` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对tttusdt';

/*Table structure for table `data_candle_tradepair_ttusdt` */

DROP TABLE IF EXISTS `data_candle_tradepair_ttusdt`;

CREATE TABLE `data_candle_tradepair_ttusdt` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对ttusdt';

/*Table structure for table `data_candle_tradepair_ukousd` */

DROP TABLE IF EXISTS `data_candle_tradepair_ukousd`;

CREATE TABLE `data_candle_tradepair_ukousd` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='交易对ukousd';

/*Table structure for table `data_candle_tradepair_usdcny` */

DROP TABLE IF EXISTS `data_candle_tradepair_usdcny`;

CREATE TABLE `data_candle_tradepair_usdcny` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='交易对usdcny';

/*Table structure for table `data_candle_tradepair_usddkk` */

DROP TABLE IF EXISTS `data_candle_tradepair_usddkk`;

CREATE TABLE `data_candle_tradepair_usddkk` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对usddkk';

/*Table structure for table `data_candle_tradepair_usdhkd` */

DROP TABLE IF EXISTS `data_candle_tradepair_usdhkd`;

CREATE TABLE `data_candle_tradepair_usdhkd` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='交易对usdhkd';

/*Table structure for table `data_candle_tradepair_usdjpy` */

DROP TABLE IF EXISTS `data_candle_tradepair_usdjpy`;

CREATE TABLE `data_candle_tradepair_usdjpy` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='交易对usdjpy';

/*Table structure for table `data_candle_tradepair_usdthb` */

DROP TABLE IF EXISTS `data_candle_tradepair_usdthb`;

CREATE TABLE `data_candle_tradepair_usdthb` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='交易对usdthb';

/*Table structure for table `data_candle_tradepair_wsmusdt` */

DROP TABLE IF EXISTS `data_candle_tradepair_wsmusdt`;

CREATE TABLE `data_candle_tradepair_wsmusdt` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易对wsmusdt';

/*Table structure for table `data_candle_tradepair_xagusd` */

DROP TABLE IF EXISTS `data_candle_tradepair_xagusd`;

CREATE TABLE `data_candle_tradepair_xagusd` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='交易对xagusd';

/*Table structure for table `data_candle_tradepair_xauusd` */

DROP TABLE IF EXISTS `data_candle_tradepair_xauusd`;

CREATE TABLE `data_candle_tradepair_xauusd` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='交易对xauusd';

/*Table structure for table `data_candle_tradepair_xcuusd` */

DROP TABLE IF EXISTS `data_candle_tradepair_xcuusd`;

CREATE TABLE `data_candle_tradepair_xcuusd` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='交易对xcuusd';

/*Table structure for table `data_candle_tradepair_xniusd` */

DROP TABLE IF EXISTS `data_candle_tradepair_xniusd`;

CREATE TABLE `data_candle_tradepair_xniusd` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`time_range`,`time_type`,`timestamp`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='交易对xniusd';

/*Table structure for table `data_control_candle` */

DROP TABLE IF EXISTS `data_control_candle`;

CREATE TABLE `data_control_candle` (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对编码',
  `time_range` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围',
  `time_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '时间范围类型 1分钟 2小时 3日 4月',
  `timestamp` bigint unsigned NOT NULL COMMENT '时间戳',
  `high_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最高价格',
  `low_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '最低价格',
  `open_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '开盘价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '收盘价格',
  `volume` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交量',
  `turnover` decimal(32,4) NOT NULL DEFAULT '0.0000' COMMENT '成交额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_market_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是休市数据',
  PRIMARY KEY (`code`,`time_range`,`time_type`,`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='控盘k线(用于接口数据替换)';

/*Table structure for table `exchange_trade_asset_type_tenant` */

DROP TABLE IF EXISTS `exchange_trade_asset_type_tenant`;

CREATE TABLE `exchange_trade_asset_type_tenant` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '编号',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  `asset_type` bigint NOT NULL DEFAULT '0' COMMENT '交易对资产类型',
  `name_i18n` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '名称code 国际化需要',
  `sort` tinyint NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '开启状态（0正常 1停用）',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint unsigned NOT NULL COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=82 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='租户交易对资产类型配置表';

/*Table structure for table `exchange_trade_pair` */

DROP TABLE IF EXISTS `exchange_trade_pair`;

CREATE TABLE `exchange_trade_pair` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '编号',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对名称',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对代码',
  `base_asset` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '基础资产',
  `quote_asset` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '报价资产',
  `trade_type` tinyint NOT NULL DEFAULT '0' COMMENT '交易类型 0:现货 1:合约期货 2:杠杆保证金',
  `asset_type` tinyint NOT NULL DEFAULT '0' COMMENT '资产类型 0:加密货币 1:股票 2:大宗商品/贵金属 3:外汇',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '开启状态（0正常 1停用）',
  `sync_status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '数据同步状态（0正常 1停止同步）',
  `source` tinyint NOT NULL DEFAULT '0' COMMENT '三方数据源 0:polygon 1:alltick',
  `scale` tinyint NOT NULL DEFAULT '2' COMMENT '小数部分显示位数',
  `mark_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '标记价格',
  `percentage` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '涨跌幅百分比',
  `icon` varchar(320) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '图标',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `deleted_time` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
  `issued_price` decimal(18,8) DEFAULT NULL COMMENT '发行价格',
  `reference_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '参考币',
  `candle_start_time` bigint DEFAULT NULL COMMENT 'k线开始时间',
  `is_custom` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否自发币',
  `candle_current_time` bigint DEFAULT NULL COMMENT '当前k线走到哪里，控盘时这个时间肯定大于当前时间不要去复制参考k线',
  `oneMinuteTurnover` decimal(24,6) DEFAULT NULL COMMENT '每分钟成交额',
  `one_minute_turnover` decimal(24,6) DEFAULT NULL COMMENT '每分钟成交额',
  `is_copy` bigint DEFAULT NULL COMMENT '是否复制参考交易对',
  `candle_time` bigint DEFAULT NULL COMMENT '当前k线时间，如果是复制币控盘的会有时间，当前时间小于这个时间就按控盘走，否则复制参考币',
  `is24_hour` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否是24小时的交易对',
  `transaction_time_dst_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '夏令时交易时间',
  `transaction_time_winter_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '冬令时交易时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uni_idx_code` (`code`,`deleted`,`deleted_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=92 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='系统交易对表';

/*Table structure for table `exchange_trade_pair_control` */

DROP TABLE IF EXISTS `exchange_trade_pair_control`;

CREATE TABLE `exchange_trade_pair_control` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `trade_pair_id` bigint NOT NULL COMMENT '交易对id',
  `trade_pair_code` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对',
  `start_time` bigint NOT NULL COMMENT '开始时间',
  `end_time` bigint NOT NULL COMMENT '结束时间',
  `end_price` decimal(20,8) NOT NULL COMMENT '结束价格',
  `status` int NOT NULL COMMENT '状态',
  `one_minute_turnover` decimal(20,8) NOT NULL COMMENT '分钟成交额',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint NOT NULL COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint NOT NULL COMMENT '修改时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户',
  `high_price` decimal(20,8) NOT NULL COMMENT '最高价',
  `low_price` decimal(20,8) NOT NULL COMMENT '最低价',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=27 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='控盘';

/*Table structure for table `exchange_trade_pair_tenant` */

DROP TABLE IF EXISTS `exchange_trade_pair_tenant`;

CREATE TABLE `exchange_trade_pair_tenant` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '编号',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  `trade_pair_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对代码',
  `trade_pair_id` bigint NOT NULL COMMENT '交易对编号',
  `sort` tinyint NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '开启状态（0正常 1停用）',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0' COMMENT '更新时间',
  `hot` bit(1) DEFAULT b'0' COMMENT '热门',
  `is_default` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否默认交易对',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uni_idx_tenant_trade_code` (`tenant_id`,`trade_pair_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=551 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='租户交易对表';

/*Table structure for table `exchange_trade_transaction_time` */

DROP TABLE IF EXISTS `exchange_trade_transaction_time`;

CREATE TABLE `exchange_trade_transaction_time` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `season` varchar(10) NOT NULL COMMENT '夏令时(dst)/冬令时(winter)',
  `config_name` varchar(20) NOT NULL COMMENT '配置名称',
  `day_of_week_open` varchar(10) NOT NULL COMMENT '周几开市 1:周日 2:周一 3:周二 4:周三 5:周四 6:周五 7:周六',
  `market_open` time NOT NULL COMMENT '开市时间',
  `day_of_week_close` varchar(10) NOT NULL COMMENT '周几休市',
  `market_close` time NOT NULL COMMENT '休市时间',
  `has_daily_break` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否有日内休市',
  `daily_break_start` time NOT NULL DEFAULT '00:00:00' COMMENT '日内休市开始时间',
  `daily_break_end` time NOT NULL DEFAULT '00:00:00' COMMENT '日内休市结束时间',
  `timezone` varchar(10) NOT NULL COMMENT '时区',
  `creator` varchar(100) DEFAULT NULL,
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) DEFAULT NULL,
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='交易对的交易时间表';

/*Table structure for table `infra_api_access_log` */

DROP TABLE IF EXISTS `infra_api_access_log`;

CREATE TABLE `infra_api_access_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `trace_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '链路追踪编号',
  `user_id` bigint NOT NULL DEFAULT '0' COMMENT '用户编号',
  `user_type` tinyint NOT NULL DEFAULT '0' COMMENT '用户类型',
  `application_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '应用名',
  `request_method` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '请求方法名',
  `request_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '请求地址',
  `request_params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '请求参数',
  `response_body` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '响应结果',
  `user_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户 IP',
  `user_agent` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '浏览器 UA',
  `operate_module` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作模块',
  `operate_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作名',
  `operate_type` tinyint DEFAULT '0' COMMENT '操作分类',
  `begin_time` bigint DEFAULT '0',
  `end_time` bigint DEFAULT '0',
  `duration` int NOT NULL COMMENT '执行时长',
  `result_code` int NOT NULL DEFAULT '0' COMMENT '结果码',
  `result_msg` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '结果提示',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='API 访问日志表';

/*Table structure for table `infra_api_error_log` */

DROP TABLE IF EXISTS `infra_api_error_log`;

CREATE TABLE `infra_api_error_log` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '编号',
  `trace_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '链路追踪编号\n     *\n     * 一般来说，通过链路追踪编号，可以将访问日志，错误日志，链路追踪日志，logger 打印日志等，结合在一起，从而进行排错。',
  `user_id` int NOT NULL DEFAULT '0' COMMENT '用户编号',
  `user_type` tinyint NOT NULL DEFAULT '0' COMMENT '用户类型',
  `application_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '应用名\n     *\n     * 目前读取 spring.application.name',
  `request_method` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '请求方法名',
  `request_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '请求地址',
  `request_params` varchar(8000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '请求参数',
  `user_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户 IP',
  `user_agent` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '浏览器 UA',
  `exception_time` bigint NOT NULL COMMENT '异常发生时间',
  `exception_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '异常名\n     *\n     * {@link Throwable#getClass()} 的类全名',
  `exception_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '异常导致的消息\n     *\n     * {@link cn.iocoder.common.framework.util.ExceptionUtil#getMessage(Throwable)}',
  `exception_root_cause_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '异常导致的根消息\n     *\n     * {@link cn.iocoder.common.framework.util.ExceptionUtil#getRootCauseMessage(Throwable)}',
  `exception_stack_trace` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '异常的栈轨迹\n     *\n     * {@link cn.iocoder.common.framework.util.ExceptionUtil#getServiceException(Exception)}',
  `exception_class_name` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '异常发生的类全名\n     *\n     * {@link StackTraceElement#getClassName()}',
  `exception_file_name` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '异常发生的类文件\n     *\n     * {@link StackTraceElement#getFileName()}',
  `exception_method_name` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '异常发生的方法名\n     *\n     * {@link StackTraceElement#getMethodName()}',
  `exception_line_number` int NOT NULL COMMENT '异常发生的方法所在行\n     *\n     * {@link StackTraceElement#getLineNumber()}',
  `process_status` tinyint NOT NULL COMMENT '处理状态',
  `process_time` bigint DEFAULT NULL COMMENT '处理时间',
  `process_user_id` int DEFAULT '0' COMMENT '处理用户编号',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6258 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='系统异常日志';

/*Table structure for table `infra_codegen_column` */

DROP TABLE IF EXISTS `infra_codegen_column`;

CREATE TABLE `infra_codegen_column` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table_id` bigint NOT NULL COMMENT '表编号',
  `column_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '字段名',
  `data_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '字段类型',
  `column_comment` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '字段描述',
  `nullable` bit(1) NOT NULL COMMENT '是否允许为空',
  `primary_key` bit(1) NOT NULL COMMENT '是否主键',
  `ordinal_position` int NOT NULL COMMENT '排序',
  `java_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Java 属性类型',
  `java_field` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Java 属性名',
  `dict_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '字典类型',
  `example` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '数据示例',
  `create_operation` bit(1) NOT NULL COMMENT '是否为 Create 创建操作的字段',
  `update_operation` bit(1) NOT NULL COMMENT '是否为 Update 更新操作的字段',
  `list_operation` bit(1) NOT NULL COMMENT '是否为 List 查询操作的字段',
  `list_operation_condition` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '=' COMMENT 'List 查询操作的条件类型',
  `list_operation_result` bit(1) NOT NULL COMMENT '是否为 List 查询操作的返回字段',
  `html_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '显示类型',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2153 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='代码生成表字段定义';

/*Table structure for table `infra_codegen_table` */

DROP TABLE IF EXISTS `infra_codegen_table`;

CREATE TABLE `infra_codegen_table` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `data_source_config_id` bigint NOT NULL COMMENT '数据源配置的编号',
  `scene` tinyint NOT NULL DEFAULT '1' COMMENT '生成场景',
  `table_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '表名称',
  `table_comment` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '表描述',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `module_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模块名',
  `business_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '业务名',
  `class_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '类名称',
  `class_comment` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '类描述',
  `author` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '作者',
  `template_type` tinyint NOT NULL DEFAULT '1' COMMENT '模板类型',
  `front_type` tinyint NOT NULL COMMENT '前端类型',
  `parent_menu_id` bigint DEFAULT NULL COMMENT '父菜单编号',
  `master_table_id` bigint DEFAULT NULL COMMENT '主表的编号',
  `sub_join_column_id` bigint DEFAULT NULL COMMENT '子表关联主表的字段编号',
  `sub_join_many` bit(1) DEFAULT NULL COMMENT '主表与子表是否一对多',
  `tree_parent_column_id` bigint DEFAULT NULL COMMENT '树表的父字段编号',
  `tree_name_column_id` bigint DEFAULT NULL COMMENT '树表的名字字段编号',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=133 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='代码生成表定义';

/*Table structure for table `infra_config` */

DROP TABLE IF EXISTS `infra_config`;

CREATE TABLE `infra_config` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '参数主键',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '参数分组',
  `type` tinyint NOT NULL COMMENT '参数类型',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '参数名称',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '参数键名',
  `value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '参数键值',
  `visible` bit(1) NOT NULL COMMENT '是否可见',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='参数配置表';

/*Table structure for table `infra_data_source_config` */

DROP TABLE IF EXISTS `infra_data_source_config`;

CREATE TABLE `infra_data_source_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键编号',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '参数名称',
  `url` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '数据源连接',
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '密码',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='数据源配置表';

/*Table structure for table `infra_file` */

DROP TABLE IF EXISTS `infra_file`;

CREATE TABLE `infra_file` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '文件编号',
  `config_id` bigint DEFAULT NULL COMMENT '配置编号',
  `name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '文件名',
  `path` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件路径',
  `url` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件 URL',
  `type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '文件类型',
  `size` int NOT NULL COMMENT '文件大小',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=359 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='文件表';

/*Table structure for table `infra_file_config` */

DROP TABLE IF EXISTS `infra_file_config`;

CREATE TABLE `infra_file_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `name` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置名',
  `storage` tinyint NOT NULL COMMENT '存储器',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `master` bit(1) NOT NULL COMMENT '是否为主配置',
  `config` varchar(4096) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '存储配置',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='文件配置表';

/*Table structure for table `infra_file_content` */

DROP TABLE IF EXISTS `infra_file_content`;

CREATE TABLE `infra_file_content` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `config_id` bigint NOT NULL COMMENT '配置编号',
  `path` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件路径',
  `content` mediumblob NOT NULL COMMENT '文件内容',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='文件表';

/*Table structure for table `infra_job` */

DROP TABLE IF EXISTS `infra_job`;

CREATE TABLE `infra_job` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务编号',
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '任务名称',
  `status` tinyint NOT NULL COMMENT '任务状态',
  `handler_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '处理器的名字',
  `handler_param` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '处理器的参数',
  `cron_expression` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'CRON 表达式',
  `retry_count` int NOT NULL DEFAULT '0' COMMENT '重试次数',
  `retry_interval` int NOT NULL DEFAULT '0' COMMENT '重试间隔',
  `monitor_timeout` int NOT NULL DEFAULT '0' COMMENT '监控超时时间',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='定时任务表';

/*Table structure for table `infra_job_log` */

DROP TABLE IF EXISTS `infra_job_log`;

CREATE TABLE `infra_job_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志编号',
  `job_id` bigint NOT NULL COMMENT '任务编号',
  `handler_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '处理器的名字',
  `handler_param` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '处理器的参数',
  `execute_index` tinyint NOT NULL DEFAULT '1' COMMENT '第几次执行',
  `begin_time` bigint DEFAULT '0',
  `end_time` bigint DEFAULT '0',
  `duration` int DEFAULT NULL COMMENT '执行时长',
  `status` tinyint NOT NULL COMMENT '任务状态',
  `result` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '结果数据',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='定时任务日志表';

/*Table structure for table `member_assets_spot` */

DROP TABLE IF EXISTS `member_assets_spot`;

CREATE TABLE `member_assets_spot` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '会员id',
  `username` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `trade_pair_id` bigint NOT NULL COMMENT '交易对id',
  `trade_pair_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对名称',
  `position_volume` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '现货持仓量',
  `balance` decimal(20,8) NOT NULL COMMENT '可用余额(总余额为占用余额+可用余额)',
  `balance_locked` decimal(20,8) NOT NULL COMMENT '占用余额',
  `execution_average_price` decimal(20,8) NOT NULL COMMENT '成交均价 (用于计算收益)',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  `agent_id` bigint DEFAULT NULL,
  `agent_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='会员现货资产表';

/*Table structure for table `member_certification` */

DROP TABLE IF EXISTS `member_certification`;

CREATE TABLE `member_certification` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` bigint NOT NULL COMMENT '用户',
  `username` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `area_id` bigint NOT NULL COMMENT '地区',
  `area_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '地区',
  `real_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '真名',
  `credentials_type` smallint NOT NULL COMMENT '证件类型',
  `credentials_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '证件号码',
  `credentials_front` varchar(1024) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '证件正面',
  `credentials_back` varchar(1024) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '证件反面',
  `status` smallint NOT NULL COMMENT '状态',
  `handler` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '处理人',
  `handle_time` bigint NOT NULL DEFAULT '0' COMMENT '处理时间',
  `handle_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '处理备注',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0' COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL COMMENT '租户',
  `agent_id` bigint DEFAULT NULL,
  `agent_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=79 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='会员身份认证';

/*Table structure for table `member_favorite_trade_pair` */

DROP TABLE IF EXISTS `member_favorite_trade_pair`;

CREATE TABLE `member_favorite_trade_pair` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` bigint NOT NULL COMMENT '会员id',
  `username` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `trade_pair_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对代码',
  `trade_pair_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对名称',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  `agent_id` bigint DEFAULT NULL,
  `agent_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=64 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='会员收藏交易对';

/*Table structure for table `member_frozen` */

DROP TABLE IF EXISTS `member_frozen`;

CREATE TABLE `member_frozen` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` bigint NOT NULL COMMENT '用户id',
  `username` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `frozen_amount` decimal(20,8) NOT NULL COMMENT '冻结金额',
  `frozen_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '冻结原因',
  `biz_order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '关联单号',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0' COMMENT '更新时间',
  `deleted` bigint NOT NULL DEFAULT '0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL COMMENT '租户编号',
  `agent_id` bigint DEFAULT NULL,
  `agent_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=185 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='会员冻结明细';

/*Table structure for table `member_funds_record` */

DROP TABLE IF EXISTS `member_funds_record`;

CREATE TABLE `member_funds_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '单号',
  `op_type` int NOT NULL COMMENT '类型：1充值，2提现',
  `user_id` bigint NOT NULL COMMENT '会员id',
  `username` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `currency_code` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '币种',
  `currency_amount` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '币种金额',
  `currency_rate` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '币种汇率',
  `usdt_amount` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT 'usdt金额',
  `fee_amount` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '提现手续费',
  `pay_method` tinyint NOT NULL DEFAULT '0' COMMENT '支付方式',
  `order_member_status` tinyint NOT NULL DEFAULT '0' COMMENT '会员订单状态',
  `order_system_status` tinyint NOT NULL DEFAULT '0' COMMENT '系统订单状态',
  `member_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '会员备注',
  `system_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '系统备注',
  `frozen_id` bigint NOT NULL DEFAULT '0' COMMENT '冻结的id',
  `wallet_id` bigint DEFAULT NULL COMMENT '钱包id',
  `wallet_type` tinyint NOT NULL DEFAULT '0' COMMENT '钱包类型，加密货币，银行',
  `wallet_type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '钱包名，链名，银行名',
  `wallet_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '银行户名',
  `wallet_account` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '银行账号/钱包哈希地址',
  `wallet_bank_address` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '银行地址',
  `wallet_bank_branch` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '银行支行',
  `handler` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '处理人',
  `handle_time` bigint NOT NULL DEFAULT '0' COMMENT '处理时间',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '删除',
  `tenant_id` bigint NOT NULL COMMENT '租户',
  `agent_id` bigint DEFAULT NULL,
  `agent_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `area_id` int DEFAULT NULL,
  `area` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=549 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='会员资金记录，如充值提现';

/*Table structure for table `member_level_config` */

DROP TABLE IF EXISTS `member_level_config`;

CREATE TABLE `member_level_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `level` int NOT NULL COMMENT '编码',
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '名称',
  `icon` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '图标',
  `first` bit(1) NOT NULL COMMENT '默认',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint NOT NULL COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint NOT NULL COMMENT '修改时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '删除',
  `tenant_id` bigint NOT NULL COMMENT '租户',
  `agent_id` bigint DEFAULT NULL,
  `agent_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=124 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='会员等级配置';

/*Table structure for table `member_margin_config` */

DROP TABLE IF EXISTS `member_margin_config`;

CREATE TABLE `member_margin_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT '会员id',
  `username` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `profile_type` tinyint NOT NULL DEFAULT '0' COMMENT '盈利类型 0:必亏 1:必赢 2:实际价格决定 3:随机盈利',
  `random_rate` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '随机盈利的赢率',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `agent_id` bigint DEFAULT NULL,
  `agent_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='会员合约开奖配置表';

/*Table structure for table `member_margin_order` */

DROP TABLE IF EXISTS `member_margin_order`;

CREATE TABLE `member_margin_order` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单号',
  `user_id` bigint NOT NULL COMMENT '会员id',
  `username` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `trade_pair_id` bigint unsigned NOT NULL COMMENT '交易对id',
  `trade_pair_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对名称',
  `trade_type` tinyint NOT NULL DEFAULT '0' COMMENT '交易类型 0:现货 1:合约期货 2:杠杆保证金',
  `asset_type` tinyint NOT NULL DEFAULT '0' COMMENT '资产类型 0:加密货币 1:股票 2:大宗商品 3:外汇',
  `position_start_price` decimal(20,8) NOT NULL COMMENT '开仓价格',
  `estimated_stop_price` decimal(20,8) NOT NULL COMMENT '预估强平价格',
  `position_volume` decimal(20,8) NOT NULL COMMENT '持仓量',
  `estimated_income` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '预估收益',
  `real_income` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '实际收益',
  `real_income_rate` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '实际收益率',
  `margin_rate` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '保证金率',
  `order_status` tinyint NOT NULL COMMENT '订单状态 0:进行中 1:已完成 2:失败',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  `agent_id` bigint DEFAULT NULL,
  `agent_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_user_trade_id` (`user_id`,`trade_pair_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='会员合约订单表';

/*Table structure for table `member_recharge` */

DROP TABLE IF EXISTS `member_recharge`;

CREATE TABLE `member_recharge` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '单号',
  `user_id` bigint NOT NULL COMMENT '会员 ID',
  `username` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `currency_id` bigint NOT NULL COMMENT '币种 ID',
  `currency_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '币种名称',
  `currency_amount` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '法币金额',
  `currency_rate` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '法币汇率',
  `recharge_amount` decimal(20,8) NOT NULL COMMENT '充值金额',
  `arrival_amount` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '到账金额',
  `pay_amount` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '实付金额',
  `pay_method` tinyint NOT NULL COMMENT '支付方式，虚拟/银行',
  `order_member_status` tinyint NOT NULL COMMENT '订单用户状态',
  `order_system_status` tinyint NOT NULL COMMENT '订单系统状态',
  `member_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '会员备注',
  `handler` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '操作员',
  `handle_time` bigint NOT NULL DEFAULT '0' COMMENT '处理时间',
  `system_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '系统备注',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0' COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  `agentId` bigint DEFAULT NULL,
  `agentName` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `agent_id` bigint DEFAULT NULL,
  `agent_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uni_order_no` (`order_no`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=38 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='会员充值表';

/*Table structure for table `member_spot_order` */

DROP TABLE IF EXISTS `member_spot_order`;

CREATE TABLE `member_spot_order` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单号',
  `user_id` bigint NOT NULL COMMENT '会员id',
  `username` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `trade_pair_id` bigint unsigned NOT NULL COMMENT '交易对id',
  `trade_pair_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对名称',
  `order_volume` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '委托数量',
  `order_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '委托价格',
  `execution_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '成交价格',
  `fee_amount` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '手续费金额',
  `asset_type` tinyint NOT NULL DEFAULT '0' COMMENT '资产类型 0:加密货币 1:股票 2:大宗商品 3:外汇',
  `order_status` tinyint NOT NULL COMMENT '订单状态 0:进行中 1:已完成 2:失败',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  `agent_id` bigint DEFAULT NULL,
  `agent_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_user_trade_id` (`user_id`,`trade_pair_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='会员现货订单记录表';

/*Table structure for table `member_total_day` */

DROP TABLE IF EXISTS `member_total_day`;

CREATE TABLE `member_total_day` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `agent_id` bigint NOT NULL COMMENT '代理',
  `agent_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '代理',
  `user_id` bigint NOT NULL COMMENT '会员',
  `username` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `total_time` bigint NOT NULL COMMENT '归属时间',
  `usdt_balance` decimal(18,8) NOT NULL COMMENT '余额',
  `profit_amount` decimal(18,8) NOT NULL COMMENT '盈利',
  `order_count` int NOT NULL COMMENT '单数',
  `recharge` decimal(18,8) NOT NULL COMMENT '充值',
  `withdraw` decimal(18,8) NOT NULL COMMENT '提现',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint NOT NULL COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint NOT NULL COMMENT '修改时间',
  `deleted` bit(1) NOT NULL COMMENT '删除',
  `tenant_id` bigint NOT NULL COMMENT '租户',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='会员日统计数据';

/*Table structure for table `member_transactions` */

DROP TABLE IF EXISTS `member_transactions`;

CREATE TABLE `member_transactions` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT '会员id',
  `username` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `type` tinyint NOT NULL COMMENT '发生类型，1充值，2提现，3买入现货, 4卖出现货 5买入合约 6卖出合约',
  `change_amount` decimal(20,8) NOT NULL,
  `currency_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '货币名称',
  `before_balance` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '发生前余额',
  `after_balance` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '发生后余额',
  `biz_order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '业务订单号',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '备注',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0' COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  `agent_id` bigint DEFAULT NULL,
  `agent_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=101465 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='会员账变表';

/*Table structure for table `member_user` */

DROP TABLE IF EXISTS `member_user`;

CREATE TABLE `member_user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `agent_id` bigint NOT NULL COMMENT '所属代理',
  `agent_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '所属代理',
  `username` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '注册账号',
  `mobile` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机号',
  `email` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮箱',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '密码',
  `fund_password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '资金密码',
  `certification_status` smallint NOT NULL DEFAULT '0' COMMENT '认证信息状态',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
  `register_ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '注册ip',
  `register_terminal` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '深山终端',
  `login_ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '登陆 ip',
  `login_date` bigint NOT NULL DEFAULT '0' COMMENT '登陆时间',
  `nickname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '头像',
  `name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '名称',
  `sex` tinyint NOT NULL DEFAULT '0' COMMENT '性别',
  `birthday` bigint NOT NULL DEFAULT '0' COMMENT '生日',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `usdt_balance` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '余额',
  `usdt_frozen_balance` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '冻结余额',
  `today_income` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '今天收益',
  `currency_id` bigint NOT NULL COMMENT '默认货币id',
  `currency_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '币种',
  `level_id` int DEFAULT NULL COMMENT '等级id',
  `level_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '等级名称',
  `area_id` bigint NOT NULL COMMENT '地区id',
  `area_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '地区',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0' COMMENT '修改时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '假删',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  `recharge` decimal(20,8) DEFAULT NULL COMMENT '总充值金额',
  `withdraw` decimal(20,8) DEFAULT NULL COMMENT '总提款金额',
  `demo` bit(1) DEFAULT NULL COMMENT '试玩',
  `credit_score` int DEFAULT '100',
  `disable_withdraw` bit(1) NOT NULL DEFAULT b'0' COMMENT '禁止提现',
  `disable_time_contract` bit(1) NOT NULL DEFAULT b'0' COMMENT '禁止限时合约下注',
  `disable_contract` bit(1) NOT NULL DEFAULT b'0' COMMENT '禁止合约下单',
  `pwd_wrong_count` int unsigned NOT NULL DEFAULT '0' COMMENT '密码输入错误次数(统计时间范围为永久时此字段才会有值)',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_username` (`username`) USING BTREE,
  KEY `idx_mobile` (`mobile`) USING BTREE,
  KEY `idx_email` (`email`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=102362 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='会员表';

/*Table structure for table `member_user_config` */

DROP TABLE IF EXISTS `member_user_config`;

CREATE TABLE `member_user_config` (
  `member_id` bigint NOT NULL COMMENT '会员id,主键',
  `profit_type` int NOT NULL DEFAULT '0' COMMENT '盈利类型 0必亏 1必赢 2实时价格 3随机盈利',
  `random_rate` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '随机盈利的赢率',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
  `agent_id` bigint DEFAULT NULL,
  `agent_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`member_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='会员配置表';

/*Table structure for table `member_wallet` */

DROP TABLE IF EXISTS `member_wallet`;

CREATE TABLE `member_wallet` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` bigint NOT NULL COMMENT '会员id',
  `username` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `type` tinyint NOT NULL COMMENT '类型，虚拟，银行',
  `type_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '类型名称，如银行名，或者链名',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '名称，如户名',
  `account` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '账号，如银行卡号，或者钱包地址',
  `bank_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '银行地址',
  `bank_branch` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '支行',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
  `area_id` bigint NOT NULL DEFAULT '0' COMMENT '地区id',
  `area_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '地区名称',
  `currency_id` bigint NOT NULL COMMENT '货币id',
  `currency_code` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `currency_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '货币名称',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0' COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  `agent_id` bigint DEFAULT NULL,
  `agent_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=112 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='会员钱包';

/*Table structure for table `member_withdraw` */

DROP TABLE IF EXISTS `member_withdraw`;

CREATE TABLE `member_withdraw` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '单号',
  `user_id` bigint NOT NULL COMMENT '会员id',
  `username` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `currency_id` bigint NOT NULL COMMENT '货币id',
  `currency_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '货币',
  `currency_rate` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '币种汇率',
  `currency_amount` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '币种金额',
  `withdraw_amount` decimal(20,8) NOT NULL COMMENT '提现金额',
  `deduction_amount` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '操作金额',
  `transfer_amount` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '转账金额',
  `fee_amount` decimal(12,4) NOT NULL COMMENT '手续费',
  `wallet_id` bigint NOT NULL COMMENT '钱包id',
  `wallet_type` tinyint NOT NULL COMMENT '钱包类型',
  `wallet_type_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '钱包类型名',
  `wallet_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '钱包名',
  `wallet_account` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '钱包账号',
  `bank_address` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '银行地址',
  `bank_branch` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '支行地址',
  `member_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '会员备注',
  `system_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '系统备注',
  `order_member_status` tinyint NOT NULL COMMENT '订单会员状态',
  `order_system_status` tinyint NOT NULL COMMENT '订单系统状态',
  `frozen_id` bigint NOT NULL COMMENT '冻结关联id',
  `handler` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '处理人',
  `handle_time` bigint NOT NULL DEFAULT '0' COMMENT '处理时间',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint NOT NULL DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint NOT NULL DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  `agentId` bigint DEFAULT NULL,
  `agentName` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `agent_id` bigint DEFAULT NULL,
  `agent_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uni_order_no` (`order_no`) USING BTREE,
  KEY `idx_user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='会员提现表';

/*Table structure for table `miner_product` */

DROP TABLE IF EXISTS `miner_product`;

CREATE TABLE `miner_product` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `title` varchar(512) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标题',
  `cycle` int NOT NULL COMMENT '周期-单位天',
  `img` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '图片 ',
  `min_profit` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '最小收益',
  `max_profit` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '最大收益',
  `min_purchase` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '最低购买',
  `max_purchase` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '最高购买',
  `liquidated_damages_ratio` decimal(6,2) NOT NULL DEFAULT '0.00' COMMENT '违约金-百分比',
  `sort` tinyint NOT NULL COMMENT '排序',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0' COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1025 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='挖矿产品表';

/*Table structure for table `miner_product_income` */

DROP TABLE IF EXISTS `miner_product_income`;

CREATE TABLE `miner_product_income` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `order_no` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单号',
  `product_id` int NOT NULL COMMENT '产品ID',
  `user_id` bigint NOT NULL COMMENT '会员id',
  `username` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `principal` decimal(12,8) NOT NULL DEFAULT '0.********' COMMENT '本金',
  `income` decimal(12,8) NOT NULL DEFAULT '0.********' COMMENT '利息',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0' COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='挖矿收益表';

/*Table structure for table `miner_product_order` */

DROP TABLE IF EXISTS `miner_product_order`;

CREATE TABLE `miner_product_order` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `order_no` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单号',
  `product_id` int NOT NULL COMMENT '产品ID',
  `user_id` bigint NOT NULL COMMENT '会员id',
  `username` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `cycle` int NOT NULL COMMENT '周期-单位天',
  `order_amount` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '订单金额',
  `order_income` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '订单收益',
  `order_status` tinyint NOT NULL COMMENT '订单状态 1:锁仓 2:赎回 3:结束',
  `liquidated_damages` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '违约金',
  `liquidated_damages_ratio` decimal(6,2) NOT NULL DEFAULT '0.00' COMMENT '违约金-百分比',
  `expire_time` bigint DEFAULT '0' COMMENT '到期时间',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0' COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户',
  `reward_date` date DEFAULT NULL COMMENT '奖励发放日期',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='挖矿订单表';

/*Table structure for table `system_agent` */

DROP TABLE IF EXISTS `system_agent`;

CREATE TABLE `system_agent` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '代理编号',
  `name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '代码名称',
  `user_id` bigint NOT NULL COMMENT '用户id',
  `user_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '登陆用户名',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邀请码',
  `status` tinyint NOT NULL COMMENT '帐号状态（0正常 1停用）',
  `remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '备注',
  `first` bit(1) NOT NULL DEFAULT b'0',
  `ancestor_id` bigint NOT NULL COMMENT '直属父代理id',
  `ancestor_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '上级名称',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0' COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `delete_time` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uni_code_delete_time` (`code`,`delete_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=186 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='代理';

/*Table structure for table `system_agent_statistic` */

DROP TABLE IF EXISTS `system_agent_statistic`;

CREATE TABLE `system_agent_statistic` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `agent_id` bigint NOT NULL COMMENT '代理',
  `agent_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '代理',
  `register_count` bigint NOT NULL DEFAULT '0' COMMENT '线下人数',
  `recharge_count` bigint NOT NULL DEFAULT '0' COMMENT '充值人数',
  `withdraw_count` bigint NOT NULL DEFAULT '0' COMMENT '提现人数',
  `total_balance` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '总余额',
  `total_deposit_amount` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '总充值',
  `total_withdraw_amount` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '总提现',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_agent_id` (`agent_id`,`tenant_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=163 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='代理统计';

/*Table structure for table `system_agent_statistic_history` */

DROP TABLE IF EXISTS `system_agent_statistic_history`;

CREATE TABLE `system_agent_statistic_history` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `agent_id` bigint NOT NULL COMMENT '代理',
  `agent_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '代理',
  `register_count` bigint NOT NULL DEFAULT '0' COMMENT '线下人数',
  `recharge_count` bigint NOT NULL DEFAULT '0' COMMENT '充值人数',
  `withdraw_count` bigint NOT NULL DEFAULT '0' COMMENT '提现人数',
  `total_balance` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '总余额',
  `total_deposit_amount` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '总充值',
  `total_withdraw_amount` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '总提现',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='代理数据统计历史';

/*Table structure for table `system_agent_tree` */

DROP TABLE IF EXISTS `system_agent_tree`;

CREATE TABLE `system_agent_tree` (
  `id` int NOT NULL AUTO_INCREMENT,
  `ancestor` bigint unsigned NOT NULL COMMENT '父代理id',
  `descendant` bigint unsigned NOT NULL COMMENT '子代理id',
  `dept` int unsigned NOT NULL COMMENT '级差(代理层级的距离)',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uni_idx_ancestor_descendant` (`ancestor`,`descendant`) USING BTREE,
  KEY `idx_ancestor_dept` (`ancestor`,`dept`) USING BTREE,
  KEY `idx_descendant_dept` (`descendant`,`dept`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=361 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='代理关系';

/*Table structure for table `system_banner` */

DROP TABLE IF EXISTS `system_banner`;

CREATE TABLE `system_banner` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `title` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标题',
  `img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '图片',
  `sort` tinyint NOT NULL COMMENT '排序',
  `link_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '链接路径 ',
  `link_type` tinyint NOT NULL DEFAULT '0' COMMENT '链接类型，0内网，1外网',
  `location` tinyint(1) DEFAULT '0' COMMENT '位置，left/right,后续可以自定义增加，只要改前端就行',
  `trade_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '交易对id',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0' COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  `trade_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=25736 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='banner';

/*Table structure for table `system_currency` */

DROP TABLE IF EXISTS `system_currency`;

CREATE TABLE `system_currency` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '币种编号',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '币种名称',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '货币代码',
  `symbol` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '币种符号如$，¥',
  `type` tinyint NOT NULL DEFAULT '0' COMMENT '货币类型 0:法币 1:加密货币',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '开启状态（0正常 1停用）',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `country_id` int DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2592 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='系统货币表';

/*Table structure for table `system_dept` */

DROP TABLE IF EXISTS `system_dept`;

CREATE TABLE `system_dept` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '部门id',
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '部门名称',
  `parent_id` bigint NOT NULL DEFAULT '0' COMMENT '父部门id',
  `sort` int NOT NULL DEFAULT '0' COMMENT '显示顺序',
  `leader_user_id` bigint DEFAULT NULL COMMENT '负责人',
  `phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系电话',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮箱',
  `status` tinyint NOT NULL COMMENT '部门状态（0正常 1停用）',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=114 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='部门表';

/*Table structure for table `system_dict_data` */

DROP TABLE IF EXISTS `system_dict_data`;

CREATE TABLE `system_dict_data` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典编码',
  `sort` int NOT NULL DEFAULT '0' COMMENT '字典排序',
  `label` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '字典标签',
  `value` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '字典键值',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '字典类型',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `color_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '颜色类型',
  `css_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT 'css 样式',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=650 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='字典数据表';

/*Table structure for table `system_dict_type` */

DROP TABLE IF EXISTS `system_dict_type`;

CREATE TABLE `system_dict_type` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典主键',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '字典名称',
  `type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '字典类型',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `deleted_time` bigint DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `dict_type` (`type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=61 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='字典类型表';

/*Table structure for table `system_i18n_text` */

DROP TABLE IF EXISTS `system_i18n_text`;

CREATE TABLE `system_i18n_text` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '国际化代码',
  `lang` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '语言',
  `text` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '翻译内容',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=272 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='国际化文本配置表';

/*Table structure for table `system_lang` */

DROP TABLE IF EXISTS `system_lang`;

CREATE TABLE `system_lang` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '编码',
  `local_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '本地化名称',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '名称',
  `sort` int NOT NULL COMMENT '排序',
  `status` smallint NOT NULL DEFAULT '0' COMMENT '状态',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '删除',
  `ico` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=27 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='系统语种配置';

/*Table structure for table `system_lang_content` */

DROP TABLE IF EXISTS `system_lang_content`;

CREATE TABLE `system_lang_content` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `word` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '内容key',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '对应语言内容',
  `lang` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '语种',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='系统内容多语种';

/*Table structure for table `system_login_log` */

DROP TABLE IF EXISTS `system_login_log`;

CREATE TABLE `system_login_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '访问ID',
  `log_type` bigint NOT NULL COMMENT '日志类型',
  `trace_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '链路追踪编号',
  `user_id` bigint NOT NULL DEFAULT '0' COMMENT '用户编号',
  `user_type` tinyint NOT NULL DEFAULT '0' COMMENT '用户类型',
  `username` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `result` tinyint NOT NULL COMMENT '登陆结果',
  `user_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户 IP',
  `user_agent` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '浏览器 UA',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3925 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='系统访问记录';

/*Table structure for table `system_mail_account` */

DROP TABLE IF EXISTS `system_mail_account`;

CREATE TABLE `system_mail_account` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `mail` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮箱',
  `username` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '密码',
  `secret` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '密钥',
  `host` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'SMTP 服务器域名',
  `port` int NOT NULL COMMENT 'SMTP 服务器端口',
  `webhook_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'webhook签名的key',
  `ssl_enable` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否开启 SSL',
  `starttls_enable` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否开启 STARTTLS',
  `sender_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '发件人名称',
  `template_codes` varchar(320) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '可用的模版代码,使用逗号分隔',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `delete_time` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
  `tenant_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '租户id',
  `domain` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮箱账号的域',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uni_mail_delete_time` (`mail`,`delete_time`) USING BTREE,
  KEY `idx_tenant_mail` (`tenant_id`,`mail`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='邮箱账号表';

/*Table structure for table `system_mail_code` */

DROP TABLE IF EXISTS `system_mail_code`;

CREATE TABLE `system_mail_code` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `mail` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮箱',
  `code` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '验证码',
  `create_ip` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建ip',
  `scene` tinyint NOT NULL COMMENT '发送场景',
  `today_index` tinyint NOT NULL DEFAULT '0' COMMENT '今日发送的第几条',
  `used` tinyint NOT NULL DEFAULT '0' COMMENT '是否使用',
  `used_time` bigint NOT NULL DEFAULT '0' COMMENT '使用时间',
  `used_ip` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '使用ip',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=68 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='邮箱验证码';

/*Table structure for table `system_mail_log` */

DROP TABLE IF EXISTS `system_mail_log`;

CREATE TABLE `system_mail_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `user_id` bigint DEFAULT NULL COMMENT '用户编号',
  `user_type` tinyint DEFAULT NULL COMMENT '用户类型',
  `to_mail` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '接收邮箱地址',
  `account_id` bigint NOT NULL COMMENT '邮箱账号编号',
  `from_mail` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '发送邮箱地址',
  `template_id` bigint NOT NULL COMMENT '模板编号',
  `template_code` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板编码',
  `template_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮件标题',
  `template_content` varchar(10240) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮件内容',
  `template_params` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮件参数',
  `send_status` tinyint NOT NULL DEFAULT '0' COMMENT '发送状态',
  `send_time` bigint DEFAULT '0',
  `send_message_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '发送返回的消息 ID',
  `send_exception` varchar(4096) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '发送异常',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `tenant_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '租户id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_message_id` (`send_message_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=38 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='邮件日志表';

/*Table structure for table `system_mail_log_detail` */

DROP TABLE IF EXISTS `system_mail_log_detail`;

CREATE TABLE `system_mail_log_detail` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `log_id` bigint unsigned NOT NULL COMMENT '日志id',
  `message_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '消息id',
  `event` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '消息事件',
  `detail` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '日志详情',
  `time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发生事件',
  `create_time` bigint unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_log_id` (`log_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='邮件发送日志详情';

/*Table structure for table `system_mail_template` */

DROP TABLE IF EXISTS `system_mail_template`;

CREATE TABLE `system_mail_template` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `name` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板名称',
  `code` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板编码',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板内容',
  `params` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '参数数组',
  `content_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'plainText' COMMENT '内容类型',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '开启状态',
  `lang` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '语言',
  `tenant_id` bigint unsigned NOT NULL COMMENT '租户id',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '备注',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0' COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `delete_time` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='邮件模版表';

/*Table structure for table `system_menu` */

DROP TABLE IF EXISTS `system_menu`;

CREATE TABLE `system_menu` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '菜单名称',
  `permission` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '权限标识',
  `type` tinyint NOT NULL COMMENT '菜单类型',
  `sort` int NOT NULL DEFAULT '0' COMMENT '显示顺序',
  `parent_id` bigint NOT NULL DEFAULT '0' COMMENT '父菜单ID',
  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '路由地址',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '#' COMMENT '菜单图标',
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '组件路径',
  `component_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '组件名',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '菜单状态',
  `visible` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否可见',
  `keep_alive` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否缓存',
  `always_show` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否总是显示',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2851 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='菜单权限表';

/*Table structure for table `system_notice` */

DROP TABLE IF EXISTS `system_notice`;

CREATE TABLE `system_notice` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '公告ID',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '公告标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '公告内容',
  `type` tinyint NOT NULL COMMENT '公告类型（1通知 2公告）',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '公告状态（0正常 1关闭）',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1043 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='通知公告表';

/*Table structure for table `system_notify_message` */

DROP TABLE IF EXISTS `system_notify_message`;

CREATE TABLE `system_notify_message` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `user_id` bigint NOT NULL COMMENT '用户id',
  `user_type` tinyint NOT NULL COMMENT '用户类型',
  `template_id` bigint NOT NULL COMMENT '模版编号',
  `template_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板编码',
  `template_nickname` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模版发送人名称',
  `template_content` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模版内容',
  `template_type` int NOT NULL COMMENT '模版类型',
  `template_params` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模版参数',
  `read_status` bit(1) NOT NULL COMMENT '是否已读',
  `read_time` bigint DEFAULT '0',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='站内信消息表';

/*Table structure for table `system_notify_template` */

DROP TABLE IF EXISTS `system_notify_template`;

CREATE TABLE `system_notify_template` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板名称',
  `code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模版编码',
  `nickname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '发送人名称',
  `content` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模版内容',
  `type` tinyint NOT NULL COMMENT '类型',
  `params` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '参数数组',
  `status` tinyint NOT NULL COMMENT '状态',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='站内信模板表';

/*Table structure for table `system_oauth2_access_token` */

DROP TABLE IF EXISTS `system_oauth2_access_token`;

CREATE TABLE `system_oauth2_access_token` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `user_id` bigint NOT NULL COMMENT '用户编号',
  `user_type` tinyint NOT NULL COMMENT '用户类型',
  `user_info` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户信息',
  `access_token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '访问令牌',
  `refresh_token` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '刷新令牌',
  `client_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户端编号',
  `scopes` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '授权范围',
  `expires_time` bigint unsigned DEFAULT '0',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_access_token` (`access_token`) USING BTREE,
  KEY `idx_refresh_token` (`refresh_token`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3417 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='OAuth2 访问令牌';

/*Table structure for table `system_oauth2_approve` */

DROP TABLE IF EXISTS `system_oauth2_approve`;

CREATE TABLE `system_oauth2_approve` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `user_id` bigint NOT NULL COMMENT '用户编号',
  `user_type` tinyint NOT NULL COMMENT '用户类型',
  `client_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户端编号',
  `scope` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '授权范围',
  `approved` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否接受',
  `expires_time` bigint DEFAULT '0',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=82 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='OAuth2 批准表';

/*Table structure for table `system_oauth2_client` */

DROP TABLE IF EXISTS `system_oauth2_client`;

CREATE TABLE `system_oauth2_client` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `client_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户端编号',
  `secret` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户端密钥',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '应用名',
  `logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '应用图标',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '应用描述',
  `status` tinyint NOT NULL COMMENT '状态',
  `access_token_validity_seconds` int NOT NULL COMMENT '访问令牌的有效期',
  `refresh_token_validity_seconds` int NOT NULL COMMENT '刷新令牌的有效期',
  `redirect_uris` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '可重定向的 URI 地址',
  `authorized_grant_types` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '授权类型',
  `scopes` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '授权范围',
  `auto_approve_scopes` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '自动通过的授权范围',
  `authorities` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '权限',
  `resource_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '资源',
  `additional_information` varchar(4096) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '附加信息',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=47 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='OAuth2 客户端表';

/*Table structure for table `system_oauth2_code` */

DROP TABLE IF EXISTS `system_oauth2_code`;

CREATE TABLE `system_oauth2_code` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `user_id` bigint NOT NULL COMMENT '用户编号',
  `user_type` tinyint NOT NULL COMMENT '用户类型',
  `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '授权码',
  `client_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户端编号',
  `scopes` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '授权范围',
  `expires_time` bigint DEFAULT '0',
  `redirect_uri` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '可重定向的 URI 地址',
  `state` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '状态',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='OAuth2 授权码表';

/*Table structure for table `system_oauth2_refresh_token` */

DROP TABLE IF EXISTS `system_oauth2_refresh_token`;

CREATE TABLE `system_oauth2_refresh_token` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `user_id` bigint NOT NULL COMMENT '用户编号',
  `refresh_token` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '刷新令牌',
  `user_type` tinyint NOT NULL COMMENT '用户类型',
  `client_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户端编号',
  `scopes` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '授权范围',
  `expires_time` bigint unsigned DEFAULT '0',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2308 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='OAuth2 刷新令牌';

/*Table structure for table `system_operate_log` */

DROP TABLE IF EXISTS `system_operate_log`;

CREATE TABLE `system_operate_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `trace_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '链路追踪编号',
  `user_id` bigint NOT NULL COMMENT '用户编号',
  `user_type` tinyint NOT NULL DEFAULT '0' COMMENT '用户类型',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作模块类型',
  `sub_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作名',
  `biz_id` bigint NOT NULL COMMENT '操作数据模块编号',
  `action` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '操作内容',
  `extra` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '拓展字段',
  `request_method` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '请求方法名',
  `request_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '请求地址',
  `user_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户 IP',
  `user_agent` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '浏览器 UA',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=671 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='操作日志记录 V2 版本';

/*Table structure for table `system_position` */

DROP TABLE IF EXISTS `system_position`;

CREATE TABLE `system_position` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '岗位ID',
  `code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '岗位编码',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '岗位名称',
  `sort` int NOT NULL COMMENT '显示顺序',
  `status` tinyint NOT NULL COMMENT '状态（0正常 1停用）',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='岗位信息表';

/*Table structure for table `system_recharge_channel` */

DROP TABLE IF EXISTS `system_recharge_channel`;

CREATE TABLE `system_recharge_channel` (
  `id` int NOT NULL AUTO_INCREMENT,
  `type` tinyint NOT NULL DEFAULT '0' COMMENT '虚拟，银行',
  `type_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '银行名，链名',
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '名称',
  `account` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '银行卡号/钱包地址',
  `bank_address` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '银行地址',
  `bank_branch` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '支行',
  `currency_id` bigint NOT NULL COMMENT '货币id',
  `currency_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '货币名称',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '开启状态（0正常 1停用）',
  `area_id` bigint NOT NULL DEFAULT '0' COMMENT '地区id',
  `area_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '地区名称',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='充值通道';

/*Table structure for table `system_role` */

DROP TABLE IF EXISTS `system_role`;

CREATE TABLE `system_role` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色名称',
  `code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色权限字符串',
  `sort` int NOT NULL COMMENT '显示顺序',
  `data_scope` tinyint NOT NULL DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  `data_scope_dept_ids` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '数据范围(指定部门数组)',
  `status` tinyint NOT NULL COMMENT '角色状态（0正常 1停用）',
  `type` tinyint NOT NULL COMMENT '角色类型',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=92 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='角色信息表';

/*Table structure for table `system_role_menu` */

DROP TABLE IF EXISTS `system_role_menu`;

CREATE TABLE `system_role_menu` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增编号',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `menu_id` bigint NOT NULL COMMENT '菜单ID',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3602 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='角色和菜单关联表';

/*Table structure for table `system_sms_channel` */

DROP TABLE IF EXISTS `system_sms_channel`;

CREATE TABLE `system_sms_channel` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `signature` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '短信签名',
  `code` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '渠道编码',
  `status` tinyint NOT NULL COMMENT '开启状态',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `api_key` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '短信 API 的账号',
  `api_secret` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '短信 API 的秘钥',
  `callback_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '短信发送回调 URL',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='短信渠道';

/*Table structure for table `system_sms_code` */

DROP TABLE IF EXISTS `system_sms_code`;

CREATE TABLE `system_sms_code` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `mobile` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `code` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_ip` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `scene` tinyint NOT NULL COMMENT '发送场景',
  `today_index` tinyint NOT NULL COMMENT '今日发送的第几条',
  `used` tinyint NOT NULL COMMENT '是否使用',
  `used_time` bigint DEFAULT NULL COMMENT '使用时间',
  `used_ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_mobile` (`mobile`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='手机验证码';

/*Table structure for table `system_sms_log` */

DROP TABLE IF EXISTS `system_sms_log`;

CREATE TABLE `system_sms_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `channel_id` bigint NOT NULL COMMENT '短信渠道编号',
  `channel_code` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '短信渠道编码',
  `template_id` bigint NOT NULL COMMENT '模板编号',
  `template_code` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板编码',
  `template_type` tinyint NOT NULL COMMENT '短信类型',
  `template_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '短信内容',
  `template_params` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '短信参数',
  `api_template_id` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '短信 API 的模板编号',
  `mobile` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '手机号',
  `user_id` bigint DEFAULT NULL COMMENT '用户编号',
  `user_type` tinyint DEFAULT NULL COMMENT '用户类型',
  `send_status` tinyint NOT NULL DEFAULT '0' COMMENT '发送状态',
  `send_time` bigint DEFAULT NULL COMMENT '发送时间',
  `api_send_code` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '短信 API 发送结果的编码',
  `api_send_msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '短信 API 发送失败的提示',
  `api_request_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '短信 API 发送返回的唯一请求 ID',
  `api_serial_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '短信 API 发送返回的序号',
  `receive_status` tinyint NOT NULL DEFAULT '0' COMMENT '接收状态',
  `receive_time` bigint DEFAULT NULL COMMENT '接收时间',
  `api_receive_code` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'API 接收结果的编码',
  `api_receive_msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'API 接收结果的说明',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='短信日志';

/*Table structure for table `system_sms_template` */

DROP TABLE IF EXISTS `system_sms_template`;

CREATE TABLE `system_sms_template` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `type` tinyint NOT NULL COMMENT '模板类型',
  `status` tinyint NOT NULL COMMENT '开启状态',
  `code` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板编码',
  `name` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板名称',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板内容',
  `params` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '参数数组',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `api_template_id` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '短信 API 的模板编号',
  `channel_id` bigint NOT NULL COMMENT '短信渠道编号',
  `channel_code` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '短信渠道编码',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1025 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='短信模板';

/*Table structure for table `system_social_client` */

DROP TABLE IF EXISTS `system_social_client`;

CREATE TABLE `system_social_client` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '应用名',
  `social_type` tinyint NOT NULL COMMENT '社交平台的类型',
  `user_type` tinyint NOT NULL COMMENT '用户类型',
  `client_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户端编号',
  `client_secret` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户端密钥',
  `agent_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '代理编号',
  `status` tinyint NOT NULL COMMENT '状态',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=44 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='社交客户端表';

/*Table structure for table `system_social_user` */

DROP TABLE IF EXISTS `system_social_user`;

CREATE TABLE `system_social_user` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键(自增策略)',
  `type` tinyint NOT NULL COMMENT '社交平台的类型',
  `openid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '社交 openid',
  `token` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '社交 token',
  `raw_token_info` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '原始 Token 数据，一般是 JSON 格式',
  `nickname` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户昵称',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户头像',
  `raw_user_info` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '原始用户数据，一般是 JSON 格式',
  `code` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '最后一次的认证 code',
  `state` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '最后一次的认证 state',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='社交用户表';

/*Table structure for table `system_social_user_bind` */

DROP TABLE IF EXISTS `system_social_user_bind`;

CREATE TABLE `system_social_user_bind` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键(自增策略)',
  `user_id` bigint NOT NULL COMMENT '用户编号',
  `user_type` tinyint NOT NULL COMMENT '用户类型',
  `social_type` tinyint NOT NULL COMMENT '社交平台的类型',
  `social_user_id` bigint NOT NULL COMMENT '社交用户的编号',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='社交绑定表';

/*Table structure for table `system_tenant` */

DROP TABLE IF EXISTS `system_tenant`;

CREATE TABLE `system_tenant` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '租户编号',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '租户编码',
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '租户名',
  `logo` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'logo',
  `contact_user_id` bigint DEFAULT NULL COMMENT '联系人的用户编号',
  `contact_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '联系人',
  `contact_mobile` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系手机',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '租户状态（0正常 1停用）',
  `package_id` bigint NOT NULL COMMENT '租户套餐编号',
  `expire_time` bigint NOT NULL DEFAULT '0' COMMENT '过期时间',
  `account_count` int NOT NULL COMMENT '账号数量',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uni_idx_code` (`code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1195 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='租户表';

/*Table structure for table `system_tenant_currency` */

DROP TABLE IF EXISTS `system_tenant_currency`;

CREATE TABLE `system_tenant_currency` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `currency_id` bigint NOT NULL COMMENT '币种',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='租户币种表';

/*Table structure for table `system_tenant_dict_data` */

DROP TABLE IF EXISTS `system_tenant_dict_data`;

CREATE TABLE `system_tenant_dict_data` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `label` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标签名',
  `value` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标签值',
  `dict_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '类型',
  `sort` smallint NOT NULL DEFAULT '0' COMMENT '排序',
  `status` smallint NOT NULL DEFAULT '0' COMMENT '状态',
  `color_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '颜色类型',
  `css_class` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '样式',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '备注',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '假删',
  `tenant_id` bigint NOT NULL COMMENT '租户id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5122 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='租户字典数据';

/*Table structure for table `system_tenant_dict_type` */

DROP TABLE IF EXISTS `system_tenant_dict_type`;

CREATE TABLE `system_tenant_dict_type` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '名称',
  `type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '编码类型',
  `status` smallint NOT NULL DEFAULT '0' COMMENT '状态',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '‘’' COMMENT '备注',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '假删',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=25497 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='租户配置分类表';

/*Table structure for table `system_tenant_package` */

DROP TABLE IF EXISTS `system_tenant_package`;

CREATE TABLE `system_tenant_package` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '套餐编号',
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '套餐名',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '租户状态（0正常 1停用）',
  `remark` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '备注',
  `menu_ids` varchar(4096) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '关联的菜单编号',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=113 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='租户套餐表';

/*Table structure for table `system_tenant_server_name` */

DROP TABLE IF EXISTS `system_tenant_server_name`;

CREATE TABLE `system_tenant_server_name` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '编号',
  `ten_id` bigint unsigned NOT NULL COMMENT '租户id',
  `server_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '域名',
  `is_share` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否可以作为分享链接',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '开启状态（0正常 1停用）',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uni_idx_ten_server_name` (`ten_id`,`server_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=46 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='系统租户域名表';

/*Table structure for table `system_trade_duration` */

DROP TABLE IF EXISTS `system_trade_duration`;

CREATE TABLE `system_trade_duration` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `duration` tinyint NOT NULL COMMENT '时长',
  `time_unit` tinyint NOT NULL COMMENT '单位时间',
  `sort` tinyint NOT NULL DEFAULT '0' COMMENT '排序',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='订单时长配置';

/*Table structure for table `system_trade_multiple` */

DROP TABLE IF EXISTS `system_trade_multiple`;

CREATE TABLE `system_trade_multiple` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `multiple` tinyint NOT NULL COMMENT '倍数',
  `sort` tinyint NOT NULL DEFAULT '0' COMMENT '排序',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='交易倍数';

/*Table structure for table `system_user_position` */

DROP TABLE IF EXISTS `system_user_position`;

CREATE TABLE `system_user_position` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` bigint NOT NULL DEFAULT '0' COMMENT '用户',
  `post_id` bigint NOT NULL DEFAULT '0' COMMENT '职位',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
  `deleted` bigint NOT NULL DEFAULT '0' COMMENT '删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;

/*Table structure for table `system_user_post` */

DROP TABLE IF EXISTS `system_user_post`;

CREATE TABLE `system_user_post` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` bigint NOT NULL DEFAULT '0' COMMENT '用户ID',
  `post_id` bigint NOT NULL DEFAULT '0' COMMENT '岗位ID',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='用户岗位表';

/*Table structure for table `system_user_role` */

DROP TABLE IF EXISTS `system_user_role`;

CREATE TABLE `system_user_role` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增编号',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint DEFAULT '0' COMMENT '更新时间',
  `deleted` bit(1) DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=283 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='用户和角色关联表';

/*Table structure for table `system_users` */

DROP TABLE IF EXISTS `system_users`;

CREATE TABLE `system_users` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '密码',
  `nickname` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户昵称',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `dept_id` bigint DEFAULT NULL COMMENT '部门ID',
  `post_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '岗位编号数组',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '用户邮箱',
  `mobile` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '手机号码',
  `sex` tinyint DEFAULT '0' COMMENT '用户性别',
  `avatar` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '头像地址',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
  `login_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '最后登录IP',
  `login_date` bigint DEFAULT '0' COMMENT '最后登录时间',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  `secret` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_tenant_admin` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是租户管理员用户账号',
  `is_agent_user` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是代理用户账号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_username` (`username`,`update_time`,`tenant_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1308 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='用户信息表';

/*Table structure for table `trade_contract_order` */

DROP TABLE IF EXISTS `trade_contract_order`;

CREATE TABLE `trade_contract_order` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `order_no` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单号',
  `position_record_no` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '仓位订单号',
  `is_visible` bit(1) NOT NULL DEFAULT b'1' COMMENT '管理后台和APP是否可见此记录 true:可见',
  `user_id` bigint unsigned NOT NULL COMMENT '会员id',
  `username` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `code` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对代码',
  `price_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '价格类型 0:市价订单 1:限价订单',
  `short_long` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '多空类型 0:做空 1:做多',
  `order_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '委托类型 0:开仓委托 1:平仓委托 2:止盈委托 3:止损委托 4:强平委托',
  `margin_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '保证金类型 0:全仓 1:逐仓',
  `margin` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '实际保证金',
  `order_amount` decimal(20,8) NOT NULL COMMENT '委托金额',
  `order_amount_currency` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '委托金额法币币种',
  `order_volume` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '委托币数量',
  `order_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '委托价格',
  `transaction_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '成交价格',
  `leverage` tinyint unsigned NOT NULL COMMENT '杠杆倍数',
  `fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '手续费',
  `order_status` tinyint unsigned NOT NULL COMMENT '订单状态 0:委托中 1:已成交',
  `remark` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '备注',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `tenant_id` bigint unsigned NOT NULL COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=280 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='合约委托记录表';

/*Table structure for table `trade_contract_position` */

DROP TABLE IF EXISTS `trade_contract_position`;

CREATE TABLE `trade_contract_position` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '编号',
  `record_no` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '记录编号',
  `user_id` bigint unsigned NOT NULL COMMENT '会员id',
  `username` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `code` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易对代码',
  `asset_type` tinyint unsigned NOT NULL COMMENT '持仓资产类型',
  `short_long` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '多空类型 0:做空 1:做多',
  `close_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '平仓类型 1手动平仓 2自动止盈 3自动止损 4自动强平',
  `position_status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '仓位状态 0:持仓中 1:已平仓',
  `margin` decimal(20,8) NOT NULL COMMENT '初始保证金',
  `margin_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '保证金模式 0:全仓 1:逐仓',
  `margin_currency` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '初始保证金法币币种',
  `open_price` decimal(20,8) NOT NULL COMMENT '开仓价格',
  `close_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '平仓价格',
  `volume` decimal(20,8) NOT NULL COMMENT '持仓量',
  `volume_asset` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '持仓资产',
  `volume_value` decimal(20,8) NOT NULL COMMENT '持仓价值',
  `volume_value_currency` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '持仓价值法币币种',
  `position_fee` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '持仓手续费',
  `stop_profit_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '止盈价',
  `stop_profit_order_no` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '止盈委托订单号',
  `stop_loss_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '止损价',
  `stop_loss_order_no` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '止损委托订单号',
  `profit_loss` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '收益亏损',
  `profit_loss_rate` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '收益亏损率',
  `leverage` int unsigned NOT NULL DEFAULT '1' COMMENT '杠杆倍数',
  `open_time` bigint unsigned NOT NULL DEFAULT '0' COMMENT '开仓时间',
  `close_time` bigint unsigned NOT NULL DEFAULT '0' COMMENT '平仓时间',
  `remark` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '备注',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `tenant_id` bigint unsigned NOT NULL COMMENT '租户编号',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_record_no` (`record_no`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=279 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='合约持仓记录表';

/*Table structure for table `trade_duration` */

DROP TABLE IF EXISTS `trade_duration`;

CREATE TABLE `trade_duration` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `duration` int unsigned NOT NULL COMMENT '时长',
  `time_unit` tinyint NOT NULL COMMENT '单位时间',
  `min_amount` int unsigned NOT NULL COMMENT '最低购买金额',
  `profit_rate` decimal(10,2) NOT NULL COMMENT '收益率',
  `sort` tinyint NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '开启状态 0开启 1停用',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=122 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='订单时长配置';

/*Table structure for table `trade_multiple` */

DROP TABLE IF EXISTS `trade_multiple`;

CREATE TABLE `trade_multiple` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `multiple` tinyint NOT NULL COMMENT '倍数',
  `sort` tinyint NOT NULL DEFAULT '0' COMMENT '排序',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='交易倍数';

/*Table structure for table `trade_time_contract_record` */

DROP TABLE IF EXISTS `trade_time_contract_record`;

CREATE TABLE `trade_time_contract_record` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '记录id',
  `trade_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易号',
  `user_id` bigint unsigned NOT NULL COMMENT '用户id',
  `username` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '交易对代码',
  `send_time` bigint unsigned NOT NULL COMMENT '发送时间(时间戳)',
  `settle_time` bigint NOT NULL DEFAULT '0',
  `duration` int unsigned NOT NULL COMMENT '时长（单位秒）',
  `order_price` decimal(20,8) NOT NULL COMMENT '买入价格',
  `settle_price` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '结算价格',
  `amount` decimal(20,8) NOT NULL COMMENT '数量',
  `short_long` tinyint unsigned NOT NULL COMMENT '交易方向 0做空 1做多',
  `profit_type` tinyint NOT NULL DEFAULT '-1' COMMENT '盈利类型 0:必亏 1:必赢 2:实际价格决定 3:随机盈利',
  `profit_rate` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '收益率',
  `lose_profit_rate` decimal(10,2) NOT NULL COMMENT '亏损率',
  `random_rate` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '随机赢率',
  `profit_result` decimal(20,8) NOT NULL DEFAULT '0.********' COMMENT '实际利润',
  `trade_status` tinyint unsigned NOT NULL COMMENT '交易状态',
  `fail_reason` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `creator` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` bigint unsigned NOT NULL COMMENT '创建时间',
  `updater` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新时间',
  `tenant_id` bigint unsigned NOT NULL COMMENT '租户id',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uni_idx_trade_no` (`trade_no`) USING BTREE,
  UNIQUE KEY `uni_idx_user_code_send` (`user_id`,`send_time`,`code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13554 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='限时合约交易记录表';

DROP TABLE IF EXISTS `system_tenant`;
CREATE TABLE `system_tenant`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '租户编号',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '租户编码',
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '租户名',
  `logo` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'logo',
  `contact_user_id` bigint NULL DEFAULT NULL COMMENT '联系人的用户编号',
  `contact_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '联系人',
  `contact_mobile` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系手机',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '租户状态（0正常 1停用）',
  `package_id` bigint NOT NULL COMMENT '租户套餐编号',
  `expire_time` bigint NOT NULL DEFAULT 0 COMMENT '过期时间',
  `account_count` int NOT NULL COMMENT '账号数量',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '创建者',
  `create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` bigint NOT NULL DEFAULT 0 COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uni_idx_code`(`code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1183 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '租户表' ROW_FORMAT = DYNAMIC;

-- 交易时间配置数据
INSERT INTO `exchange_trade_transaction_time`(`id`,`season`,`config_name`,`day_of_week_open`,`market_open`,`day_of_week_close`,`market_close`,`has_daily_break`,`daily_break_start`,`daily_break_end`,`timezone`,`creator`,`create_time`,`updater`,`update_time`) VALUES 
(1,'dst','外汇-夏令时','sun','21:00:00','fri','21:00:00',0,'00:00:00','00:00:00','GMT0','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000),
(2,'winter','外汇-冬令时','sun','22:00:00','fri','22:00:00',0,'00:00:00','00:00:00','GMT0','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000),
(3,'dst','金属外汇-夏令时','sun','22:00:00','fri','21:00:00',1,'21:00:00','22:00:00','GMT0','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000),
(4,'winter','金属外汇-冬令时','sun','23:00:00','fri','22:00:00',1,'22:00:00','23:00:00','GMT0','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000),
(5,'dst','贵金属-夏令时','sun','22:00:00','fri','21:00:00',1,'21:00:00','22:00:00','GMT0','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000),
(6,'winter','贵金属-冬令时','sun','23:00:00','fri','22:00:00',1,'22:00:00','23:00:00','GMT0','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000),
(7,'dst','贵金属01-夏令时','sun','22:00:00','fri','21:00:00',1,'21:00:00','22:00:00','GMT0','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000),
(8,'winter','贵金属01-冬令时','sun','23:00:00','fri','22:00:00',1,'22:00:00','23:00:00','GMT0','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000),
(9,'dst','贵金属02-夏令时','mon','00:00:00','fri','18:00:00',1,'18:00:00','00:00:00','GMT0','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000),
(10,'winter','贵金属02-冬令时','mon','01:00:00','fri','19:00:00',1,'19:00:00','01:00:00','GMT0','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000),
(11,'dst','贵金属03-夏令时','mon','07:00:00','fri','18:00:00',1,'18:00:00','07:00:00','GMT0','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000),
(12,'winter','贵金属03-冬令时','mon','08:00:00','fri','19:00:00',1,'19:00:00','08:00:00','GMT0','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000),
(13,'dst','贵金属04-夏令时','sun','00:00:00','fri','18:00:00',1,'18:00:00','00:00:00','GMT0','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000),
(14,'winter','贵金属04-冬令时','mon','08:00:00','fri','19:00:00',1,'19:00:00','08:00:00','GMT0','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000),
(15,'dst','能源-夏令时','mon','00:00:00','fri','21:00:00',1,'21:00:00','22:00:00','GMT0','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000),
(16,'winter','能源-冬令时','mon','01:00:00','fri','22:00:00',1,'22:00:00','23:00:00','GMT0','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000);

-- ========================================
-- 3. 基础交易对数据
-- ========================================

-- 主要加密货币交易对
INSERT INTO `exchange_trade_pair`(`id`,`name`,`code`,`base_asset`,`quote_asset`,`trade_type`,`asset_type`,`status`,`sync_status`,`source`,`scale`,`mark_price`,`percentage`,`icon`,`creator`,`create_time`,`updater`,`update_time`,`deleted`,`deleted_time`,`issued_price`,`reference_code`,`candle_start_time`,`is_custom`,`candle_current_time`,`oneMinuteTurnover`,`one_minute_turnover`,`is_copy`,`candle_time`,`is24_hour`,`transaction_time_dst_id`,`transaction_time_winter_id`) VALUES
(1,'BTC/USDT','BTCUSDT','BTC','USDT',3,0,0,0,1,2,0.********,0.00,'https://example.com/icons/btcusdt.png','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000,0,0,NULL,NULL,0,0,0,3000.000000,3000.000000,0,0,'',0,0),
(2,'ETH/USDT','ETHUSDT','ETH','USDT',3,0,0,0,1,4,0.********,0.00,'https://example.com/icons/ethusdt.png','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000,0,0,NULL,NULL,0,0,0,3000.000000,3000.000000,0,0,'',0,0),
(3,'DOGE/USDT','DOGEUSDT','DOGE','USDT',3,0,0,0,1,4,0.********,0.00,'https://example.com/icons/dogeusdt.png','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000,0,0,NULL,NULL,0,0,0,3000.000000,3000.000000,0,NULL,'',0,0),
(4,'BNB/USDT','BNBUSDT','BNB','USDT',3,0,0,0,1,1,0.********,0.00,'https://example.com/icons/bnbusdt.png','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000,0,0,NULL,NULL,0,0,0,3000.000000,3000.000000,0,NULL,'',0,0),
(5,'BCH/USDT','BCHUSDT','BCH','USDT',3,0,0,0,1,4,0.********,0.00,'https://example.com/icons/bchusdt.png','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000,0,0,NULL,NULL,0,0,0,3000.000000,3000.000000,0,NULL,'',0,0),
(6,'AAVE/USDT','AAVEUSDT','AAVE','USDT',3,0,0,0,1,4,0.********,0.00,'https://example.com/icons/aaveusdt.png','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000,0,0,NULL,NULL,0,0,0,3000.000000,3000.000000,0,NULL,'',0,0),
(7,'AVAX/USDT','AVAXUSDT','AVAX','USDT',3,0,0,0,1,8,0.********,0.00,'https://example.com/icons/avaxusdt.png','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000,0,0,NULL,NULL,0,0,0,3000.000000,3000.000000,0,NULL,'',0,0),
(8,'BONK/USDT','BONKUSDT','BONK','USDT',3,0,0,0,1,8,0.********,0.00,'https://example.com/icons/bonkusdt.png','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000,0,0,NULL,NULL,0,0,0,3000.000000,3000.000000,0,NULL,'',0,0),
(9,'PEPE/USDT','PEPEUSDT','PEPE','USDT',3,0,0,0,1,8,0.********,0.00,'https://example.com/icons/pepeusdt.png','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000,0,0,NULL,NULL,0,0,0,3000.000000,3000.000000,0,0,'',0,0);

-- 外汇交易对
INSERT INTO `exchange_trade_pair`(`id`,`name`,`code`,`base_asset`,`quote_asset`,`trade_type`,`asset_type`,`status`,`sync_status`,`source`,`scale`,`mark_price`,`percentage`,`icon`,`creator`,`create_time`,`updater`,`update_time`,`deleted`,`deleted_time`,`issued_price`,`reference_code`,`candle_start_time`,`is_custom`,`candle_current_time`,`oneMinuteTurnover`,`one_minute_turnover`,`is_copy`,`candle_time`,`is24_hour`,`transaction_time_dst_id`,`transaction_time_winter_id`) VALUES
(10,'AUD/JPY','AUDJPY','AUD','JPY',3,3,0,0,1,4,0.********,0.00,'https://example.com/icons/audjpy.png','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000,0,0,NULL,NULL,0,0,0,3000.000000,3000.000000,0,NULL,0,1,2),
(11,'EUR/JPY','EURJPY','EUR','JPY',3,3,0,0,1,4,0.********,0.00,'https://example.com/icons/eurjpy.png','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000,0,0,NULL,NULL,0,0,0,3000.000000,3000.000000,0,NULL,0,1,2),
(12,'USD/JPY','USDJPY','USD','JPY',3,3,0,0,1,4,0.********,0.00,'https://example.com/icons/usdjpy.png','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000,0,0,NULL,NULL,0,0,0,3000.000000,3000.000000,0,0,0,1,2),
(13,'USD/THB','USDTHB','USD','THB',3,3,0,0,1,4,0.********,0.00,'https://example.com/icons/usdthb.png','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000,0,0,NULL,NULL,0,0,0,3000.000000,3000.000000,0,NULL,0,1,2),
(14,'USD/CNY','USDCNY','USD','CNY',3,3,0,0,1,4,0.********,0.00,'https://example.com/icons/usdcny.png','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000,0,0,NULL,NULL,0,0,0,3000.000000,3000.000000,0,NULL,0,1,2),
(15,'USD/HKD','USDHKD','USD','HKD',3,3,0,0,1,4,0.********,0.00,'https://example.com/icons/usdhkd.png','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000,0,0,NULL,NULL,0,0,0,3000.000000,3000.000000,0,NULL,0,1,2),
(16,'USD/DKK','USDDKK','USD','DKK',3,3,0,0,1,2,0.********,0.00,'https://example.com/icons/usddkk.png','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000,0,0,NULL,'',0,0,NULL,NULL,NULL,0,0,'',0,0),
(17,'AUD/CAD','AUDCAD','AUD','CAD',3,3,0,0,1,2,0.********,0.00,'https://example.com/icons/audcad.png','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000,0,0,NULL,'',0,0,NULL,NULL,NULL,0,0,'',0,0);

-- 贵金属交易对
INSERT INTO `exchange_trade_pair`(`id`,`name`,`code`,`base_asset`,`quote_asset`,`trade_type`,`asset_type`,`status`,`sync_status`,`source`,`scale`,`mark_price`,`percentage`,`icon`,`creator`,`create_time`,`updater`,`update_time`,`deleted`,`deleted_time`,`issued_price`,`reference_code`,`candle_start_time`,`is_custom`,`candle_current_time`,`oneMinuteTurnover`,`one_minute_turnover`,`is_copy`,`candle_time`,`is24_hour`,`transaction_time_dst_id`,`transaction_time_winter_id`) VALUES
(18,'GOLD','XAUUSD','XAU','USD',3,2,0,0,1,4,0.********,0.00,'https://example.com/icons/xauusd.png','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000,0,0,NULL,NULL,0,0,0,3000.000000,3000.000000,0,NULL,0,7,8),
(19,'SILVER','XAGUSD','XAG','USD',3,2,0,0,1,4,0.********,0.00,'https://example.com/icons/xagusd.png','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000,0,0,NULL,NULL,0,0,0,3000.000000,3000.000000,0,NULL,0,7,8),
(20,'COPPER','XCUUSD','XCU','USD',3,2,0,0,1,2,0.********,0.00,'https://example.com/icons/xcuusd.png','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000,0,0,NULL,NULL,0,0,0,3000.000000,3000.000000,0,NULL,0,9,10),
(21,'Nickel','XNIUSD','XNI','USD',3,2,0,0,1,4,0.********,0.00,'https://example.com/icons/xniusd.png','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000,0,0,NULL,NULL,0,0,0,3000.000000,3000.000000,0,NULL,0,11,12);

-- 能源交易对
INSERT INTO `exchange_trade_pair`(`id`,`name`,`code`,`base_asset`,`quote_asset`,`trade_type`,`asset_type`,`status`,`sync_status`,`source`,`scale`,`mark_price`,`percentage`,`icon`,`creator`,`create_time`,`updater`,`update_time`,`deleted`,`deleted_time`,`issued_price`,`reference_code`,`candle_start_time`,`is_custom`,`candle_current_time`,`oneMinuteTurnover`,`one_minute_turnover`,`is_copy`,`candle_time`,`is24_hour`,`transaction_time_dst_id`,`transaction_time_winter_id`) VALUES
(22,'UKOIL','UKOUSD','UKO','USD',3,2,0,0,1,4,0.********,0.00,'https://example.com/icons/ukoil.png','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000,0,0,NULL,NULL,0,0,0,3000.000000,3000.000000,0,NULL,0,13,14),
(23,'ROBUSTA','ROBUSTA','USD','USD',3,2,0,0,1,2,0.********,0.00,'https://example.com/icons/robusta.png','system',UNIX_TIMESTAMP()*1000,'system',UNIX_TIMESTAMP()*1000,0,0,NULL,'',0,0,NULL,NULL,NULL,0,0,'',0,0);



-- 协议类型字典
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES
('协议类型', 'system_agreement_type', 1, '协议管理的协议类型', 'system', NOW(), 'system', NOW(), 0);

INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES
(1, '隐私协议', '1', 'system_agreement_type', 1, 'primary', '', '隐私协议', 'system', NOW(), 'system', NOW(), 0),
(2, '用户准则', '2', 'system_agreement_type', 1, 'success', '', '用户准则', 'system', NOW(), 'system', NOW(), 0),
(3, '服务条款', '3', 'system_agreement_type', 1, 'info', '', '服务条款', 'system', NOW(), 'system', NOW(), 0),
(4, '免责声明', '4', 'system_agreement_type', 1, 'warning', '', '免责声明', 'system', NOW(), 'system', NOW(), 0);

-- 交易对类型字典
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES
('交易对类型', 'exchange_trade_pair_type', 1, '交易对的类型分类', 'system', NOW(), 'system', NOW(), 0);

INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES
(1, '加密货币', '0', 'exchange_trade_pair_type', 1, 'primary', '', '加密货币交易对', 'system', NOW(), 'system', NOW(), 0),
(2, '股票', '1', 'exchange_trade_pair_type', 1, 'success', '', '股票交易对', 'system', NOW(), 'system', NOW(), 0),
(3, '贵金属', '2', 'exchange_trade_pair_type', 1, 'warning', '', '贵金属交易对', 'system', NOW(), 'system', NOW(), 0),
(4, '外汇', '3', 'exchange_trade_pair_type', 1, 'info', '', '外汇交易对', 'system', NOW(), 'system', NOW(), 0);

-- 交易状态字典
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES
('交易状态', 'exchange_trade_status', 1, '交易订单状态', 'system', NOW(), 'system', NOW(), 0);

INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES
(1, '待成交', '0', 'exchange_trade_status', 1, 'primary', '', '订单待成交', 'system', NOW(), 'system', NOW(), 0),
(2, '已成交', '1', 'exchange_trade_status', 1, 'success', '', '订单已成交', 'system', NOW(), 'system', NOW(), 0),
(3, '已取消', '2', 'exchange_trade_status', 1, 'danger', '', '订单已取消', 'system', NOW(), 'system', NOW(), 0),
(4, '部分成交', '3', 'exchange_trade_status', 1, 'warning', '', '订单部分成交', 'system', NOW(), 'system', NOW(), 0);


-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

