-- 协议多语言内容表
CREATE TABLE `system_agreement_content` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `agreement_id` bigint NOT NULL COMMENT '协议ID，关联system_agreement表',
  `language_code` varchar(10) NOT NULL COMMENT '语言代码，如zh-CN, en, ja, ko等',
  `title` varchar(200) NOT NULL COMMENT '协议标题',
  `content` longtext NOT NULL COMMENT '协议内容（HTML格式）',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_agreement_language` (`agreement_id`, `language_code`, `deleted`),
  KEY `idx_agreement_id` (`agreement_id`),
  KEY `idx_language_code` (`language_code`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='协议多语言内容表';

-- 插入示例数据
INSERT INTO `system_agreement_content` (`agreement_id`, `language_code`, `title`, `content`, `creator`) VALUES
-- 隐私协议多语言版本
(1, 'zh-CN', '隐私协议', '<h1>隐私协议</h1><p>我们重视您的隐私保护...</p>', 'admin'),
(1, 'en', 'Privacy Policy', '<h1>Privacy Policy</h1><p>We value your privacy protection...</p>', 'admin'),
(1, 'ja', 'プライバシーポリシー', '<h1>プライバシーポリシー</h1><p>お客様のプライバシー保護を重視しています...</p>', 'admin'),

-- 用户准则多语言版本
(2, 'zh-CN', '用户准则', '<h1>用户准则</h1><p>为了维护良好的交易环境...</p>', 'admin'),
(2, 'en', 'User Guidelines', '<h1>User Guidelines</h1><p>To maintain a good trading environment...</p>', 'admin'),
(2, 'ja', 'ユーザーガイドライン', '<h1>ユーザーガイドライン</h1><p>良好な取引環境を維持するため...</p>', 'admin'),

-- 服务条款多语言版本
(3, 'zh-CN', '服务条款', '<h1>服务条款</h1><p>欢迎使用我们的服务...</p>', 'admin'),
(3, 'en', 'Terms of Service', '<h1>Terms of Service</h1><p>Welcome to use our services...</p>', 'admin'),
(3, 'ja', '利用規約', '<h1>利用規約</h1><p>当社のサービスをご利用いただき...</p>', 'admin');
