COUNTRY_TH=Thailand
COUNTRY_JP=Japan
COUNTRY_KR=Korea
COUNTRY_PH=Philippines
COUNTRY_VN=Vietnam
COUNTRY_CN=China
COUNTRY_GB=United Kingdom
COUNTRY_FR=France
COUNTRY_US=United States
COUNTRY_ES=Spain
COUNTRY_DE=Germany
COUNTRY_RU=Russia
COUNTRY_CA=Canada
COUNTRY_TR=T\u00FCrkiye
COUNTRY_PT=Portugal
COUNTRY_MA=Morocco
COUNTRY_DZ=Algeria
COUNTRY_IT=Italy
COUNTRY_CO=Colombia
COUNTRY_MX=Mexico
COUNTRY_CH=Switzerland
COUNTRY_BE=Belgium
COUNTRY_AR=Argentina
COUNTRY_NO=Norway
COUNTRY_HK=Hong Kong
BANK=bank card
CRYPTO=Cryptocurrency
USER_CERTIFIED_STATUS_NOT=Unauthenticated
USER_CERTIFIED_STATUS_HANDLING=Authorizing
USER_CERTIFIED_STATUS_SUCCESS=Authorized
USER_CERTIFIED_STATUS_FAIL=Authentication failed
ACCOUNT_NOT_EMPTY=Account cannot be empty
PASSWORD_NOT_EMPTY=Password cannot be empty
PASSWORD_NOT_LENGTH_6_16=Password length is 6-16 characters
PASSWORD_FORMATTER_ERROR=Password format is numbers and letters
ACCOUNT_TYPE_ERROR=Account type error
MAIL_FORMATTER_ERROR=Email format error
MOBILE_FORMATTER_ERROR=Mobile number format error
AREA_NOT_EMPTY=Region cannot be empty
MAIL_SCENE_ERROR=SMS sending scenario error
SMS_SCENE_ERROR=SMS sending scenario error
REAL_NAME_NOT_EMPTY=Real name cannot be empty
CERTIFICATION_TYPE_ERROR=Identity authentication type error
CERTIFICATION_CODE_NOT_EMPTY=ID number cannot be empty
CERTIFICATION_FRONT_NOT_EMPTY=ID front photo cannot be empty
CERTIFICATION_BACK_NOT_EMPTY=ID back photo cannot be empty
TRADE_PAIR_NOT_EMPTY=Please select trading pair
FUNDS_RECORD_OP_TYPE=Fund type error
CURRENCY_NOT_EMPTY=Please select currency
PAY_METHOD_ERROR=Please select payment method
RECHARGE_AMOUNT_ERROR=Please enter top-up amount
WITHDRAW_AMOUNT_ERROR=Fully enter withdrawal amount
FUNDS_PASSWORD_ERROR=Fund password error
WALLET_NOT_EMPTY=Please select wallet
AUTH_CODE_NOT_EMPTY=Please enter verification code
AVATAR_FORMATTER_ERROR=Please select avatar
WALLET_TYPE_ERROR=Wallet type error
WALLET_NAME_NOT_EMPTY=Please enter wallet name
WALLET_ACCOUNT_NOT_EMPTY=Please enter wallet account number
WALLET_TYPE_NAME_NOT_EMPTY=Please enter bank name
ASSET_Type_ERROR=Asset type error
KEY_NOT_EMPTY=Keyword cannot be empty
AMOUNT_NOT_EMPTY=Amount error
TRADE_DIRECT_ERROR=Transaction direction error
TRADE_DURATION_ERROR=Transaction time error
TRADE_SEND_TIME_ERROR=Transaction sending time error
TRADE_PRICE_TIME_ERROR=Transaction price time error
TRADE_PAGE_TYPE_ERROR=Classification error
SUCCESS=Success
WAITHANDLE=Processing
FAILURE=fail
PENDING=Pending
BAD_REQUEST=Wrong request
UNKNOW_AUTHORIZED=Unknown authorization type [{}]
TOKEN_NOT_SUPPORT_MODE=Token interface does not support [{}] authorization mode
CLIENT_ERROR=Client_id or client_secret is not passed correctly
TOKEN_REFRESH_INVALID=Invalid refresh token
TOKEN_REFRESH_CLIENT_ERROR=Incorrect client number of refresh token
TOKEN_REFRESH_EXPIRE=Refresh token expired
TOKEN_NOT_EXISTS=Access token does not exist
TOKEN_EXPIRE=Access token expired
GRANT_RESPONSE_TYPE_ERROR=The response_type parameter value only allows code and token
UNAUTHORIZED=Account not logged in
FORBIDDEN=No permission for this operation
NOT_FOUND=The requested resource does not exist
METHOD_NOT_ALLOWED=Request method not allowed
LOCKED=Locked
TOO_MANY_REQUESTS=Requests are too frequent, please try again later
INTERNAL_SERVER_ERROR=System abnormality
NOT_IMPLEMENTED=Function not implemented/not enabled
REPEATED_REQUESTS=Repeated requests, please try again later
DEMO_DENY=Demo mode, write operation prohibited
VALUE_ERROR=Value error
GOOGLE_AUTH_NOT_BIND=Google Authenticator is not bound
GOOGLE_AUTH_CODE_ERROR=Google verification code error
SCHEDULER_JOB_STOP=[Scheduled task - disabled][Reference https://doc.iocoder.cn/job/ Enable]
CANDLE_TABLE_NAME_NOT_AVAILABLE=K-line table name is invalid
CANDLE_BAR_VALUE_ERROR=Bar parameter is invalid
CANDLE_PRICE_ERROR=Get price error
TRADE_PAIR_NOT_EXISTS=Trading pair does not exist
TRADE_PAIR_EXISTS=Trading pair already exists
TRADE_PAIR_TENANT_NOT_EXISTS=Tenant does not have this trading pair
TRADE_PAIR_TENANT_EXISTS=Tenant trading pair already exists
TRADE_TENANT_ASSET_TYPE_NOT_EXISTS=Tenant trading pair asset type configuration does not exist
TRADE_TENANT_NEED_DEFAULT=There must be a default tenant trading pair
TRADE_TENANT_DUPLICATE_DEFAULT=Tenant's default trading pair already exists
CONFIG_NOT_EXISTS=Parameter configuration does not exist
CONFIG_KEY_DUPLICATE=Parameter configuration key Duplicate
CONFIG_CAN_NOT_DELETE_SYSTEM_TYPE=Cannot delete parameter configuration of system built-in type
CONFIG_GET_VALUE_ERROR_IF_VISIBLE=Failed to obtain parameter configuration, reason: Obtaining invisible configuration is not allowed
JOB_NOT_EXISTS=Scheduled task does not exist
JOB_HANDLER_EXISTS=The processor of the scheduled task already exists
JOB_CHANGE_STATUS_INVALID=Only change to open or close state is allowed
JOB_CHANGE_STATUS_EQUALS=The scheduled task is already in this state, no modification is required
JOB_UPDATE_ONLY_NORMAL_STATUS=Only tasks in the open state can be modified
JOB_CRON_EXPRESSION_VALID=CRON expression is incorrect
JOB_HANDLER_BEAN_NOT_EXISTS=The processor bean of the scheduled task does not exist
JOB_HANDLER_BEAN_TYPE_ERROR=The processor bean type of the scheduled task is incorrect, and the JobHandler interface is not implemented
API_ERROR_LOG_NOT_FOUND=API error log does not exist
API_ERROR_LOG_PROCESSED=API error log has been processed
FILE_PATH_EXISTS=File path already exists
FILE_NOT_EXISTS=File does not exist
FILE_IS_EMPTY=File is empty
CODEGEN_TABLE_EXISTS=Table definition already exists
CODEGEN_IMPORT_TABLE_NULL=Imported table does not exist
CODEGEN_IMPORT_COLUMNS_NULL=Imported field does not exist
CODEGEN_TABLE_NOT_EXISTS=Table definition does not exist
CODEGEN_COLUMN_NOT_EXISTS=Field definition does not exist
CODEGEN_SYNC_COLUMNS_NULL=Synchronized field does not exist
CODEGEN_SYNC_NONE_CHANGE=Synchronization failed, no change
CODEGEN_TABLE_INFO_TABLE_COMMENT_IS_NULL=Database table comment is not filled in
CODEGEN_TABLE_INFO_COLUMN_COMMENT_IS_NULL=Database table field ({}) comment is not filled in
CODEGEN_MASTER_TABLE_NOT_EXISTS=The main table (id={}) definition does not exist, please check
CODEGEN_SUB_COLUMN_NOT_EXISTS=Subtable field (id={}) does not exist, please check
CODEGEN_MASTER_GENERATION_FAIL_NO_SUB_TABLE=Main table code generation failed, reason: it has no subtable
FILE_CONFIG_NOT_EXISTS=File configuration does not exist
FILE_CONFIG_DELETE_FAIL_MASTER=This file configuration cannot be deleted, reason: it is the main configuration, deletion will result in failure to upload files
DATA_SOURCE_CONFIG_NOT_EXISTS=Data source Configuration does not exist
DATA_SOURCE_CONFIG_NOT_OK=Data source configuration is incorrect, unable to connect
DEMO01_CONTACT_NOT_EXISTS=Sample contact does not exist
DEMO02_CATEGORY_NOT_EXISTS=Sample category does not exist
DEMO02_CATEGORY_EXITS_CHILDREN=Sub-sample category exists, cannot be deleted
DEMO02_CATEGORY_PARENT_NOT_EXITS=Parent sample category does not exist
DEMO02_CATEGORY_PARENT_ERROR=Cannot set itself as parent sample category
DEMO02_CATEGORY_NAME_DUPLICATE=Sample category with the same name already exists
DEMO02_CATEGORY_PARENT_IS_CHILD=Cannot set own sub-sample category as parent sample category
DEMO03_STUDENT_NOT_EXISTS=Student does not exist
DEMO03_GRADE_NOT_EXISTS=Student class does not exist
DEMO03_GRADE_EXISTS=Student class already exists
AREA_NOT_EXISTS=Region does not exist
USER_NOT_EXISTS=User does not exist
USER_MOBILE_NOT_EXISTS=Mobile number is not registered
USER_MOBILE_USED=Mobile number has been used
USER_ACCOUNT_USED=Account has been used
USER_EMAIL_USED=Email has been used
USER_POINT_NOT_ENOUGH=User points balance is insufficient
USER_OLD_PASSWORD_NOT_MATCH=Old password error
USER_BALANCE_ERROR=User balance error
USER_CONFIG_NOT_SUPPORTED=User configuration items not supported
AUTH_LOGIN_BAD_CREDENTIALS=Login failed, account password is incorrect
AUTH_LOGIN_USER_DISABLED=Login failed, account is disabled
AUTH_ACCOUNT_FORMAT_ERROR=Authentication account format error
TAG_NOT_EXISTS=Tag does not exist
TAG_NAME_EXISTS=Tag already exists
TAG_HAS_USER=User exists under user tag, cannot be deleted
POINT_RECORD_BIZ_NOT_SUPPORT=User points record business type not supported
SIGN_IN_CONFIG_NOT_EXISTS=Sign-in rule does not exist
SIGN_IN_CONFIG_EXISTS=Sign-in days rule already exists
SIGN_IN_RECORD_TODAY_EXISTS=Signed in today, please do not sign in again
LEVEL_NOT_EXISTS=User level does not exist
LEVEL_NAME_EXISTS=User level name has been used
LEVEL_VALUE_EXISTS=User level value has been used
LEVEL_EXPERIENCE_MIN=Upgrade experience must be greater than the upgrade experience set for the previous level
LEVEL_EXPERIENCE_MAX=Upgrade experience must be less than the upgrade experience set for the next level
LEVEL_HAS_USER=Users exist under user level, cannot be deleted
EXPERIENCE_BIZ_NOT_SUPPORT=User experience business type not supported
GROUP_NOT_EXISTS=User group does not exist
GROUP_HAS_USER=Users exist in user group, cannot be deleted
USER_WITHDRAW_NOT_EXISTS=Member Withdrawal does not exist
USER_BALANCE_NOT_ENOUGH=Insufficient balance
USER_WITHDRAW_HAS_HANDLE=Withdrawal has been processed
USER_WITHDRAW_LESS_MIN_AMOUNT=Withdrawal must be greater than [{}]
USER_WITHDRAW_LESS_MAX_AMOUNT=Withdrawal must be less than [{}]
USER_WITHDRAW_LESS_MAX_PROCESS=You have an order in the process of withdrawing
USER_FROZEN_BALANCE_NOT_ENOUGH=Insufficient balance
USER_ASSETS_SPOT_NOT_EXISTS=User assets do not exist
USER_FAVORITE_TRADE_PAIR_EXISTS=Favorite trading pair exists
USER_FAVORITE_TRADE_PAIR_NOT_EXISTS=Favorite trading pair does not exist
USER_RECHARGE_NOT_EXISTS=Recharge record does not exist
USER_RECHARGE_HAS_HANDLE=Recharge record has been processed
USER_WALLET_NOT_EXISTS=Member wallet does not exist
USER_SPOT_ORDER_NOT_EXISTS=Member spot order does not exist
USER_TRANSACTIONS_NOT_EXISTS=Member bill does not exist
USER_MARGIN_ORDER_NOT_EXISTS=Member contract order does not exist
USER_MARGIN_CONFIG_NOT_EXISTS=Member contract configuration does not exist
USER_CERTIFICATION_NOT_EXISTS=Member authentication information does not exist
USER_CERTIFICATION_BEEN_HANDLE=Member authentication information has been processed
USER_CERTIFICATION_STATUS_SUCCESS=Member authentication information has been authenticated
USER_CERTIFICATION_STATUS_HANDLING=Member authentication information is under review
USER_CERTIFICATION_NOT_VERIFY=Identity [{}], operation not allowed
USER_CERTIFICATION_VERIFYING=During identity authentication, operation is not allowed
USER_CERTIFICATION_VERIFY_FAILURE=Identity authentication failed, operation not allowed
LEVEL_CONFIG_NOT_EXISTS=Member level configuration does not exist
LEVEL_CONFIG_DEFAULT_DELETED_FORBID=Member default level configuration cannot be deleted
LEVEL_CONFIG_NAME_EXISTS=Level name It says it already exists
USER_FUND_PASSWORD_NOT_EXISTS=You have not set a fund password and cannot modify it
USER_FUND_PASSWORD_ERROR=Wrong fund password
USER_FORBIDDEN_FUNC=This feature is disabled
FUNDS_RECORD_NOT_EXISTS=Fund record does not exist
USER_RECHARGE_LESS_MAX_PROCESS=You have [{}] orders being withdrawn
AUTH_LOGIN_CAPTCHA_CODE_ERROR=The verification code is incorrect, reason: {}
AUTH_THIRD_LOGIN_NOT_BIND=AUTH_THIRD_LOGIN_NOT_BIND
AUTH_TOKEN_EXPIRED=Token has expired
AUTH_MOBILE_NOT_EXISTS=Mobile number does not exist
MENU_NAME_DUPLICATE=A menu with this name already exists
MENU_PARENT_NOT_EXISTS=The parent menu does not exist
MENU_PARENT_ERROR=Cannot set itself as the parent menu
MENU_NOT_EXISTS=Menu does not exist
MENU_EXISTS_CHILDREN=Submenu exists and cannot be deleted
MENU_PARENT_NOT_DIR_OR_MENU=The parent menu must be a directory or menu
ROLE_NOT_EXISTS=Role does not exist
ROLE_NAME_DUPLICATE=A role named [{}] already exists
ROLE_CODE_DUPLICATE=A role coded [{}] already exists
ROLE_CAN_NOT_UPDATE_SYSTEM_TYPE_ROLE=Cannot operate a type that is built-in by the system Role
ROLE_IS_DISABLE=The role named [{}] has been disabled
ROLE_ADMIN_CODE_ERROR=The code [{}] cannot be used
USER_USERNAME_EXISTS=The account is unavailable
USER_MOBILE_EXISTS=The phone number already exists
USER_EMAIL_EXISTS=The email address already exists
USER_IMPORT_LIST_IS_EMPTY=The imported user data cannot be empty
USER_PASSWORD_FAILED=User password verification failed
USER_IS_DISABLE=The user named [{}] has been disabled
USER_COUNT_MAX=Failed to create a user, the reason is: exceeding the maximum tenant quota of the tenant [{}]
DEPT_NAME_DUPLICATE=A department with this name already exists
DEPT_PARENT_NOT_EXITS=The parent department does not exist
DEPT_NOT_FOUND=The current department does not exist
DEPT_EXITS_CHILDREN=Sub-departments exist and cannot be deleted
DEPT_PARENT_ERROR=Cannot set itself as the parent department
DEPT_EXISTS_USER=There are employees in the department and cannot be deleted
DEPT_NOT_ENABLE=Department [{}] is not in the open state and selection is not allowed
DEPT_PARENT_IS_CHILD=Cannot set your own sub-department as the parent department
POST_NOT_FOUND=The current position does not exist
POST_NOT_ENABLE=Position [{}] is not in the open state, selection is not allowed
POST_NAME_DUPLICATE=A position with this name already exists
POST_CODE_DUPLICATE=A position with this identifier already exists
DICT_TYPE_NOT_EXISTS=The current dictionary type does not exist
DICT_TYPE_NOT_ENABLE=The dictionary type is not in the open state, selection is not allowed
DICT_TYPE_NAME_DUPLICATE=A dictionary type with this name already exists
DICT_TYPE_TYPE_DUPLICATE=A dictionary type with this type already exists
DICT_TYPE_HAS_CHILDREN=Cannot be deleted, this dictionary type still has dictionary data
DICT_DATA_NOT_EXISTS=Current dictionary data does not exist
DICT_DATA_NOT_ENABLE=Dictionary data [{}] is not in the open state, selection is not allowed
DICT_DATA_VALUE_DUPLICATE=Dictionary data with this value already exists
NOTICE_NOT_FOUND=Current notification announcement does not exist
SMS_CHANNEL_NOT_EXISTS=SMS channel does not exist
SMS_CHANNEL_DISABLE=SMS channel is not in the open state, selection is not allowed
SMS_CHANNEL_HAS_CHILDREN=Cannot be deleted, this SMS channel still has SMS templates
SMS_TEMPLATE_NOT_EXISTS=SMS template does not exist Exists
SMS_TEMPLATE_CODE_DUPLICATE=The SMS template coded as [{}] already exists
SMS_TEMPLATE_API_ERROR=The SMS API template call failed, the reason is: {}
SMS_TEMPLATE_API_AUDIT_CHECKING=The SMS API template cannot be used, the reason is: under review
SMS_TEMPLATE_API_AUDIT_FAIL=The SMS API template cannot be used, the reason is: approval failed, {}
SMS_TEMPLATE_API_NOT_FOUND=The SMS API template cannot be used, the reason is: the template does not exist
SMS_SEND_MOBILE_TEMPLATE_PARAM_MISS=The template parameter [{}] is missing
SMS_CODE_NOT_FOUND=The verification code does not exist
SMS_CODE_EXPIRED=The verification code has expired
SMS_CODE_USED=The verification code has been used
SMS_CODE_NOT_CORRECT=The verification code is incorrect
SMS_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY=The number of SMS messages sent per day has exceeded
SMS_CODE_SEND_TOO_FAST=SMS messages are sent too frequently
TENANT_NOT_EXISTS=The tenant does not exist
TENANT_DISABLE=The tenant named [{}] has been disabled
TENANT_EXPIRE=The tenant named [{}] has expired
TENANT_CAN_NOT_UPDATE_SYSTEM=The system tenant cannot be modified, deleted, etc.!
TENANT_CODE_DUPLICATE=The tenant with tenant code [{}] already exists
TENANT_WEBSITE_DUPLICATE=The tenant with domain name [{}] already exists
TENANT_PACKAGE_NOT_EXISTS=The tenant package does not exist
TENANT_PACKAGE_USED=The tenant is using this package, please reset the package for the tenant before trying to delete
TENANT_PACKAGE_DISABLE=The tenant package named [{}] has been disabled
OAUTH2_CLIENT_NOT_EXISTS=The OAuth2 client does not exist
OAUTH2_CLIENT_EXISTS=The OAuth2 client ID already exists
OAUTH2_CLIENT_DISABLE=The OAuth2 client is disabled
OAUTH2_CLIENT_AUTHORIZED_GRANT_TYPE_NOT_EXISTS=The authorization type is not supported
OAUTH2_CLIENT_SCOPE_OVER=The authorization scope is too large
OAUTH2_CLIENT_REDIRECT_URI_NOT_MATCH=Invalid redirect_uri: [{}]
OAUTH2_CLIENT_CLIENT_SECRET_ERROR=Invalid client_secret: {}
OAUTH2_GRANT_CLIENT_ID_MISMATCH=The client_id does not match
OAUTH2_GRANT_REDIRECT_URI_MISMATCH=The redirect_uri does not match
OAUTH2_GRANT_STATE_MISMATCH=The state does not match
OAUTH2_GRANT_CODE_NOT_EXISTS=Code does not exist
OAUTH2_CODE_NOT_EXISTS=Code does not exist
OAUTH2_CODE_EXPIRE=Verification code expired
MAIL_ACCOUNT_NOT_EXISTS=Email account does not exist
MAIL_ACCOUNT_RELATE_TEMPLATE_EXISTS=Cannot be deleted, the email account still has email templates Board
MAIL_TEMPLATE_NOT_EXISTS=Mail template does not exist
MAIL_TEMPLATE_CODE_EXISTS=Mail template code[{}] already exists
MAIL_SEND_TEMPLATE_PARAM_MISS=Template parameter[{}] is missing
MAIL_SEND_MAIL_NOT_EXISTS=Email does not exist
MAIL_CODE_SEND_TOO_FAST=Email is sent too frequently
MAIL_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY=Exceeds the number of daily emails sent
MAIL_CODE_NOT_FOUND=Verification code does not exist
MAIL_CODE_EXPIRED=Verification code has expired
MAIL_CODE_USED=Verification code has been used
MAIL_CODE_NOT_CORRECT=Verification code is incorrect
MAIL_IS_EXISTS=Email has been used
NOTIFY_TEMPLATE_NOT_EXISTS=Internal message template does not exist
NOTIFY_TEMPLATE_CODE_DUPLICATE=Internal message template coded as [{}] already exists
NOTIFY_SEND_TEMPLATE_PARAM_MISS=Template parameter[{}] is missing
AGENT_NOT_EXISTS=Agency does not exist
AGENT_INVITE_CODE_EXISTS=Invitation code already exists
AGENT_HAS_DESCENDANT=There are subordinate agents under the agent and cannot be deleted
AGENT_ANCESTOR_NOT_AVAILABLE=Parent agent is unavailable
AUTH_NOT_EXISTS=Membership certification does not exist
CURRENCY_NOT_EXISTS=Currency does not exist
CURRENCYNOTRATE=There is no exchange rate for this currency
BANNER_NOT_EXISTS=BANNER does not exist
TENANT_SERVER_NAME_NOT_EXISTS=System tenant domain name does not exist
MessageConstants.TENANT_DICT_DATA_NOT_EXISTS=Tenant dictionary data does not exist
TIME_CONTRACT_AMOUNT_LESS=Purchase amount is less than the minimum amount
TIME_CONTRACT_RECORD_NOT_EXISTS=Time-limited contract transaction record does not exist
TRADE_DURATION_NOT_EXISTS=Order duration configuration does not exist
TRADE_MULTIPLE_NOT_EXISTS=Order multiple configuration does not exist
USER_ACCOUNT_NOT_EMPTY=Account cannot be empty
AGENT_HAS_NOT_ANCESTOR=The moved agent does not have any parent agent
USER_EMAIL_NOT_EXISTS=Email does not exist
Mail_CODE_SEND_FAIL=Email verification code failed to be sent
GOOGLE_SECRET_BINDING_EXPIRED=The bound Google key has expired, please get it again
GOOGLE_CODE_ERROR=Google verification code is incorrect
ACCOUNT_IS_ERROR=Login failed, account does not exist
GOOGLE_SECRET_IS_NOT_BINDING=Google key is not bound, please bind first
# Asset Type Internationalization
ASSET_TYPE_CRYPTO=Cryptocurrency
ASSET_TYPE_STOCKS=Stocks
ASSET_TYPE_COMMODITY=Commodities/Precious Metals
ASSET_TYPE_FOREX=Forex
ASSET_TYPE_FAVORITES=Favorites
# Agreement Management Internationalization
AGREEMENT_NOT_EXISTS=Agreement does not exist
AGREEMENT_TYPE_DUPLICATE=An agreement of the same type already exists under this tenant
AGREEMENT_TITLE_DUPLICATE=Agreement title already exists
# Agreement Type Internationalization
AGREEMENT_TYPE_PRIVACY_POLICY=Privacy Policy
AGREEMENT_TYPE_USER_GUIDELINES=User Guidelines
AGREEMENT_TYPE_TERMS_OF_SERVICE=Terms of Service
AGREEMENT_TYPE_DISCLAIMER=Disclaimer
AGREEMENT_TYPE_ABOUT_US=About Us
TRADE_CONTRACT_RECORD_NOT_EXISTS=Contract order does not exist
TRADE_CONTRACT_CONFIG_ERROR=Contract configuration error
ARG_ORDER_STATUS_IS_EMPTY=Parameter order status cannot be empty
ARG_LEVERAGE_IS_EMPTY=Parameter leverage cannot be empty
ARG_ORDER_TYPE_IS_EMPTY=Parameter order type cannot be empty
ARG_VALUE_ERROR=Parameter value error
TRADE_CONTRACT_ORDER_CANCEL_ALREADY=The contract order has been canceled
TRADE_CONTRACT_ORDER_FINISHED_ALREADY=The contract order has been completed
TRADE_CONTRACT_POSITION_CLOSE_ALREADY=The contract has been closed
TRADE_CONTRACT_CURRENCY_NOT_EXISTS=Order exchange rate error
IMAGE_URL_ERROR=Image link error
