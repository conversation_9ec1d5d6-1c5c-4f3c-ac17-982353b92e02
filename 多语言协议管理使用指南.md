# 多语言协议管理使用指南

## 🎯 **功能概述**

多语言协议管理系统支持为每个协议配置多种语言版本，语言数量不固定，可以根据业务需要动态添加或删除语言版本。

### 核心特性
- ✅ 支持无限制的语言种类
- ✅ 动态添加/删除语言版本
- ✅ 智能语言回退机制
- ✅ 富文本内容编辑
- ✅ 完整的权限控制
- ✅ 数据导入导出

## 🗄️ **数据库变更**

### 1. 执行SQL脚本
```sql
-- 1. 创建多语言内容表
source sql/mysql/system_agreement_content.sql;

-- 2. 修改现有协议表（移除title和content字段）
ALTER TABLE `system_agreement` 
DROP COLUMN `title`,
DROP COLUMN `content`;
```

### 2. 数据迁移（如果有现有数据）
```sql
-- 将现有协议数据迁移到多语言表
INSERT INTO `system_agreement_content` 
(`agreement_id`, `language_code`, `title`, `content`, `creator`)
SELECT 
    `id`, 
    'zh-CN', 
    `title`, 
    `content`, 
    'system'
FROM `system_agreement` 
WHERE `title` IS NOT NULL AND `content` IS NOT NULL;
```

## 🔧 **后端API接口**

### 管理后台API

#### 1. 创建多语言协议
```http
POST /admin-api/system/agreement-multilang/create
Content-Type: application/json

{
  "tenantId": 1,
  "type": 1,
  "version": "1.0",
  "status": 1,
  "contents": [
    {
      "languageCode": "zh-CN",
      "title": "隐私协议",
      "content": "<h1>隐私协议</h1><p>我们重视您的隐私保护...</p>"
    },
    {
      "languageCode": "en",
      "title": "Privacy Policy",
      "content": "<h1>Privacy Policy</h1><p>We value your privacy protection...</p>"
    },
    {
      "languageCode": "ja",
      "title": "プライバシーポリシー",
      "content": "<h1>プライバシーポリシー</h1><p>お客様のプライバシー保護を重視しています...</p>"
    }
  ]
}
```

#### 2. 获取协议多语言内容
```http
GET /admin-api/system/agreement-multilang/contents?agreementId=1
```

#### 3. 获取协议支持的语言列表
```http
GET /admin-api/system/agreement-multilang/languages?agreementId=1
```

### APP端API（自动多语言）

#### 1. 获取隐私协议（根据请求语言自动返回）
```http
GET /app-api/system/agreement/privacy-policy
Headers:
  lang: ja
  Accept-Language: ja
```

响应：
```json
{
  "code": 0,
  "data": {
    "id": 1,
    "type": 1,
    "title": "プライバシーポリシー",
    "content": "<h1>プライバシーポリシー</h1><p>お客様のプライバシー保護を重視しています...</p>",
    "version": "1.0"
  }
}
```

## 🎨 **前端管理界面**

### 1. 协议列表页面功能
- 显示协议基本信息和支持的语言列表
- 支持按协议类型、状态筛选
- 提供编辑、语言管理、删除操作

### 2. 协议编辑表单
- Tab切换编辑不同语言版本
- 富文本编辑器支持HTML内容
- 实时预览功能
- 表单验证和错误提示

### 3. 语言管理器
- 查看协议的所有语言版本
- 添加新的语言版本
- 编辑或删除现有语言版本
- 批量操作支持

## 🌍 **支持的语言代码**

### 常用语言代码
| 语言代码 | 语言名称 | 本地名称 |
|---------|---------|---------|
| zh-CN | 简体中文 | 简体中文 |
| zh-TW | 繁体中文 | 繁體中文 |
| en | 英语 | English |
| ja | 日语 | 日本語 |
| ko | 韩语 | 한국어 |
| th | 泰语 | ภาษาไทย |
| vi | 越南语 | Tiếng Việt |
| es | 西班牙语 | Español |
| fr | 法语 | Français |
| de | 德语 | Deutsch |
| it | 意大利语 | Italiano |
| pt | 葡萄牙语 | Português |
| ru | 俄语 | Русский |
| ar | 阿拉伯语 | العربية |

### 添加新语言
1. 在前端语言配置中添加新的语言选项
2. 在协议编辑界面选择新语言
3. 输入对应语言的标题和内容
4. 保存即可生效

## 🔄 **语言回退机制**

### 智能回退策略
1. **优先返回请求语言**：根据HTTP头中的`lang`或`Accept-Language`返回对应语言版本
2. **回退到默认语言**：如果请求语言不存在，自动回退到中文（zh-CN）
3. **返回任意可用语言**：如果中文也不存在，返回第一个可用的语言版本
4. **返回空结果**：如果协议没有任何语言版本，返回null

### 示例场景
```javascript
// 用户请求日语版本
GET /app-api/system/agreement/privacy-policy
Headers: { lang: 'ja' }

// 回退逻辑：
// 1. 查找日语版本 (ja) ✓ 存在 → 返回日语版本
// 2. 如果不存在 → 查找中文版本 (zh-CN)
// 3. 如果中文也不存在 → 返回第一个可用版本
// 4. 如果没有任何版本 → 返回 null
```

## 📱 **移动端适配**

### 1. 响应式设计
- 管理界面在移动设备上自动适配
- 表格转换为卡片布局
- 触摸友好的操作按钮

### 2. 性能优化
- 懒加载语言内容
- 图片压缩和CDN
- 本地缓存策略

## 🔐 **权限配置**

### 管理后台权限
```
system:agreement:create  - 创建协议
system:agreement:update  - 更新协议
system:agreement:delete  - 删除协议
system:agreement:query   - 查询协议
system:agreement:export  - 导出协议
```

### APP端权限
- 所有协议获取接口均为公开接口
- 无需登录即可访问
- 支持限流控制

## 🚀 **部署和维护**

### 1. 部署步骤
1. 执行数据库变更脚本
2. 部署后端代码
3. 部署前端管理界面
4. 配置权限和菜单
5. 导入初始数据

### 2. 数据备份
- 定期备份协议数据
- 支持Excel导入导出
- 版本控制和回滚

### 3. 监控和日志
- API调用监控
- 错误日志记录
- 性能指标统计

## 🎯 **最佳实践**

### 1. 内容管理
- 保持各语言版本内容的一致性
- 定期更新和维护协议内容
- 使用版本号管理协议变更

### 2. 语言策略
- 优先支持主要用户群体的语言
- 考虑地区法律法规要求
- 提供专业的翻译服务

### 3. 用户体验
- 确保协议内容清晰易懂
- 提供多种格式的协议文档
- 支持协议变更通知机制
