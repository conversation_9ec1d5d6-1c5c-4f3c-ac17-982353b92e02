import { defineConfig } from 'vite';
import uni from '@dcloudio/vite-plugin-uni';
import compressPlugin from 'vite-plugin-compression';
import { terser } from 'rollup-plugin-terser';

// 是否删除原始文件
const deleteOriginFile = false;
const version = new Date().toISOString().replace(/[-:.]/g, '');

export default defineConfig(({ command, mode }) => {
  const isBuild = command === 'build';
  return {
    plugins: [
      uni(),
      isBuild && compressPlugin({
        ext: '.gz',
        deleteOriginFile,
      }),
      isBuild && compressPlugin({
        ext: '.br',
        algorithm: 'brotliCompress',
        deleteOriginFile,
      }),
      {
        name: 'html-transform',
        transformIndexHtml(html) {
          return html.replace(/(href|src)="(.+?)"/g, (match, p1, p2) => {
            if (p2.startsWith('/assets/')) {
              return `${p1}="${p2}?v=${version}"`;
            }
            return match;
          });
        }
      }
    ],
    build: {
      rollupOptions: {
        output: {
          // 入口文件名
          manualChunks: {
            vue: ['vue'],
          },
        },
        plugins: [
          terser({
            compress: {
              drop_console: true,
            },
          }),
        ],
      },
    },
    css: {
      postcss: {
        plugins: [
          require('./postcss.plugin')() // 导入并调用您的自定义 PostCSS 插件
        ]
      }
    }
  }
});
