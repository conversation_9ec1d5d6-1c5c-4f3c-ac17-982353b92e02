// postcss.plugin.js
/**
 * 解决会去加载一些shadow阴影图片，导致页面很慢
 * @returns 
 */
module.exports = () => {
    return {
        postcssPlugin: 'modify-css', // 插件名称
        Once(root) {
            root.walkDecls(decl => {
                if (decl.prop === 'background-image' && decl.value.includes('https://cdn.dcloud.net.cn/img/shadow-blue.png')) {
                    decl.value = decl.value.replace(
                        'https://cdn.dcloud.net.cn/img/shadow-blue.png',
                        'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAAGBAMAAADwPukCAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAASUExURb7T9ezy/MbY9t/p+tHg+Pf6/gmQWsMAAAAUSURBVAjXY2BgUGBwYDBgEGAIAAAEXADx8btKYQAAAABJRU5ErkJggg=='
                    );
                }

                if (decl.prop === 'background-image' && decl.value.includes('https://cdn.dcloud.net.cn/img/shadow-green.png')) {
                    decl.value = decl.value.replace(
                        'https://cdn.dcloud.net.cn/img/shadow-green.png',
                        'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAAGBAMAAADwPukCAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAASUExURb71vuz87Mb2xt/639H40ff+98iMtmEAAAAUSURBVAjXY2BgUGBwYDBgEGAIAAAEXADx8btKYQAAAABJRU5ErkJggg=='
                    );
                }

                if (decl.prop === 'background-image' && decl.value.includes('https://cdn.dcloud.net.cn/img/shadow-grey.png')) {
                    decl.value = decl.value.replace(
                        'https://cdn.dcloud.net.cn/img/shadow-grey.png',
                        'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAAGBAMAAADwPukCAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAASUExURdnZ2fT09N7e3uzs7OTk5Pr6+vVa4lkAAAAUSURBVAjXY2BgUGBwYDBgEGAIAAAEXADx8btKYQAAAABJRU5ErkJggg=='
                    );
                }

                if (decl.prop === 'background-image' && decl.value.includes('https://cdn.dcloud.net.cn/img/shadow-orange.png')) {
                    decl.value = decl.value.replace(
                        'https://cdn.dcloud.net.cn/img/shadow-orange.png',
                        'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAAGBAMAAADwPukCAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAASUExURfXTvvzy7PbYxvrp3/jg0f7692in75UAAAAUSURBVAjXY2BgUGBwYDBgEGAIAAAEXADx8btKYQAAAABJRU5ErkJggg=='
                    );
                }

                if (decl.prop === 'background-image' && decl.value.includes('https://cdn.dcloud.net.cn/img/shadow-red.png')) {
                    decl.value = decl.value.replace(
                        'https://cdn.dcloud.net.cn/img/shadow-red.png',
                        'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAAGBAMAAADwPukCAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAASUExURfW+yvzs7/bG0Prf5PjR2v73+TToEXgAAAAUSURBVAjXY2BgUGBwYDBgEGAIAAAEXADx8btKYQAAAABJRU5ErkJggg=='
                    );
                }

                if (decl.prop === 'background-image' && decl.value.includes('https://cdn.dcloud.net.cn/img/shadow-yellow.png')) {
                    decl.value = decl.value.replace(
                        'https://cdn.dcloud.net.cn/img/shadow-yellow.png',
                        'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAAGBAMAAADwPukCAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAASUExURfXvvvz67Pbxxvr33/j00f7997nVB7sAAAAUSURBVAjXY2BgUGBwYDBgEGAIAAAEXADx8btKYQAAAABJRU5ErkJggg=='
                    );
                }
            });
        }
    };
};

module.exports.postcss = true;
