<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Expires" content="0">

  <!-- <link id="linkicon" rel="icon" href="./static/logo.png"> -->
  <script>
    var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') ||
      CSS.supports('top: constant(a)'))
    document.write(
      '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
      (coverSupport ? ', viewport-fit=cover' : '') + '" />')
    document.addEventListener('gesturestart', function (e) {
      e.preventDefault();
    });
    document.addEventListener('dblclick', function (e) {
      e.preventDefault();
    });
    document.addEventListener('contextmenu', function (e) {
      // e.preventDefault();
    });
    document.addEventListener("DOMContentLoaded", function() {
    function isPC() {
        // 使用用户代理检测是否为PC
        var userAgentInfo = navigator.userAgent;
        
        var mobileAgents = [
          "Android",       // Android设备
          "iPhone",        // iPhone
          "iPod",          // iPod
          "iPad",          // iPad
          "Symbian",       // Symbian OS
          "Windows Phone", // Windows Phone
          "BlackBerry",    // 黑莓设备
          "webOS",         // Palm webOS设备
          "Opera Mini",    // Opera Mini浏览器
          "Opera Mobi",    // Opera Mobile浏览器
          "IEMobile",      // IE Mobile
          "Mobile",        // 通用移动设备标识
          "Kindle",        // Kindle设备
          "Silk",          // Kindle Fire的浏览器
          "PlayBook",      // BlackBerry PlayBook
          "MeeGo",         // MeeGo OS
          "Zune",          // 微软 Zune
          "MIDP",          // Java ME MIDP设备
          "Fennec",        // Firefox Mobile
          "Bada",          // Samsung Bada
          "Tizen",         // Tizen OS
          "UCWEB",         // UC浏览器
          "Windows CE"     // Windows CE设备
      ];
        
        for (var i = 0; i < mobileAgents.length; i++) {
            if (userAgentInfo.indexOf(mobileAgents[i]) > 0) {
                return false; // 发现是移动设备，返回false
            }
        }
        return true; // 如果没有匹配到移动设备，返回true
    }

    if (isPC()) {
        document.documentElement.classList.add("pc");
    }
  });

  </script>
  <title></title>
  <!--preload-links-->
  <!--app-context-->
  <style>
    body::after {
      content: none;
    }

   /* PC设备样式 */
  .pc {
      max-width: 375px;
      margin: 0 auto;
      border-left: 1px solid #ebedf0;
      border-right: 1px solid #ebedf0;
  }

  .pc .uni-tabbar,
  .pc .uni-page-head,
  .pc .z-tabs-conatiner,
  .pc .table-td-class,
  .pc .zp-view-super {
      max-width: 375px;
      margin: 0 auto;
      width: 100%;
  }

  /* 手机和平板样式 - 没有 .pc 类的情况下 */
  html {
      margin: 0;
      padding: 0;
      width: 100%;
  }

  .uni-tabbar,
  .uni-page-head,
  .z-tabs-container,
  .table-td-class,
  .zp-view-super {
      width: 100%;
      margin: 0;
  }



    .loading {
      width: 100%;
      height: 100vh;
      background-color: #3248F4;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #FFF;
      text-align: center;
      font-family: 'Phosphate';
      font-size: 40px;
      font-style: normal;
      font-weight: 400;
      line-height: 140%;
      /* 56px */
    }

    .letter {
      opacity: 0;
      animation: fadeInOut 1s linear infinite;
    }

    @keyframes fadeInOut {

      0%,
      100% {
        opacity: 0;
      }

      50% {
        opacity: 1;
      }
    }

    @font-face {
      font-family: 'Phosphate';
      src: url('./src/static/fonts/PhosphateInline.ttf');
      font-weight: normal;
      font-style: normal;
    }
  </style>
</head>

<body>
  <div id="app">
    <div class="loading" id="loading">
      <!-- Letters will be dynamically inserted here -->
    </div>
  </div>
  <script type="module" src="/src/main.js"></script>
</body>

<script>
  const word = 'LOADING';  // 这里可以改成你想要的任何单词
  const loadingElement = document.getElementById('loading');

  function displayWord(word) {
    loadingElement.innerHTML = '';
    for (let i = 0; i < word.length; i++) {
      const span = document.createElement('span');
      span.className = 'letter';
      span.style.animationDelay = `${i * 0.1}s`;
      span.textContent = word[i];
      loadingElement.appendChild(span);
    }
  }

  // Initial display
  displayWord(word);

</script>

</html>